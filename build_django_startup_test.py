#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
构建Django启动测试程序
"""

import os
import subprocess

def create_django_startup_test_spec():
    """创建Django启动测试的PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['test_django_startup.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('SchoolAcademicManager', 'SchoolAcademicManager'),
        ('django_utils.py', '.'),
    ],
    hiddenimports=[
        'django',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.core.management',
        'django.core.management.commands',
        'django.core.management.commands.runserver',
        'django.apps',
        'django.apps.registry',
        'django.conf',
        'django.urls',
        'django.utils.log',
        'import_export',
        'import_export.admin',
        'import_export.resources',
        'import_export.fields',
        'import_export.widgets',
        'import_export.formats',
        'import_export.formats.base_formats',
        'colorfield',
        'colorfield.fields',
        'openpyxl',
        'xlwt',
        'xlrd',
        'academic',
        'academic.models',
        'academic.views',
        'academic.admin',
        'academic.apps',
        'academic.urls',
        'SchoolAcademicManager',
        'SchoolAcademicManager.settings',
        'SchoolAcademicManager.urls',
        'SchoolAcademicManager.wsgi',
        'django_utils',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Django启动测试',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
'''
    
    with open('django_startup_test.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建Django启动测试PyInstaller规格文件")

def build_django_startup_test():
    """构建Django启动测试可执行文件"""
    print("\n🚀 开始构建Django启动测试程序...")
    
    try:
        result = subprocess.run([
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'django_startup_test.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print(f"📁 可执行文件位置: dist/Django启动测试.exe")
            return True
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 Django启动测试程序打包工具")
    print("=" * 60)
    
    create_django_startup_test_spec()
    
    if build_django_startup_test():
        print("\n🎉 打包完成！")
        print("📋 特点:")
        print("- 直接测试Django服务器启动")
        print("- 显示详细的启动过程")
        print("- 捕获所有Django输出")
        print("- 控制台窗口显示结果")
        print("\n📝 使用方法:")
        print("运行 dist/Django启动测试.exe 进行Django启动测试")
    else:
        print("\n❌ 打包失败")

if __name__ == '__main__':
    main()
