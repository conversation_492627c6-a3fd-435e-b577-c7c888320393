#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重置为试用期状态
清除许可证数据，让程序回到60天试用期
"""

import os
import winreg
from academic_trial_manager import AcademicTrialManager

def reset_to_trial():
    """重置为试用期状态"""
    print("🔄 正在重置为试用期状态...")
    
    tm = AcademicTrialManager()
    
    # 删除许可证文件
    if os.path.exists(tm.license_file):
        try:
            os.remove(tm.license_file)
            print("✅ 已删除许可证文件")
        except Exception as e:
            print(f"❌ 删除许可证文件失败: {e}")
    
    # 清除注册表中的许可证信息
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, tm.registry_key, 0, winreg.KEY_SET_VALUE) as key:
            try:
                winreg.DeleteValue(key, 'license_data')
                print("✅ 已清除注册表许可证数据")
            except:
                pass
            try:
                winreg.DeleteValue(key, 'license_status')
                print("✅ 已清除注册表许可证状态")
            except:
                pass
    except Exception as e:
        print(f"⚠️ 清除注册表失败: {e}")
    
    # 检查当前状态
    print("\n📊 当前状态:")
    trial_status = tm.check_trial_status()
    print(f"试用期状态: {trial_status['message']}")
    print(f"剩余天数: {trial_status['days_remaining']}")
    print(f"试用期: {trial_status['start_date']} 至 {trial_status['end_date']}")
    
    print("\n✅ 重置完成！重新启动GUI程序即可看到试用期状态。")

if __name__ == '__main__':
    reset_to_trial()
