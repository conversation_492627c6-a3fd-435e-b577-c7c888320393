#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试版打包脚本
"""

import subprocess

def create_debug_spec():
    """创建调试版PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['debug_academic_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('SchoolAcademicManager', 'SchoolAcademicManager'),
        ('django_utils.py', '.'),
    ],
    hiddenimports=[
        'django',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.core.management',
        'django.core.management.commands',
        'django.core.management.commands.runserver',
        'django.apps',
        'django.apps.registry',
        'django.conf',
        'django.urls',
        'django.utils.log',
        'import_export',
        'import_export.admin',
        'import_export.resources',
        'import_export.fields',
        'import_export.widgets',
        'import_export.formats',
        'import_export.formats.base_formats',
        'colorfield',
        'colorfield.fields',
        'openpyxl',
        'xlwt',
        'xlrd',
        'academic',
        'academic.models',
        'academic.views',
        'academic.admin',
        'academic.apps',
        'academic.urls',
        'SchoolAcademicManager',
        'SchoolAcademicManager.settings',
        'SchoolAcademicManager.urls',
        'SchoolAcademicManager.wsgi',
        'django_utils',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='中学教务管理系统_调试版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 显示控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
'''
    
    with open('debug_academic.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建调试版PyInstaller规格文件")

def build_debug_exe():
    """构建调试版可执行文件"""
    print("\n🚀 开始构建调试版可执行文件...")
    
    try:
        result = subprocess.run([
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'debug_academic.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print(f"📁 可执行文件位置: dist/中学教务管理系统_调试版.exe")
            return True
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🐛 调试版中学教务管理系统打包工具")
    print("=" * 60)
    
    create_debug_spec()
    
    if build_debug_exe():
        print("\n🎉 调试版打包完成！")
        print("📋 特点:")
        print("- 显示控制台窗口用于调试")
        print("- 详细的日志输出")
        print("- 实时Django服务器输出")
        print("- 环境检查功能")
    else:
        print("\n❌ 打包失败")

if __name__ == '__main__':
    main()
