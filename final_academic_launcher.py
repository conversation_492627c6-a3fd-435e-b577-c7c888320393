#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中学教务管理系统启动器 - 最终修复版
解决了Django在打包环境中的启动问题
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import webbrowser
import time
import socket
from trial_manager import TrialManager

# 导入单机授权系统
try:
    from single_machine_license import SingleMachineLicense
    from activation_dialog import show_activation_dialog
except ImportError:
    SingleMachineLicense = None
    show_activation_dialog = None



def get_resource_path(relative_path):
    """获取资源文件路径，兼容开发环境和打包后的环境"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

class FinalAcademicLauncher:
    def __init__(self):
        self.trial_manager = TrialManager(trial_days=60)
        self.license_system = SingleMachineLicense() if SingleMachineLicense else None
        self.server_thread = None
        self.server_running = False
        self.root = None
        
    def create_gui(self):
        """创建图形界面"""
        self.root = tk.Tk()
        self.root.title("中学教务管理系统 v1.0")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="中学教务管理系统", 
                               font=("Microsoft YaHei", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 版本信息
        version_label = ttk.Label(main_frame, text="版本 1.0 - 专业版", 
                                 font=("Microsoft YaHei", 10))
        version_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # 试用期信息框架
        trial_frame = ttk.LabelFrame(main_frame, text="授权信息", padding="10")
        trial_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 试用期状态
        self.status_label = ttk.Label(trial_frame, text="检查中...", 
                                     font=("Microsoft YaHei", 10))
        self.status_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.StringVar()
        self.progress_label = ttk.Label(trial_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # 软件激活框架
        activation_frame = ttk.LabelFrame(main_frame, text="软件激活", padding="10")
        activation_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        # 激活说明
        activation_info = ttk.Label(activation_frame,
                                   text="本软件采用单机授权模式，需要激活后才能使用",
                                   font=("Microsoft YaHei", 9))
        activation_info.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 激活按钮
        self.activate_btn = ttk.Button(activation_frame, text="激活软件",
                                      command=self.activate_license)
        self.activate_btn.grid(row=1, column=0, padx=(0, 10))

        # 查看机器信息按钮
        machine_info_btn = ttk.Button(activation_frame, text="查看机器信息",
                                     command=self.show_machine_info)
        machine_info_btn.grid(row=1, column=1)
        
        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(0, 20))
        
        self.start_btn = ttk.Button(button_frame, text="启动系统", 
                                   command=self.start_system, state="disabled")
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(button_frame, text="停止系统", 
                                  command=self.stop_system, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.open_btn = ttk.Button(button_frame, text="打开系统", 
                                  command=self.open_system, state="disabled")
        self.open_btn.grid(row=0, column=2, padx=(0, 10))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 关于按钮
        about_btn = ttk.Button(main_frame, text="关于", command=self.show_about)
        about_btn.grid(row=6, column=1, sticky=tk.E, pady=(10, 0))
        
        # 检查试用期状态
        self.check_trial_status()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        return self.root
    
    def check_trial_status(self):
        """检查试用期状态"""
        # 首先检查单机许可证
        if self.license_system:
            valid, message = self.license_system.validate_license()
            if valid:
                license_info = self.license_system.get_license_info()
                if license_info:
                    self.status_label.config(text=f"软件已激活 (剩余{license_info['days_left']}天)", foreground="green")
                    self.progress_var.set(f"许可证类型: {license_info['license_type']} | 激活时间: {license_info['first_activation']}")
                    self.start_btn.config(state="normal")
                    self.activate_btn.config(text="重新激活", state="normal")
                    return
            else:
                # 许可证无效，检查试用期
                if "未找到许可证文件" in message:
                    # 没有许可证文件，检查试用期
                    pass
                else:
                    # 许可证文件存在但无效
                    self.status_label.config(text="许可证无效", foreground="red")
                    self.progress_var.set(f"错误: {message}")
                    self.start_btn.config(state="disabled")
                    self.activate_btn.config(text="激活软件", state="normal")
                    return

        # 如果没有有效的机器许可证，检查试用期
        if self.trial_manager.is_licensed():
            self.status_label.config(text="软件已激活", foreground="green")
            self.progress_var.set("感谢您的支持！")
            self.start_btn.config(state="normal")
            self.activate_btn.config(state="disabled")
        elif self.trial_manager.can_run():
            status = self.trial_manager.check_trial_status()
            days_remaining = status['days_remaining']
            total_days = status['total_days']
            days_used = total_days - days_remaining
            self.status_label.config(text=f"试用期 (剩余{days_remaining}天)", foreground="orange")
            self.progress_var.set(f"试用期: {days_used}/{total_days} 天已使用")
            self.start_btn.config(state="normal")
            self.activate_btn.config(text="激活软件", state="normal")
        else:
            self.status_label.config(text="试用期已过期", foreground="red")
            self.progress_var.set("请购买正式版本或输入许可证密钥")
            self.start_btn.config(state="disabled")
            self.activate_btn.config(text="激活软件", state="normal")
    
    def activate_license(self):
        """激活许可证"""
        if self.license_system and show_activation_dialog:
            # 使用单机授权系统的激活对话框
            success = show_activation_dialog(self.root)
            if success:
                self.check_trial_status()
        else:
            # 备用方法：显示简单的输入对话框
            from tkinter import simpledialog
            license_key = simpledialog.askstring("激活软件",
                                                "请输入许可证密钥:",
                                                parent=self.root)
            if not license_key:
                return

            self.status_var.set("正在验证许可证...")
            self.activate_btn.config(state="disabled")

            # 在后台线程中验证许可证
            def verify_license():
                if self.trial_manager.activate_license(license_key.strip().upper()):
                    self.root.after(0, lambda: self.on_license_activated(True))
                else:
                    self.root.after(0, lambda: self.on_license_activated(False))

            threading.Thread(target=verify_license, daemon=True).start()
    
    def on_license_activated(self, success):
        """许可证激活回调"""
        if success:
            messagebox.showinfo("激活成功", "许可证激活成功！感谢您的支持。")
            self.check_trial_status()
        else:
            messagebox.showerror("激活失败", "许可证密钥无效，请检查后重试。")
            self.activate_btn.config(state="normal")
        
        self.status_var.set("就绪")

    def show_machine_info(self):
        """显示机器信息"""
        if self.license_system:
            machine_id, fingerprint = self.license_system.get_machine_fingerprint()

            info_text = f"""当前计算机信息

机器标识:
{machine_id}

硬件指纹:
"""
            for i, fp in enumerate(fingerprint, 1):
                info_text += f"{i:2d}. {fp}\n"

            info_text += f"""
说明:
• 机器标识是根据硬件信息生成的唯一ID
• 购买许可证时需要提供此机器标识
• 更换主要硬件后机器标识会改变
• 请将机器标识发送给软件提供商获取许可证"""

            # 创建信息窗口
            info_window = tk.Toplevel(self.root)
            info_window.title("机器信息")
            info_window.geometry("600x500")
            info_window.resizable(False, False)

            # 居中显示
            info_window.update_idletasks()
            x = (info_window.winfo_screenwidth() // 2) - (600 // 2)
            y = (info_window.winfo_screenheight() // 2) - (500 // 2)
            info_window.geometry(f"600x500+{x}+{y}")

            info_frame = ttk.Frame(info_window, padding="20")
            info_frame.pack(fill=tk.BOTH, expand=True)

            # 信息显示
            from tkinter import scrolledtext
            info_text_widget = scrolledtext.ScrolledText(info_frame, wrap=tk.WORD,
                                                        font=("Courier", 9))
            info_text_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            info_text_widget.insert(tk.END, info_text)
            info_text_widget.config(state=tk.DISABLED)

            # 按钮框架
            button_frame = ttk.Frame(info_frame)
            button_frame.pack(fill=tk.X)

            # 复制机器ID按钮
            def copy_machine_id():
                self.root.clipboard_clear()
                self.root.clipboard_append(machine_id)
                messagebox.showinfo("成功", "机器标识已复制到剪贴板")

            ttk.Button(button_frame, text="复制机器标识",
                      command=copy_machine_id).pack(side=tk.LEFT, padx=(0, 10))

            ttk.Button(button_frame, text="关闭",
                      command=info_window.destroy).pack(side=tk.RIGHT)
        else:
            messagebox.showinfo("机器信息", "机器信息功能不可用")


    
    def start_django_server(self):
        """在独立线程中启动Django服务器"""
        try:
            # 获取Django项目目录
            academic_dir = get_resource_path("SchoolAcademicManager")
            
            if not os.path.exists(academic_dir):
                raise Exception("找不到SchoolAcademicManager目录")
            
            # 使用安全的Django初始化
            try:
                from django_utils import safe_django_setup
                if not safe_django_setup():
                    raise Exception("Django初始化失败")
            except ImportError:
                # 如果django_utils不可用，使用传统方法
                os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')

                if academic_dir not in sys.path:
                    sys.path.insert(0, academic_dir)

                import django
                from django.conf import settings

                if not settings.configured:
                    django.setup()

            # 导入Django组件
            from django.core.management import execute_from_command_line
            from django.core.wsgi import get_wsgi_application
            
            # 使用更简单可靠的方法启动Django服务器
            import subprocess
            try:
                # 获取正确的Python解释器路径
                if hasattr(sys, '_MEIPASS'):
                    # 打包环境，尝试多种方法找到Python
                    import shutil
                    python_exe = shutil.which("python") or shutil.which("python.exe")
                    if not python_exe:
                        # 如果找不到系统Python，尝试使用打包的Python
                        python_exe = sys.executable
                else:
                    # 开发环境，使用当前Python
                    python_exe = sys.executable

                # 启动Django服务器进程
                self.server_process = subprocess.Popen([
                    python_exe, "manage.py", "runserver", "127.0.0.1:8001", "--noreload"
                ], cwd=academic_dir,
                   stdout=subprocess.DEVNULL,
                   stderr=subprocess.DEVNULL,
                   creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)

                # 等待服务器启动并验证
                import time
                max_wait_time = 15  # 最多等待15秒
                wait_interval = 0.5  # 每0.5秒检查一次

                for i in range(int(max_wait_time / wait_interval)):
                    time.sleep(wait_interval)

                    # 检查进程是否还在运行
                    if self.server_process.poll() is not None:
                        # 进程已退出
                        error_msg = f"Django进程退出，返回码: {self.server_process.returncode}"
                        raise Exception(error_msg)

                    # 检查端口是否开始监听
                    if check_port(8001):
                        self.server_running = True
                        self.root.after(0, self.on_server_started)
                        return

                # 如果等待超时
                if self.server_process.poll() is None:
                    # 进程还在运行但端口没响应
                    raise Exception("Django服务器启动超时，端口8001未响应")
                else:
                    raise Exception("Django服务器启动失败，进程已退出")

            except Exception as e:
                # 服务器启动失败
                raise Exception(f"服务器启动失败: {e}")
            
        except Exception as e:
            self.server_running = False
            error_msg = str(e)
            self.root.after(0, lambda: self.on_server_error(error_msg))
    
    def start_system(self):
        """启动系统"""
        # 首先检查单机许可证
        if self.license_system:
            valid, message = self.license_system.validate_license()
            if not valid:
                # 没有正式许可证，检查试用期
                if not self.trial_manager.can_run():
                    messagebox.showerror("无法启动", "试用期已过期或许可证无效，请激活软件")
                    return
        else:
            # 备用：检查试用期
            if not self.trial_manager.can_run():
                messagebox.showerror("无法启动", "试用期已过期或许可证无效")
                return
        
        if check_port(8001):
            messagebox.showwarning("端口占用", "端口8001已被占用，请先停止占用该端口的程序")
            return
        
        self.status_var.set("正在启动系统...")
        self.start_btn.config(state="disabled")
        
        # 在后台线程中启动Django服务器
        self.server_thread = threading.Thread(target=self.start_django_server, daemon=True)
        self.server_thread.start()
    
    def on_server_started(self):
        """服务器启动成功回调"""
        self.status_var.set("系统已启动 - 运行在 http://127.0.0.1:8001")
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.open_btn.config(state="normal")
        
        # 自动打开浏览器
        webbrowser.open("http://127.0.0.1:8001")
    
    def on_server_error(self, error):
        """服务器启动失败回调"""
        self.status_var.set("启动失败")
        self.start_btn.config(state="normal")

        # 显示详细的错误信息
        error_details = f"""系统启动失败：

错误信息：
{error}

可能的解决方案：
1. 检查端口8001是否被其他程序占用
2. 确保Django项目文件完整
3. 重新启动程序
4. 联系技术支持

如果问题持续存在，请保存此错误信息并联系技术支持。"""

        messagebox.showerror("启动失败", error_details)
    
    def stop_system(self):
        """停止系统"""
        self.server_running = False

        # 如果有服务器进程，先尝试正常终止
        if hasattr(self, 'server_process') and self.server_process:
            try:
                self.server_process.terminate()
                # 等待进程结束
                import time
                time.sleep(2)
                if self.server_process.poll() is None:
                    # 如果还没结束，强制杀死
                    self.server_process.kill()
                self.server_process = None
            except:
                pass

        # 备用方法：通过端口终止进程
        try:
            import subprocess
            subprocess.run(['taskkill', '/f', '/fi', 'PID eq 8001'],
                         capture_output=True, timeout=5)
        except:
            pass

        self.status_var.set("系统已停止")
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.open_btn.config(state="disabled")
    
    def open_system(self):
        """打开系统页面"""
        if check_port(8001):
            webbrowser.open("http://127.0.0.1:8001")
        else:
            messagebox.showwarning("系统未运行", "系统未运行，请先启动系统")
    
    def show_about(self):
        """显示关于对话框"""
        about_text = """中学教务管理系统 v1.0

功能特色：
• 学生信息管理
• 教师班级管理  
• 课程安排管理
• 成绩考勤管理

试用期：60天
技术支持：<EMAIL>

© 2024 学校管理系统开发团队"""
        
        messagebox.showinfo("关于", about_text)
    
    def on_closing(self):
        """关闭程序"""
        self.server_running = False
        self.root.destroy()
    
    def run(self):
        """运行启动器"""
        root = self.create_gui()
        root.mainloop()

def main():
    """主函数"""
    launcher = FinalAcademicLauncher()
    launcher.run()

if __name__ == '__main__':
    main()
