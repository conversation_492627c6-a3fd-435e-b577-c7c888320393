#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Django工具函数 - 解决Django初始化和打包相关问题
"""

import os
import sys
import threading

# 全局锁，防止多线程同时初始化Django
_django_setup_lock = threading.Lock()
_django_initialized = False


def get_resource_path(relative_path):
    """获取资源文件路径，兼容开发环境和打包环境"""
    try:
        # PyInstaller打包后的临时目录
        base_path = sys._MEIPASS
    except Exception:
        # 开发环境
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)


def safe_django_setup(project_name='SchoolAcademicManager'):
    """
    安全的Django初始化，防止重复初始化
    
    Args:
        project_name: Django项目名称
        
    Returns:
        bool: 初始化是否成功
    """
    global _django_initialized
    
    with _django_setup_lock:
        if _django_initialized:
            return True
            
        try:
            # 设置Django项目路径
            project_dir = get_resource_path(project_name)
            if project_dir not in sys.path:
                sys.path.insert(0, project_dir)
            
            # 设置Django环境变量
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', f'{project_name}.settings')
            
            # 导入Django
            import django
            from django.conf import settings
            
            # 检查Django是否已经配置
            if not settings.configured:
                django.setup()
                _django_initialized = True
                return True
            else:
                _django_initialized = True
                return True
                
        except Exception as e:
            print(f"Django初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def check_django_dependencies():
    """
    检查Django相关依赖是否正确安装

    Returns:
        dict: 检查结果
    """
    results = {}

    # 检查Django
    try:
        import django
        results['django'] = {
            'status': 'OK',
            'version': django.get_version(),
            'message': f'Django {django.get_version()} 已安装'
        }
    except ImportError as e:
        results['django'] = {
            'status': 'ERROR',
            'version': None,
            'message': f'Django导入失败: {e}'
        }

    # 检查import_export（基础模块）
    try:
        import import_export
        results['import_export'] = {
            'status': 'OK',
            'version': import_export.__version__,
            'message': f'django-import-export {import_export.__version__} 已安装'
        }
    except ImportError as e:
        results['import_export'] = {
            'status': 'ERROR',
            'version': None,
            'message': f'django-import-export导入失败: {e}'
        }

    # 检查import_export.admin（需要Django配置）
    try:
        # 先确保Django已配置
        if not safe_django_setup():
            raise ImportError("Django配置失败")

        from import_export.admin import ImportExportModelAdmin
        results['import_export_admin'] = {
            'status': 'OK',
            'version': None,
            'message': 'import_export.admin 导入成功'
        }
    except Exception as e:
        results['import_export_admin'] = {
            'status': 'ERROR',
            'version': None,
            'message': f'import_export.admin导入失败: {e}'
        }
    
    # 检查colorfield（基础模块）
    try:
        import colorfield
        results['colorfield'] = {
            'status': 'OK',
            'version': None,
            'message': 'django-colorfield 已安装'
        }
    except ImportError as e:
        results['colorfield'] = {
            'status': 'ERROR',
            'version': None,
            'message': f'django-colorfield导入失败: {e}'
        }
    
    # 检查Excel相关库
    excel_libs = ['openpyxl', 'xlwt', 'xlrd']
    for lib in excel_libs:
        try:
            module = __import__(lib)
            version = getattr(module, '__version__', 'Unknown')
            results[lib] = {
                'status': 'OK',
                'version': version,
                'message': f'{lib} {version} 已安装'
            }
        except ImportError as e:
            results[lib] = {
                'status': 'ERROR',
                'version': None,
                'message': f'{lib}导入失败: {e}'
            }
    
    return results


def print_dependency_report():
    """打印依赖检查报告"""
    print("\n=== Django依赖检查报告 ===")
    results = check_django_dependencies()
    
    for name, info in results.items():
        status = info['status']
        message = info['message']
        
        if status == 'OK':
            print(f"[OK] {message}")
        else:
            print(f"[ERROR] {message}")
    
    # 统计
    total = len(results)
    success = sum(1 for r in results.values() if r['status'] == 'OK')
    
    print(f"\n检查完成: {success}/{total} 个依赖正常")
    
    if success == total:
        print("[OK] 所有依赖检查通过")
        return True
    else:
        print("[ERROR] 部分依赖缺失或有问题")
        return False


def test_django_import_export():
    """测试Django和import_export功能"""
    print("\n=== 测试Django和import_export功能 ===")
    
    # 初始化Django
    if not safe_django_setup():
        print("[ERROR] Django初始化失败")
        return False
    
    try:
        # 测试Django配置
        from django.conf import settings
        print(f"[OK] Django配置成功")
        print(f"数据库引擎: {settings.DATABASES['default']['ENGINE']}")
        
        # 测试应用导入
        from academic import models, admin
        print("[OK] academic应用导入成功")
        
        # 测试import_export功能
        from import_export.admin import ImportExportModelAdmin
        print("[OK] ImportExportModelAdmin导入成功")
        
        # 测试模型管理器
        from academic.admin import DepartmentAdmin
        print("[OK] DepartmentAdmin导入成功")
        
        print("[OK] Django和import_export功能测试通过")
        return True
        
    except Exception as e:
        print(f"[ERROR] 功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    """独立运行时进行完整测试"""
    print("=" * 60)
    print("Django工具测试")
    print("=" * 60)
    
    # 依赖检查
    deps_ok = print_dependency_report()
    
    # 功能测试
    if deps_ok:
        func_ok = test_django_import_export()
    else:
        func_ok = False
    
    print("\n" + "=" * 60)
    if deps_ok and func_ok:
        print("[OK] 所有测试通过！Django应该可以正常运行")
    else:
        print("[ERROR] 测试失败，需要解决问题")
    print("=" * 60)
