@echo off
chcp 65001
echo ========================================
echo    学校管理系统状态检查
echo ========================================
echo.

echo 检查系统运行状态...
echo.

REM 检查端口占用情况
echo 端口占用情况：
echo ┌─────────────────────────────────────┐

REM 检查8000端口（图书管理系统）
netstat -an | findstr ":8000" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo │ 端口 8000 [图书管理系统]: ❌ 未运行  │
) else (
    echo │ 端口 8000 [图书管理系统]: ✅ 运行中  │
)

REM 检查8001端口（教务管理系统）
netstat -an | findstr ":8001" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo │ 端口 8001 [教务管理系统]: ❌ 未运行  │
) else (
    echo │ 端口 8001 [教务管理系统]: ✅ 运行中  │
)

REM 检查8002端口（财务管理系统）
netstat -an | findstr ":8002" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo │ 端口 8002 [财务管理系统]: ❌ 未运行  │
) else (
    echo │ 端口 8002 [财务管理系统]: ✅ 运行中  │
)

echo └─────────────────────────────────────┘
echo.

REM 检查Python进程
echo Python进程检查：
tasklist | findstr python.exe >nul
if errorlevel 1 (
    echo ❌ 未发现Python进程
) else (
    echo ✅ 发现Python进程
    echo.
    echo 当前Python进程：
    tasklist | findstr python.exe
)

echo.
echo ========================================
echo 系统访问地址：
echo.
echo 🌐 系统导航页面: index.html
echo 📚 图书管理系统: http://127.0.0.1:8000
echo 🎓 教务管理系统: http://127.0.0.1:8001  
echo 💰 财务管理系统: http://127.0.0.1:8002
echo.
echo 管理后台地址：
echo 📚 图书管理: http://127.0.0.1:8000/admin
echo 🎓 教务管理: http://127.0.0.1:8001/admin
echo 💰 财务管理: http://127.0.0.1:8002/admin
echo.
echo 默认管理员: admin / admin123
echo ========================================
echo.

echo 按任意键退出...
pause >nul
