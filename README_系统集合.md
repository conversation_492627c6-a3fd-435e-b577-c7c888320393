# 学校管理系统集合

一个完整的学校管理解决方案，包含三个独立的管理系统，满足学校的不同管理需求。

## 🏫 系统概览

### 📚 图书管理系统 (BookManager)
- **端口**: 8000
- **功能**: 图书信息管理、借阅归还、读者管理、统计分析
- **特色**: 专业图书馆管理、多语言支持、现代化界面
- **说明**: 纯粹的图书管理系统，专注于图书馆核心业务

### 🎓 中学教务管理系统 (SchoolAcademicManager)
- **端口**: 8001
- **功能**: 学生管理、教师管理、课程安排、成绩统计、班级管理
- **特色**: 专为中学设计、家长信息管理、住校管理、教研组管理

### 💰 学校财务管理系统 (SchoolFinanceManager)
- **端口**: 8002
- **功能**: 收支管理、预算制定、财务报表、资金分析
- **特色**: 独立财务系统、多维度统计、预算执行监控、财务数据可视化
- **说明**: 专业的学校财务管理系统，与其他系统完全独立

## 🚀 快速开始

### 方式一：智能启动器（推荐）

**选择启动方式：**
```bash
# 双击运行启动器选择菜单
启动器选择.bat
```

**直接启动：**
```bash
# 静默启动（无控制台窗口，推荐）
start_launcher_silent.bat

# 普通启动（显示控制台窗口）
start_launcher.bat
```

**特色功能：**
- 🔍 实时检测系统运行状态
- 🚀 一键启动/停止单个或所有系统
- 🌐 直接打开系统页面
- 📊 可视化系统管理界面
- 🔇 静默模式（无控制台窗口干扰）

### 方式二：一键启动所有系统
```bash
# 双击运行启动脚本
start_all_systems.bat
```

### 方式三：单独启动系统
```bash
# 图书管理系统
cd BookManager
python manage.py runserver 127.0.0.1:8000

# 教务管理系统
cd SchoolAcademicManager
python manage.py runserver 127.0.0.1:8001

# 财务管理系统
cd SchoolFinanceManager
python manage.py runserver 127.0.0.1:8002
```

## 📋 系统管理

### 智能启动器（推荐）
- **启动方式选择**: 运行 `启动器选择.bat`
- **静默启动**: 运行 `start_launcher_silent.bat` （无控制台窗口）
- **普通启动**: 运行 `start_launcher.bat` （显示控制台窗口）
- **停止启动器**: 运行 `stop_launcher.bat`
- **访问地址**: http://127.0.0.1:5000
- **功能特色**:
  - 实时状态监控
  - 一键启动/停止
  - 智能系统管理
  - 直接访问系统
  - 静默后台运行

### 传统方式
- **一键启动**: 运行 `start_all_systems.bat`
- **系统导航**: 打开 `index.html` 选择要使用的系统
- **状态检查**: 运行 `check_systems.bat`
- **一键停止**: 运行 `stop_all_systems.bat`

### 强制停止（解决停止失败问题）
- **强制停止脚本**: 运行 `强制停止所有系统.bat`
- **Python强制停止**: 运行 `python kill_systems.py`
- **Web界面强制停止**: 在启动器页面点击"强制停止"按钮
- **诊断工具**: 运行 `diagnose_systems.bat` 查看系统状态

## 🌐 访问地址

### 系统入口
- **智能启动器**: http://127.0.0.1:5000 （推荐）
- **系统导航页面**: `index.html`
- **图书管理系统**: http://127.0.0.1:8000
- **教务管理系统**: http://127.0.0.1:8001
- **财务管理系统**: http://127.0.0.1:8002

### 管理后台
- **图书管理后台**: http://127.0.0.1:8000/admin
- **教务管理后台**: http://127.0.0.1:8001/admin
- **财务管理后台**: http://127.0.0.1:8002/admin

## 🔐 默认账户

所有系统使用相同的默认管理员账户：
- **用户名**: admin
- **密码**: admin123

部分系统还提供测试账户：
- **教师账户**: teacher1~teacher5 / teacher123

## 📊 系统特性

### 🎨 统一设计
- 现代化响应式界面
- Bootstrap 5 + Font Awesome
- 一致的用户体验

### 🔧 技术架构
- **后端**: Django 5.2.1
- **数据库**: SQLite3
- **前端**: Bootstrap 5
- **图标**: Font Awesome 6

### 🌍 多语言支持
- 中文简体
- 英文
- 日文
- 韩文

### 📱 响应式设计
- 支持PC端和移动端
- 自适应屏幕尺寸
- 触摸友好的界面

## 📁 目录结构

```
学校管理系统集合/
├── index.html                 # 系统导航页面
├── start_all_systems.bat      # 一键启动脚本
├── stop_all_systems.bat       # 一键停止脚本
├── check_systems.bat          # 状态检查脚本
├── README_系统集合.md          # 说明文档
├── BookManager/               # 图书管理系统
├── SchoolAcademicManager/     # 教务管理系统
└── SchoolFinanceManager/      # 财务管理系统
```

## 🛠️ 环境要求

- **Python**: 3.8+
- **Django**: 5.2.1
- **操作系统**: Windows 10/11
- **浏览器**: Chrome, Firefox, Edge (现代浏览器)

## 📖 使用指南

### 1. 首次使用
1. 运行 `start_all_systems.bat` 启动所有系统
2. 等待所有系统启动完成（约10-15秒）
3. 打开 `index.html` 选择要使用的系统
4. 使用 admin/admin123 登录管理后台

### 2. 日常使用
1. 根据需要启动单个或多个系统
2. 通过导航页面快速切换系统
3. 使用统一的管理员账户登录

### 3. 系统维护
1. 定期运行 `check_systems.bat` 检查状态
2. 使用 `stop_all_systems.bat` 安全停止所有系统
3. 查看各系统的日志文件排查问题

## 🔍 故障排除

### 端口冲突
如果遇到端口被占用的问题：
1. 运行 `stop_all_systems.bat` 停止所有服务
2. 运行 `check_systems.bat` 检查端口状态
3. 重新启动系统

### 系统无法访问
1. 检查防火墙设置
2. 确认Python环境正常
3. 查看命令行窗口的错误信息

### 数据库问题
如果遇到数据库错误：
1. 检查各系统的 `db.sqlite3` 文件
2. 运行数据库迁移命令
3. 重新初始化数据

## 📞 技术支持

如有问题或建议，请：
1. 查看各系统的详细文档
2. 检查系统日志文件
3. 联系系统管理员

## 📄 许可证

本项目采用 MIT 许可证，详见各子系统的 LICENSE 文件。

---

**学校管理系统集合** - 让学校管理更简单、更高效！
