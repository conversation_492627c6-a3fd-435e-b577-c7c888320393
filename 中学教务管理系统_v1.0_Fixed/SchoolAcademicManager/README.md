# 中学教务管理系统 (Middle School Academic Manager)

一个基于Django开发的现代化中学教务管理系统，专为中学教育环境设计，提供完整的学生、教师、课程、成绩等教务管理功能。

## 功能特性

### 🎓 学生管理
- 学生信息管理（基本信息、学籍状态、家长信息）
- 住校/走读学生管理
- 学生健康状况记录
- 学生考勤记录

### 👨‍🏫 教师管理
- 教师信息管理（基本信息、职称、任教科目）
- 班主任管理
- 教研组管理
- 教学工作量统计

### 📚 课程管理
- 中学课程设置（主科、理科、文科、艺术、体育等）
- 课程类型分类管理
- 教材信息管理
- 考试科目设置

### 🏫 班级管理
- 年级管理（初一到高三）
- 班级类型管理（普通班、理科班、文科班、实验班等）
- 班主任分配
- 教室位置管理

### 📊 成绩管理
- 多种考试类型支持（平时、期中、期末）
- 成绩录入和查询
- 成绩统计分析
- 自动计算等级和通过状态

### 📅 排课管理
- 课程表管理
- 教室安排
- 时间冲突检测

### ✅ 考勤管理
- 学生考勤记录
- 考勤状态统计

### 📈 统计报表
- 成绩统计分析
- 课程通过率统计
- 各类教务数据报表

## 技术架构

- **后端框架**: Django 5.2.1
- **数据库**: SQLite3 (可扩展为MySQL/PostgreSQL)
- **前端框架**: Bootstrap 5 + Font Awesome
- **数据导入导出**: django-import-export
- **多语言支持**: Django i18n (中文/英文/日文/韩文)

## 快速开始

### 环境要求
- Python 3.8+
- pip

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd SchoolAcademicManager
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **初始化数据库**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

4. **创建初始数据**
   ```bash
   python init_data.py
   ```

5. **启动服务器**
   ```bash
   python manage.py runserver 127.0.0.1:8001
   ```

### 快速启动（Windows）
直接运行 `start_server.bat` 文件，系统会自动完成所有初始化步骤。

## 访问系统

- **系统首页**: http://127.0.0.1:8001
- **管理后台**: http://127.0.0.1:8001/admin

### 默认账户
- **管理员**: admin / admin123
- **教师账户**: teacher1 / teacher123, teacher2 / teacher123, teacher3 / teacher123

## 系统截图

### 首页仪表板
- 显示关键统计数据
- 最近成绩和选课记录
- 快速操作入口

### 学生管理
- 学生列表和搜索
- 学生详细信息
- 学籍状态管理

### 课程表
- 可视化课程表显示
- 按时间段和教室查看
- 课程冲突检测

### 成绩统计
- 课程成绩分析
- 通过率统计
- 图表可视化

## 数据模型

### 核心实体
- **Department**: 院系/部门
- **Teacher**: 教师信息
- **Student**: 学生信息
- **Course**: 课程信息
- **Class**: 班级信息
- **Grade**: 年级信息
- **Major**: 专业信息

### 教务实体
- **Semester**: 学期管理
- **CourseOffering**: 开课信息
- **Enrollment**: 选课记录
- **GradeRecord**: 成绩记录
- **Attendance**: 考勤记录
- **Schedule**: 课程表
- **Classroom**: 教室信息

## 配置说明

### 数据库配置
系统默认使用SQLite数据库，生产环境可修改 `settings.py` 中的数据库配置。

### 多语言配置
系统支持中文、英文、日文、韩文，可在 `settings.py` 中配置默认语言。

### 文件上传配置
支持成绩附件上传，可在 `settings.py` 中配置上传路径和大小限制。

## 开发指南

### 添加新功能
1. 在 `models.py` 中定义数据模型
2. 在 `admin.py` 中注册管理界面
3. 在 `views.py` 中实现业务逻辑
4. 在 `urls.py` 中配置URL路由
5. 创建相应的模板文件

### 数据迁移
```bash
python manage.py makemigrations
python manage.py migrate
```

### 创建超级用户
```bash
python manage.py createsuperuser
```

## 部署指南

### 生产环境部署
1. 修改 `settings.py` 中的 `DEBUG = False`
2. 配置 `ALLOWED_HOSTS`
3. 配置生产数据库
4. 收集静态文件: `python manage.py collectstatic`
5. 使用 Gunicorn + Nginx 部署

### Docker部署
```dockerfile
# 可根据需要创建Dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8001
CMD ["python", "manage.py", "runserver", "0.0.0.0:8001"]
```

## 常见问题

### Q: 如何重置管理员密码？
A: 运行 `python manage.py changepassword admin`

### Q: 如何备份数据？
A: 运行 `python manage.py dumpdata > backup.json`

### Q: 如何恢复数据？
A: 运行 `python manage.py loaddata backup.json`

### Q: 如何添加新的院系？
A: 在管理后台的"院系"模块中添加，或通过Django shell操作

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 项目地址: <repository-url>

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本发布
- 完整的教务管理功能
- 多语言支持
- 响应式界面设计
