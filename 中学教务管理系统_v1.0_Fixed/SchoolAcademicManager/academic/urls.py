from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'academic'

urlpatterns = [
    # 首页
    path('', views.index, name='index'),

    # 学生管理
    path('students/', views.student_list, name='student_list'),
    path('students/<str:student_id>/', views.student_detail, name='student_detail'),

    # 教师管理
    path('teachers/', views.teacher_list, name='teacher_list'),

    # 课程管理
    path('courses/', views.course_list, name='course_list'),

    # 课程表
    path('schedule/', views.schedule_view, name='schedule'),

    # 成绩统计
    path('statistics/grades/', views.grade_statistics, name='grade_statistics'),

    # 登出
    path('logout/', auth_views.LogoutView.as_view(next_page='/'), name='logout'),
]
