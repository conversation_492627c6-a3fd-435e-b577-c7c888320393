@echo off
setlocal enabledelayedexpansion

rem =========================
rem Batch build script for SchoolAcademicManager
rem 1. Installs Python deps (from project requirements + packager requirements)
rem 2. Cleans previous PyInstaller artifacts
rem 3. Builds self-contained exe into ..\dist
rem =========================

set PROJECT_DIR=%~dp0..
set PACKAGER_DIR=%~dp0

rem ----- install dependencies -----
if exist "%PROJECT_DIR%\requirements.txt" (
    echo Installing project dependencies...
    pip install -r "%PROJECT_DIR%\requirements.txt"
)
pip install -r "%PACKAGER_DIR%requirements.txt"

rem ----- clean older build artifacts -----
cd "%PROJECT_DIR%"
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build
if exist __pycache__ rmdir /s /q __pycache__

rem ----- run PyInstaller -----
pyinstaller --noconfirm --onedir ^
    --windowed ^
    --name "SchoolAcademicManager" ^
    --add-data "templates;templates" ^
    --add-data "static;static" ^
    --add-data "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\import_export\templates;import_export/templates" ^
    --add-data "run_server.py;." ^
    --add-data "manage.py;." ^
    --add-data "SchoolAcademicManager;SchoolAcademicManager" ^
    --add-data "trial_checker.py;." ^
    --add-data "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\tcl\tcl8.6;tcl/tcl8.6" ^
    --add-data "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\tcl\tk8.6;tcl/tk8.6" ^
    --add-binary "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs\tcl86t.dll;." ^
    --add-binary "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs\tk86t.dll;." ^
    --hidden-import="django" ^
    launcher.py

rem ----- copy runtime helper scripts -----
xcopy /E /I "%PACKAGER_DIR%runtime" "%PROJECT_DIR%\dist\runtime" >nul

rem ----- copy sqlite db -----
if not exist "%PROJECT_DIR%\dist\data" mkdir "%PROJECT_DIR%\dist\data"
if exist "%PROJECT_DIR%\db.sqlite3" copy /Y "%PROJECT_DIR%\db.sqlite3" "%PROJECT_DIR%\dist\data\db.sqlite3" >nul

rem ----- copy static dir (optional) -----
if exist "%PROJECT_DIR%\static" xcopy /E /I "%PROJECT_DIR%\static" "%PROJECT_DIR%\dist\static" >nul

echo.
echo Build finished!  Executable located at: %PROJECT_DIR%\dist\SchoolAcademicManager.exe
pause
