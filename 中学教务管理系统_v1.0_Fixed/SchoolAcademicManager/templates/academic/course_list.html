{% extends 'base.html' %}

{% block title %}课程管理 - 学校教务管理系统{% endblock %}
{% block page_title %}课程管理{% endblock %}

{% block content %}
<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="课程代码或课程名称">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">开课院系</label>
                <select class="form-select" id="department" name="department">
                    <option value="">全部院系</option>
                    {% for department in departments %}
                        <option value="{{ department.id }}" {% if department.id|stringformat:"s" == selected_department %}selected{% endif %}>
                            {{ department.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="course_type" class="form-label">课程类型</label>
                <select class="form-select" id="course_type" name="course_type">
                    <option value="">全部类型</option>
                    {% for value, display in course_type_choices %}
                        <option value="{{ value }}" {% if value == selected_course_type %}selected{% endif %}>
                            {{ display }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{% url 'academic:course_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                    <a href="/admin/academic/course/add/" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加课程
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 课程列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-book me-2"></i>课程列表
            <span class="badge bg-primary ms-2">共 {{ page_obj.paginator.count }} 门</span>
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>课程代码</th>
                            <th>课程名称</th>
                            <th>课程类型</th>
                            <th>学分</th>
                            <th>学时</th>
                            <th>开课院系</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for course in page_obj %}
                        <tr>
                            <td><strong>{{ course.code }}</strong></td>
                            <td>{{ course.name }}</td>
                            <td>
                                <span class="badge bg-{% if course.course_type == 'required' %}danger{% elif course.course_type == 'elective' %}warning{% elif course.course_type == 'public' %}info{% else %}secondary{% endif %}">
                                    {{ course.get_course_type_display }}
                                </span>
                            </td>
                            <td>{{ course.credits }}</td>
                            <td>{{ course.hours }}</td>
                            <td>{{ course.department.name }}</td>
                            <td>
                                <span class="badge bg-{% if course.is_active %}success{% else %}danger{% endif %}">
                                    {% if course.is_active %}启用{% else %}停用{% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="/admin/academic/course/{{ course.id }}/change/" 
                                       class="btn btn-outline-secondary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-info" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#courseModal{{ course.id }}" 
                                            title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- 课程详情模态框 -->
                        <div class="modal fade" id="courseModal{{ course.id }}" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">课程详情 - {{ course.name }}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>基本信息</h6>
                                                <table class="table table-sm">
                                                    <tr><td>课程代码:</td><td>{{ course.code }}</td></tr>
                                                    <tr><td>课程名称:</td><td>{{ course.name }}</td></tr>
                                                    <tr><td>课程类型:</td><td>{{ course.get_course_type_display }}</td></tr>
                                                    <tr><td>学分:</td><td>{{ course.credits }}</td></tr>
                                                    <tr><td>学时:</td><td>{{ course.hours }}</td></tr>
                                                    <tr><td>开课院系:</td><td>{{ course.department.name }}</td></tr>
                                                </table>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>课程描述</h6>
                                                <p>{{ course.description|default:"暂无描述" }}</p>
                                                
                                                {% if course.prerequisites.exists %}
                                                <h6>先修课程</h6>
                                                <ul>
                                                    {% for prereq in course.prerequisites.all %}
                                                        <li>{{ prereq.code }} - {{ prereq.name }}</li>
                                                    {% endfor %}
                                                </ul>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                        <a href="/admin/academic/course/{{ course.id }}/change/" class="btn btn-primary">编辑</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="课程列表分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_course_type %}&course_type={{ selected_course_type }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_course_type %}&course_type={{ selected_course_type }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_course_type %}&course_type={{ selected_course_type }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_course_type %}&course_type={{ selected_course_type }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                <p class="text-muted">没有找到符合条件的课程</p>
                <a href="/admin/academic/course/add/" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>添加第一门课程
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
