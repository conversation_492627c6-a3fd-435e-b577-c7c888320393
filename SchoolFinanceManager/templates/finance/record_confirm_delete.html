{% extends "base.html" %}

{% block title %}删除财务记录 - 学校财务管理系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> 确认删除财务记录
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-warning"></i>
                        <strong>警告：</strong>此操作不可撤销！删除后将无法恢复此财务记录。
                    </div>
                    
                    <h5 class="mb-3">您确定要删除以下财务记录吗？</h5>
                    
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-4"><strong>标题：</strong></div>
                                <div class="col-sm-8">{{ record.title }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>类型：</strong></div>
                                <div class="col-sm-8">
                                    {% if record.record_type == 'income' %}
                                        <span class="badge badge-success">收入</span>
                                    {% else %}
                                        <span class="badge badge-danger">支出</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>分类：</strong></div>
                                <div class="col-sm-8">{{ record.category.name }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>金额：</strong></div>
                                <div class="col-sm-8 {% if record.record_type == 'income' %}text-success{% else %}text-danger{% endif %} font-weight-bold">
                                    {% if record.record_type == 'income' %}+{% else %}-{% endif %}￥{{ record.amount|floatformat:2 }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>支付方式：</strong></div>
                                <div class="col-sm-8">{{ record.payment_method.name }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>交易日期：</strong></div>
                                <div class="col-sm-8">{{ record.transaction_date }}</div>
                            </div>
                            {% if record.description %}
                            <div class="row">
                                <div class="col-sm-4"><strong>描述：</strong></div>
                                <div class="col-sm-8">{{ record.description }}</div>
                            </div>
                            {% endif %}
                            <div class="row">
                                <div class="col-sm-4"><strong>记录人：</strong></div>
                                <div class="col-sm-8">{{ record.created_by.username }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>创建时间：</strong></div>
                                <div class="col-sm-8">{{ record.created_at|date:"Y-m-d H:i:s" }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="text-center">
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-trash"></i> 确认删除
                            </button>
                            <a href="{% url 'finance_record_list' %}" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.card-body .row {
    margin-bottom: 0.5rem;
}

.badge {
    font-size: 0.75em;
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.125rem;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-success {
    color: #28a745 !important;
}

.badge-success {
    background-color: #28a745;
}

.badge-danger {
    background-color: #dc3545;
}
</style>
{% endblock %}
