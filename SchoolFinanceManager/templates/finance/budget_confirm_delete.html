{% extends "base.html" %}

{% block title %}删除预算 - 学校财务管理系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> 确认删除预算
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-warning"></i>
                        <strong>警告：</strong>此操作不可撤销！删除后将无法恢复此预算信息。
                    </div>
                    
                    <h5 class="mb-3">您确定要删除以下预算吗？</h5>
                    
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>预算名称：</strong></div>
                                <div class="col-sm-8">{{ budget.name }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>分类：</strong></div>
                                <div class="col-sm-8">{{ budget.category.name }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>预算金额：</strong></div>
                                <div class="col-sm-8 text-primary font-weight-bold">￥{{ budget.budgeted_amount|floatformat:2 }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>已使用：</strong></div>
                                <div class="col-sm-8 text-danger font-weight-bold">￥{{ budget.spent_amount|floatformat:2 }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>剩余金额：</strong></div>
                                <div class="col-sm-8 {% if budget.remaining_amount < 0 %}text-danger{% else %}text-success{% endif %} font-weight-bold">
                                    ￥{{ budget.remaining_amount|floatformat:2 }}
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>使用率：</strong></div>
                                <div class="col-sm-8">
                                    <span class="badge {% if budget.usage_percentage > 100 %}badge-danger{% elif budget.usage_percentage > 80 %}badge-warning{% else %}badge-success{% endif %}">
                                        {{ budget.usage_percentage|floatformat:1 }}%
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>预算期间：</strong></div>
                                <div class="col-sm-8">{{ budget.start_date }} 至 {{ budget.end_date }}</div>
                            </div>
                            {% if budget.description %}
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>描述：</strong></div>
                                <div class="col-sm-8">{{ budget.description }}</div>
                            </div>
                            {% endif %}
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>创建人：</strong></div>
                                <div class="col-sm-8">{{ budget.created_by.username }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4"><strong>创建时间：</strong></div>
                                <div class="col-sm-8">{{ budget.created_at|date:"Y-m-d H:i:s" }}</div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4"><strong>状态：</strong></div>
                                <div class="col-sm-8">
                                    <span class="badge {% if budget.is_active %}badge-success{% else %}badge-secondary{% endif %}">
                                        {% if budget.is_active %}活跃{% else %}已停用{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="text-center">
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-trash"></i> 确认删除
                            </button>
                            <a href="{% url 'budget_list' %}" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.card-body .row {
    margin-bottom: 0.5rem;
}

.badge {
    font-size: 0.75em;
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.125rem;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.text-primary {
    color: #007bff !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-success {
    color: #28a745 !important;
}

.badge-success {
    background-color: #28a745;
}

.badge-danger {
    background-color: #dc3545;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-secondary {
    background-color: #6c757d;
}
</style>
{% endblock %}
