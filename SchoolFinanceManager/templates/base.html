<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}学校财务管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .main-content {
            min-height: calc(100vh - 120px);
            padding: 2rem 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .btn {
            border-radius: 0.375rem;
        }
        
        .page-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
        
        .footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 1rem 0;
            margin-top: 2rem;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .navbar-nav .nav-link {
            font-weight: 500;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }
            
            .page-title {
                font-size: 1.5rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-university"></i> 学校财务管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mr-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="financeDropdown" role="button" data-toggle="dropdown">
                            <i class="fas fa-money-bill-wave"></i> 财务管理
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="{% url 'finance_record_list' %}">
                                <i class="fas fa-list"></i> 财务记录
                            </a>
                            <a class="dropdown-item" href="{% url 'finance_record_add' %}">
                                <i class="fas fa-plus"></i> 添加记录
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{% url 'budget_list' %}">
                                <i class="fas fa-chart-pie"></i> 预算管理
                            </a>
                            <a class="dropdown-item" href="{% url 'budget_add' %}">
                                <i class="fas fa-plus-circle"></i> 创建预算
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{% url 'finance_report' %}">
                                <i class="fas fa-chart-bar"></i> 财务报表
                            </a>
                        </div>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            {% if user.is_superuser %}
                            <a class="dropdown-item" href="/admin/">
                                <i class="fas fa-cog"></i> 后台管理
                            </a>
                            <div class="dropdown-divider"></div>
                            {% endif %}
                            <form method="post" action="{% url 'logout' %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="dropdown-item" style="border: none; background: none; width: 100%; text-align: left;">
                                    <i class="fas fa-sign-out-alt"></i> 退出登录
                                </button>
                            </form>
                        </div>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/login/">
                            <i class="fas fa-sign-in-alt"></i> 登录
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container">
            <!-- 消息提示 -->
            {% if messages %}
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                {% endfor %}
            {% endif %}
            
            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p class="mb-0">
                &copy; 2025 学校财务管理系统 | 
                <i class="fas fa-code"></i> 基于 Django 开发
            </p>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
