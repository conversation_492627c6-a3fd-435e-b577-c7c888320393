#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学校财务管理系统初始化数据脚本
创建基础的财务分类和支付方式数据
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolFinanceManager.settings')
django.setup()

from finance.models import FinanceCategory, PaymentMethod
from django.contrib.auth.models import User

def create_superuser():
    """创建超级用户"""
    print("正在创建超级用户...")

    if not User.objects.filter(username='admin').exists():
        user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='系统',
            last_name='管理员'
        )
        print(f"✅ 创建超级用户: {user.username}")
    else:
        print("⚠️ 超级用户已存在: admin")

def create_finance_categories():
    """创建财务分类"""
    print("正在创建财务分类...")
    
    # 收入分类
    income_categories = [
        {'name': '学费收入', 'description': '学生缴纳的学费'},
        {'name': '杂费收入', 'description': '各种杂费收入'},
        {'name': '政府补助', 'description': '政府拨款和补助'},
        {'name': '捐赠收入', 'description': '社会捐赠和赞助'},
        {'name': '培训收入', 'description': '培训班和课程收入'},
        {'name': '其他收入', 'description': '其他各类收入'},
    ]
    
    for category_data in income_categories:
        category, created = FinanceCategory.objects.get_or_create(
            name=category_data['name'],
            category_type='income',
            defaults={
                'description': category_data['description'],
                'is_active': True
            }
        )
        if created:
            print(f"✅ 创建收入分类: {category.name}")
        else:
            print(f"⚠️ 收入分类已存在: {category.name}")
    
    # 支出分类
    expense_categories = [
        {'name': '教师工资', 'description': '教师薪资和福利'},
        {'name': '行政费用', 'description': '行政管理费用'},
        {'name': '设备采购', 'description': '教学设备和办公设备采购'},
        {'name': '维修费用', 'description': '设施设备维修费用'},
        {'name': '水电费', 'description': '水费、电费等公用事业费'},
        {'name': '办公用品', 'description': '办公用品和耗材'},
        {'name': '图书采购', 'description': '图书和教材采购'},
        {'name': '活动费用', 'description': '学校活动和比赛费用'},
        {'name': '培训费用', 'description': '教师培训和进修费用'},
        {'name': '其他支出', 'description': '其他各类支出'},
    ]
    
    for category_data in expense_categories:
        category, created = FinanceCategory.objects.get_or_create(
            name=category_data['name'],
            category_type='expense',
            defaults={
                'description': category_data['description'],
                'is_active': True
            }
        )
        if created:
            print(f"✅ 创建支出分类: {category.name}")
        else:
            print(f"⚠️ 支出分类已存在: {category.name}")

def create_payment_methods():
    """创建支付方式"""
    print("\n正在创建支付方式...")
    
    payment_methods = [
        {'name': '现金', 'description': '现金支付'},
        {'name': '银行转账', 'description': '银行转账支付'},
        {'name': '支票', 'description': '支票支付'},
        {'name': '微信支付', 'description': '微信支付'},
        {'name': '支付宝', 'description': '支付宝支付'},
        {'name': '刷卡', 'description': '银行卡刷卡支付'},
        {'name': '网银', 'description': '网上银行支付'},
        {'name': '其他', 'description': '其他支付方式'},
    ]
    
    for method_data in payment_methods:
        method, created = PaymentMethod.objects.get_or_create(
            name=method_data['name'],
            defaults={
                'description': method_data['description'],
                'is_active': True
            }
        )
        if created:
            print(f"✅ 创建支付方式: {method.name}")
        else:
            print(f"⚠️ 支付方式已存在: {method.name}")

def create_users():
    """创建用户账户"""
    print("\n正在创建用户账户...")

    # 创建管理员用户
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='系统',
            last_name='管理员'
        )
        print(f"✅ 创建管理员用户: {admin_user.username}")
    else:
        print("⚠️ 管理员用户已存在: admin")

    # 创建财务主管账户
    if not User.objects.filter(username='finance_manager').exists():
        finance_manager = User.objects.create_user(
            username='finance_manager',
            email='<EMAIL>',
            password='finance123',
            first_name='财务',
            last_name='主管'
        )
        finance_manager.is_staff = True  # 允许访问后台
        finance_manager.save()
        print(f"✅ 创建财务主管: {finance_manager.username}")
    else:
        print("⚠️ 财务主管已存在: finance_manager")

    # 创建会计账户
    if not User.objects.filter(username='accountant').exists():
        accountant = User.objects.create_user(
            username='accountant',
            email='<EMAIL>',
            password='account123',
            first_name='会计',
            last_name='员'
        )
        accountant.is_staff = True  # 允许访问后台
        accountant.save()
        print(f"✅ 创建会计员: {accountant.username}")
    else:
        print("⚠️ 会计员已存在: accountant")

    # 创建出纳账户
    if not User.objects.filter(username='cashier').exists():
        cashier = User.objects.create_user(
            username='cashier',
            email='<EMAIL>',
            password='cashier123',
            first_name='出纳',
            last_name='员'
        )
        cashier.is_staff = True  # 允许访问后台
        cashier.save()
        print(f"✅ 创建出纳员: {cashier.username}")
    else:
        print("⚠️ 出纳员已存在: cashier")

    # 创建审计账户
    if not User.objects.filter(username='auditor').exists():
        auditor = User.objects.create_user(
            username='auditor',
            email='<EMAIL>',
            password='audit123',
            first_name='审计',
            last_name='员'
        )
        auditor.is_staff = True  # 允许访问后台
        auditor.save()
        print(f"✅ 创建审计员: {auditor.username}")
    else:
        print("⚠️ 审计员已存在: auditor")

def main():
    """主函数"""
    print("=" * 60)
    print("🏫 学校财务管理系统数据初始化")
    print("=" * 60)
    
    try:
        # 创建用户账户
        create_users()
        
        # 创建财务分类
        create_finance_categories()
        
        # 创建支付方式
        create_payment_methods()
        
        print("\n" + "=" * 60)
        print("✅ 学校财务管理系统数据初始化完成！")
        print("=" * 60)
        
        # 显示统计信息
        income_count = FinanceCategory.objects.filter(category_type='income').count()
        expense_count = FinanceCategory.objects.filter(category_type='expense').count()
        payment_count = PaymentMethod.objects.count()
        user_count = User.objects.count()
        
        print(f"\n📊 数据统计:")
        print(f"   👥 用户数量: {user_count} 个")
        print(f"   📈 收入分类: {income_count} 个")
        print(f"   📉 支出分类: {expense_count} 个")
        print(f"   💳 支付方式: {payment_count} 个")
        
        print(f"\n🚀 系统启动指南:")
        print(f"   1. 运行: python manage.py runserver 127.0.0.1:8002")
        print(f"   2. 访问: http://127.0.0.1:8002/")
        print(f"   3. 后台: http://127.0.0.1:8002/admin/")

        print(f"\n👥 用户账户信息:")
        print(f"   🔑 系统管理员: admin / admin123")
        print(f"   💼 财务主管: finance_manager / finance123")
        print(f"   📊 会计员: accountant / account123")
        print(f"   💰 出纳员: cashier / cashier123")
        print(f"   🔍 审计员: auditor / audit123")
        
        print(f"\n💡 功能说明:")
        print(f"   • 财务记录管理: 记录收入和支出")
        print(f"   • 预算管理: 制定和监控预算")
        print(f"   • 财务报表: 查看图表和统计")
        print(f"   • 后台管理: 管理分类和用户")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    main()
