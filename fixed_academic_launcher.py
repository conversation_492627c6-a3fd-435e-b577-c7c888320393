#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复版SchoolAcademicManager GUI启动器
解决Django启动问题的简化版本
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import webbrowser
import time
import socket
import subprocess
from academic_trial_manager import AcademicTrialManager
from academic_license_validator import AcademicLicenseValidator

class FixedAcademicLauncher:
    def __init__(self):
        self.trial_manager = AcademicTrialManager(trial_days=60)
        self.license_validator = AcademicLicenseValidator(self.trial_manager)
        self.server_process = None
        self.server_running = False
        self.root = None
        
    def create_gui(self):
        """创建图形界面"""
        self.root = tk.Tk()
        self.root.title("中学教务管理系统 v1.0")
        self.root.geometry("700x650")
        self.root.resizable(False, False)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="中学教务管理系统", 
                               font=("Microsoft YaHei", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 试用期信息
        self.create_trial_section(main_frame)
        
        # 系统控制
        self.create_control_section(main_frame)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W, padding="5")
        status_bar.pack(fill=tk.X, pady=(20, 0))
        
        # 更新状态
        self.update_status()
        
        # 居中显示
        self.center_window()
        
        return self.root
    
    def create_trial_section(self, parent):
        """创建试用期信息区域"""
        trial_frame = ttk.LabelFrame(parent, text="授权信息", padding="15")
        trial_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 状态显示
        self.status_label = ttk.Label(trial_frame, text="检查中...", 
                                     font=("Microsoft YaHei", 11, "bold"))
        self.status_label.pack(pady=(0, 10))
        
        # 详细信息
        self.detail_label = ttk.Label(trial_frame, text="", 
                                     font=("Microsoft YaHei", 9))
        self.detail_label.pack(pady=(0, 15))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(trial_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.pack(pady=(0, 10))
        
        # 进度标签
        self.progress_label = ttk.Label(trial_frame, text="", 
                                       font=("Microsoft YaHei", 8))
        self.progress_label.pack(pady=(0, 15))
        
        # 按钮区域
        button_frame = ttk.Frame(trial_frame)
        button_frame.pack(pady=(10, 0))

        # 激活按钮
        self.activate_btn = ttk.Button(button_frame, text="激活软件",
                                      command=self.activate_software)
        self.activate_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 查看机器ID按钮
        self.machine_id_btn = ttk.Button(button_frame, text="查看机器ID",
                                        command=self.show_machine_id_dialog)
        self.machine_id_btn.pack(side=tk.LEFT)
    
    def create_control_section(self, parent):
        """创建系统控制区域"""
        control_frame = ttk.LabelFrame(parent, text="系统控制", padding="15")
        control_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 系统状态
        self.system_status_label = ttk.Label(control_frame, text="系统状态：未启动", 
                                            font=("Microsoft YaHei", 10))
        self.system_status_label.pack(pady=(0, 15))
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        self.start_btn = ttk.Button(button_frame, text="启动系统", 
                                   command=self.start_system, state="disabled",
                                   width=15)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(button_frame, text="停止系统", 
                                  command=self.stop_system, state="disabled",
                                  width=15)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.open_btn = ttk.Button(button_frame, text="打开系统", 
                                  command=self.open_system, state="disabled",
                                  width=15)
        self.open_btn.pack(side=tk.LEFT)
        
        # 系统信息
        info_frame = ttk.Frame(control_frame)
        info_frame.pack(pady=(15, 0))
        
        ttk.Label(info_frame, text="访问地址：http://127.0.0.1:8001", 
                 font=("Microsoft YaHei", 9)).pack()
        ttk.Label(info_frame, text="默认账户：admin / admin123", 
                 font=("Microsoft YaHei", 9)).pack()
    
    def center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = 700
        height = 650
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def update_status(self):
        """更新状态显示"""
        # 检查许可证状态
        license_status = self.license_validator.check_license_status()
        
        if license_status['licensed']:
            # 已激活许可证
            self.status_label.config(text="✅ 软件已激活", foreground="green")
            self.detail_label.config(text=f"许可证类型：{license_status['license_type']} | "
                                         f"到期日期：{license_status['expire_date']}")
            self.progress_bar.pack_forget()
            self.progress_label.pack_forget()
            self.start_btn.config(state="normal")
            self.activate_btn.config(text="重新激活")
        else:
            # 检查试用期
            trial_status = self.trial_manager.check_trial_status()
            
            if trial_status['status'] == 'active':
                # 试用期有效
                days_remaining = trial_status['days_remaining']
                days_used = trial_status['days_used']
                total_days = trial_status['total_days']
                
                self.status_label.config(text=f"⏰ 试用期 (剩余 {days_remaining} 天)", 
                                        foreground="orange")
                self.detail_label.config(text=f"试用期：{trial_status['start_date']} 至 {trial_status['end_date']}")
                
                progress = (days_used / total_days) * 100
                self.progress_var.set(progress)
                self.progress_label.config(text=f"已使用 {days_used}/{total_days} 天 ({progress:.1f}%)")
                
                self.start_btn.config(state="normal")
                self.activate_btn.config(text="激活软件")
            else:
                # 试用期已过期
                self.status_label.config(text="❌ 试用期已过期", foreground="red")
                self.detail_label.config(text="请购买正式版本或输入有效的许可证密钥")
                self.progress_var.set(100)
                self.progress_label.config(text="试用期已结束")
                self.start_btn.config(state="disabled")
                self.activate_btn.config(text="激活软件")
    
    def activate_software(self):
        """激活软件"""
        try:
            # 使用简单的输入对话框
            license_key = simpledialog.askstring(
                "软件激活",
                "请输入许可证密钥 (格式: XXXXX-XXXXX-XXXXX-XXXXX-XXXXX):",
                parent=self.root
            )

            if not license_key:
                return  # 用户取消了

            # 验证并激活许可证
            success, message = self.license_validator.activate_license(license_key.strip().upper())

            if success:
                messagebox.showinfo("激活成功", f"许可证激活成功！\n\n{message}")
                self.update_status()
            else:
                messagebox.showerror("激活失败", f"许可证激活失败：\n\n{message}")

        except Exception as e:
            messagebox.showerror("错误", f"激活过程中发生错误：\n{str(e)}")

    def show_machine_id_dialog(self):
        """显示机器ID对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("机器信息")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (250)
        y = (dialog.winfo_screenheight() // 2) - (200)
        dialog.geometry(f"500x400+{x}+{y}")

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="机器信息",
                 font=("Microsoft YaHei", 14, "bold")).pack(pady=(0, 20))

        # 机器ID显示
        machine_id = self.trial_manager.machine_id

        info_text = f"""您的机器标识：

{machine_id}

购买许可证说明：
• 购买许可证时请提供上述机器标识
• 许可证与此机器硬件绑定，仅限本机使用
• 更换主要硬件后机器标识可能会改变

联系方式：
• 销售热线：021445566
• 邮箱：<EMAIL>
• QQ客服：17484780

请联系销售人员获取许可证价格和购买信息。"""

        from tkinter import scrolledtext
        text_widget = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD,
                                               font=("Microsoft YaHei", 9), height=15)
        text_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        text_widget.insert(tk.END, info_text)
        text_widget.config(state=tk.DISABLED)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def copy_machine_id():
            self.root.clipboard_clear()
            self.root.clipboard_append(machine_id)
            messagebox.showinfo("成功", "机器标识已复制到剪贴板", parent=dialog)

        ttk.Button(button_frame, text="复制机器标识",
                  command=copy_machine_id).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="关闭",
                  command=dialog.destroy).pack(side=tk.RIGHT)
    

    
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False
    
    def start_system(self):
        """启动系统"""
        # 防止重复启动
        if hasattr(self, '_starting') and self._starting:
            messagebox.showinfo("提示", "系统正在启动中，请稍候...")
            return

        if not self.license_validator.can_run():
            messagebox.showerror("无法启动", "试用期已过期或许可证无效，请激活软件")
            return

        if self.check_port(8001):
            messagebox.showwarning("端口占用", "端口8001已被占用，请先停止占用该端口的程序")
            return

        self._starting = True
        self.status_var.set("正在启动系统...")
        self.start_btn.config(state="disabled")

        # 在后台线程中启动
        threading.Thread(target=self.start_django, daemon=True).start()
    
    def start_django(self):
        """启动Django服务器"""
        try:
            # 获取资源路径（兼容开发环境和打包环境）
            def get_resource_path(relative_path):
                try:
                    # PyInstaller创建临时文件夹，路径存储在_MEIPASS中
                    base_path = sys._MEIPASS
                except Exception:
                    # 开发环境中使用当前目录
                    base_path = os.path.abspath(".")
                return os.path.join(base_path, relative_path)

            # 查找Django项目目录 - 优先使用外部目录
            exe_dir = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))
            possible_paths = [
                os.path.join(exe_dir, "SchoolAcademicManager"),  # exe文件旁边
                "SchoolAcademicManager",  # 当前目录
                get_resource_path("SchoolAcademicManager"),  # 打包内部（最后尝试）
                os.path.join(os.getcwd(), "SchoolAcademicManager")
            ]

            academic_dir = None
            for path in possible_paths:
                if os.path.exists(path) and os.path.exists(os.path.join(path, "manage.py")):
                    academic_dir = os.path.abspath(path)
                    break

            if not academic_dir:
                # 详细的错误信息
                error_info = "找不到SchoolAcademicManager目录\n\n尝试的路径:\n"
                for path in possible_paths:
                    exists = "✓" if os.path.exists(path) else "✗"
                    error_info += f"{exists} {path}\n"
                error_info += "\n请确保SchoolAcademicManager目录与程序在同一目录中。"
                raise Exception(error_info)

            manage_py = os.path.join(academic_dir, "manage.py")
            if not os.path.exists(manage_py):
                raise Exception(f"找不到manage.py文件: {manage_py}")

            # 首先检查Django环境
            try:
                # 测试Django导入
                import django
                print(f"Django版本: {django.get_version()}")
            except ImportError as e:
                raise Exception(f"Django未安装或无法导入: {str(e)}")

            # 检查数据库文件
            db_file = os.path.join(academic_dir, "db.sqlite3")
            if not os.path.exists(db_file):
                print("数据库文件不存在，将在首次运行时创建")

            # 检查Python可执行文件
            python_exe = sys.executable
            if not os.path.exists(python_exe):
                raise Exception(f"Python可执行文件不存在: {python_exe}")

            # 简单检查manage.py文件
            print(f"跳过Django配置检查，直接启动服务器")

            # 启动Django服务器 - 使用系统Python而不是打包的Python
            # 尝试多个Python路径
            python_candidates = [
                "python",  # 系统PATH中的python
                "python.exe",
                r"C:\Python312\python.exe",
                r"C:\Python311\python.exe",
                r"C:\Python310\python.exe",
                sys.executable  # 最后尝试打包的python
            ]

            python_exe = None
            for candidate in python_candidates:
                try:
                    result = subprocess.run([candidate, "--version"],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        python_exe = candidate
                        print(f"使用Python: {candidate} - {result.stdout.strip()}")
                        break
                except:
                    continue

            if not python_exe:
                raise Exception("找不到可用的Python解释器")

            # 使用批处理文件启动Django (更简单可靠)
            bat_path = os.path.join(os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__)), "start_django.bat")
            if not os.path.exists(bat_path):
                bat_path = "start_django.bat"  # 尝试当前目录

            if not os.path.exists(bat_path):
                raise Exception(f"找不到Django启动脚本: {bat_path}")

            cmd = [bat_path]

            print(f"启动命令: {' '.join(cmd)}")
            print(f"Django启动脚本: {bat_path}")

            # 隐藏控制台窗口
            startupinfo = None
            if os.name == 'nt':  # Windows
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace',
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # 等待服务器启动
            startup_output = []
            for i in range(20):  # 增加等待时间
                time.sleep(1)

                # 读取输出
                if self.server_process.stdout:
                    try:
                        line = self.server_process.stdout.readline()
                        if line:
                            startup_output.append(line.strip())
                            print(f"Django输出: {line.strip()}")
                    except:
                        pass

                # 检查进程状态
                if self.server_process.poll() is not None:
                    # 进程已退出，读取所有剩余输出
                    try:
                        remaining_output, _ = self.server_process.communicate(timeout=5)
                        if remaining_output:
                            startup_output.extend(remaining_output.split('\n'))
                    except:
                        pass

                    all_output = '\n'.join(startup_output)
                    error_msg = f"Django启动失败\n返回码: {self.server_process.returncode}\n\n完整输出:\n{all_output}"
                    raise Exception(error_msg)

                # 检查端口
                if self.check_port(8001):
                    self.server_running = True
                    self._starting = False  # 重置启动状态
                    self.root.after(0, self.on_server_started)
                    return

                # 检查是否有启动成功的标志
                output_text = '\n'.join(startup_output).lower()
                if 'starting development server' in output_text or 'quit the server' in output_text:
                    # Django已经启动，再等待一下端口
                    for j in range(5):
                        time.sleep(1)
                        if self.check_port(8001):
                            self.server_running = True
                            self.root.after(0, self.on_server_started)
                            return

            # 启动超时
            all_output = '\n'.join(startup_output)
            raise Exception(f"Django服务器启动超时\n\n输出信息:\n{all_output}")

        except Exception as e:
            self.server_running = False
            self._starting = False  # 重置启动状态
            error_msg = str(e)
            self.root.after(0, lambda: self.on_server_error(error_msg))
    
    def on_server_started(self):
        """服务器启动成功"""
        self.status_var.set("系统已启动 - http://127.0.0.1:8001")
        self.system_status_label.config(text="系统状态：运行中", foreground="green")
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.open_btn.config(state="normal")
        webbrowser.open("http://127.0.0.1:8001")
    
    def on_server_error(self, error):
        """服务器启动失败"""
        self.status_var.set("启动失败")
        self.system_status_label.config(text="系统状态：启动失败", foreground="red")
        self.start_btn.config(state="normal")
        messagebox.showerror("启动失败", f"系统启动失败：\n\n{error}")
    
    def stop_system(self):
        """停止系统"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process = None
        
        self.server_running = False
        self.status_var.set("系统已停止")
        self.system_status_label.config(text="系统状态：已停止", foreground="gray")
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.open_btn.config(state="disabled")
    
    def open_system(self):
        """打开系统页面"""
        if self.check_port(8001):
            webbrowser.open("http://127.0.0.1:8001")
        else:
            messagebox.showwarning("系统未运行", "系统未运行，请先启动系统")
    
    def run(self):
        """运行启动器"""
        root = self.create_gui()
        root.mainloop()

def main():
    """主函数"""
    try:
        launcher = FixedAcademicLauncher()
        launcher.run()
    except Exception as e:
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动失败", f"程序启动失败：\n{str(e)}")
            root.destroy()
        except:
            print(f"程序启动失败：{str(e)}")

if __name__ == '__main__':
    main()
