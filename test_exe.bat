@echo off
chcp 65001
title 测试中学教务管理系统可执行文件

echo ========================================
echo    🧪 测试中学教务管理系统可执行文件
echo ========================================
echo.

echo 📁 检查可执行文件...
if exist "dist\中学教务管理系统.exe" (
    echo ✅ 可执行文件存在
    
    echo.
    echo 📊 文件信息:
    dir "dist\中学教务管理系统.exe"
    
    echo.
    echo 🚀 启动可执行文件...
    echo 注意：这将启动图形界面程序
    echo.
    
    start "" "dist\中学教务管理系统.exe"
    
    echo ✅ 可执行文件已启动
    echo 💡 请检查程序是否正常运行
    
) else (
    echo ❌ 可执行文件不存在
    echo 请先运行 build_academic_exe.py 进行打包
)

echo.
echo ========================================
pause
