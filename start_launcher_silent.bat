@echo off
chcp 65001 >nul 2>&1

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查并安装依赖
pip show flask >nul 2>&1
if errorlevel 1 (
    pip install flask flask-cors psutil >nul 2>&1
)

pip show psutil >nul 2>&1
if errorlevel 1 (
    pip install psutil >nul 2>&1
)

REM 静默启动控制器（无窗口）
start /min "" pythonw system_controller_silent.py

REM 等待服务器启动
timeout /t 3 >nul

REM 打开浏览器
start "" "http://127.0.0.1:5000"

REM 显示提示信息
echo ========================================
echo    🚀 学校管理系统启动器已启动
echo ========================================
echo.
echo ✅ 控制器已在后台运行
echo 🌐 浏览器已打开启动器页面
echo 📍 访问地址: http://127.0.0.1:5000
echo.
echo 💡 提示：
echo   • 启动器在后台运行，无控制台窗口
echo   • 关闭浏览器不会停止启动器
echo   • 要完全停止，请运行 stop_launcher.bat
echo.
echo 按任意键关闭此窗口...
pause >nul
