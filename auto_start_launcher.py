#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动启动控制器
检测控制器是否运行，如果没有运行则自动启动
"""

import socket
import subprocess
import time
import webbrowser
import threading
import os

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def start_controller():
    """启动控制器"""
    try:
        # 使用pythonw启动静默控制器
        subprocess.Popen([
            'pythonw', 'system_controller_silent.py'
        ], cwd=os.getcwd())
        return True
    except Exception as e:
        print(f"启动控制器失败: {e}")
        return False

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(3)
    webbrowser.open('http://127.0.0.1:5000')

def main():
    print("=" * 50)
    print("🚀 自动启动学校管理系统控制器")
    print("=" * 50)
    
    # 检查控制器是否已经运行
    if check_port(5000):
        print("✅ 控制器已在运行")
        print("🌐 打开浏览器...")
        webbrowser.open('http://127.0.0.1:5000')
    else:
        print("⚠️ 控制器未运行，正在启动...")
        
        if start_controller():
            print("✅ 控制器启动成功")
            print("⏳ 等待控制器完全启动...")
            
            # 等待控制器启动
            for i in range(10):
                time.sleep(1)
                if check_port(5000):
                    print("✅ 控制器已就绪")
                    break
                print(f"⏳ 等待中... ({i+1}/10)")
            else:
                print("❌ 控制器启动超时")
                return
            
            print("🌐 打开浏览器...")
            # 在后台线程中打开浏览器
            threading.Thread(target=open_browser_delayed, daemon=True).start()
        else:
            print("❌ 控制器启动失败")
            return
    
    print("\n💡 提示:")
    print("  • 控制器地址: http://127.0.0.1:5000")
    print("  • 控制器在后台运行，关闭此窗口不影响使用")
    print("  • 要停止控制器，请运行 '强制停止所有系统.bat'")
    print("\n" + "=" * 50)
    
    input("按回车键退出...")

if __name__ == '__main__':
    main()
