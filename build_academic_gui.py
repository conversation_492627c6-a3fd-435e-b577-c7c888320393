#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SchoolAcademicManager GUI启动器打包脚本
使用PyInstaller将GUI启动器打包为可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """检查打包所需的依赖"""
    required_packages = [
        'cryptography',
        'wmi',
        'tkinter'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)

    # 特别检查pyinstaller
    try:
        import PyInstaller
        print("✅ pyinstaller 已安装")
    except ImportError:
        missing_packages.append('pyinstaller')

    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        for package in missing_packages:
            if package == 'tkinter':
                print("  # tkinter通常随Python安装，如果缺失请重新安装Python")
            else:
                print(f"  pip install {package}")
        return False

    print("✅ 所有依赖包已安装")
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['fixed_academic_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('academic_trial_manager.py', '.'),
        ('academic_license_validator.py', '.'),
        ('fixed_academic_launcher.py', '.'),
        ('start_django.bat', '.'),
    ],
    hiddenimports=[
        'academic_trial_manager',
        'academic_license_validator',
        'cryptography.fernet',
        'wmi',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'tkinter.scrolledtext',
        'django',
        'django.core.management',
        'django.core.management.commands',
        'django.core.management.commands.runserver',
        'django.core.wsgi',
        'django.conf',
        'django.apps',
        'django.contrib',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.db',
        'django.db.backends',
        'django.db.backends.sqlite3',
        'django.template',
        'django.urls',
        'django.utils',
        'django.views',
        'django.middleware',
        'django.http',
        'django.shortcuts',
        'django.forms',
        'django.contrib.auth.models',
        'django.contrib.auth.views',
        'django.contrib.auth.forms',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='中学教务管理系统',
    debug=False,  # 关闭调试模式
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 关闭控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='school_icon.ico' if os.path.exists('school_icon.ico') else None,
)
'''

    with open('academic_gui.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ 已创建PyInstaller规格文件: academic_gui.spec")

def prepare_files():
    """准备打包所需的文件"""
    print("📁 准备打包文件...")
    
    # 检查必需文件
    required_files = [
        'fixed_academic_launcher.py',
        'academic_trial_manager.py',
        'academic_license_validator.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必需文件: {', '.join(missing_files)}")
        return False
    
    # 检查SchoolAcademicManager目录
    if not os.path.exists('SchoolAcademicManager'):
        print("❌ 找不到SchoolAcademicManager目录")
        print("请确保SchoolAcademicManager目录存在于当前目录中")
        return False
    
    if not os.path.exists('SchoolAcademicManager/manage.py'):
        print("❌ SchoolAcademicManager目录中找不到manage.py文件")
        return False
    
    print("✅ 所有必需文件已准备就绪")
    return True

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("🧹 已清理build目录")
        
        if os.path.exists('dist'):
            shutil.rmtree('dist')
            print("🧹 已清理dist目录")
        
        # 运行PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', 'academic_gui.spec', '--clean']
        
        print("执行命令:", ' '.join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            return True
        else:
            print("❌ 构建失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def create_installer_files():
    """创建安装程序相关文件"""
    print("📦 创建安装程序文件...")
    
    # 创建README文件
    readme_content = """中学教务管理系统 v1.0
========================================

安装说明：
1. 双击"中学教务管理系统.exe"启动程序
2. 首次运行有60天试用期
3. 试用期结束后需要购买许可证激活

系统要求：
- Windows 7/8/10/11 (64位)
- 至少2GB内存
- 500MB可用硬盘空间
- 端口8001可用

使用说明：
1. 启动程序后会显示授权信息
2. 点击"启动系统"启动Django服务器
3. 系统会自动在浏览器中打开
4. 默认管理员账户：admin / admin123

功能特色：
• 学生信息管理
• 教师班级管理
• 课程安排管理
• 成绩考勤管理
• 数据统计分析

技术支持：
邮箱：<EMAIL>
电话：400-123-4567

© 2024 学校管理系统开发团队
"""
    
    dist_dir = Path('dist')
    if dist_dir.exists():
        readme_file = dist_dir / 'README.txt'
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ 已创建README文件: {readme_file}")
    
    # 创建启动脚本
    batch_content = """@echo off
chcp 65001
title 中学教务管理系统
echo 正在启动中学教务管理系统...
start "" "中学教务管理系统.exe"
"""
    
    if dist_dir.exists():
        batch_file = dist_dir / '启动系统.bat'
        with open(batch_file, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print(f"✅ 已创建启动脚本: {batch_file}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 SchoolAcademicManager GUI启动器打包工具")
    print("=" * 60)
    
    # 检查依赖
    if not check_requirements():
        return False
    
    # 准备文件
    if not prepare_files():
        return False
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 创建安装程序文件
    create_installer_files()
    
    print("\n" + "=" * 60)
    print("🎉 打包完成!")
    print("=" * 60)
    
    # 显示结果
    exe_path = Path('dist') / '中学教务管理系统.exe'
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)
        print(f"📁 可执行文件: {exe_path}")
        print(f"📏 文件大小: {file_size:.1f} MB")
        print(f"📂 输出目录: {exe_path.parent.absolute()}")
        
        print("\n📋 分发清单:")
        print("  • 中学教务管理系统.exe  - 主程序")
        print("  • README.txt           - 使用说明")
        print("  • 启动系统.bat         - 启动脚本")
        
        print("\n🧪 测试建议:")
        print("1. 在不同的Windows版本上测试")
        print("2. 测试试用期功能")
        print("3. 测试许可证激活功能")
        print("4. 测试系统启动和停止")
        print("5. 检查防火墙和杀毒软件兼容性")
        
        return True
    else:
        print("❌ 未找到生成的可执行文件")
        return False

if __name__ == '__main__':
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 打包成功完成!")
    else:
        print("❌ 打包失败!")
    print("=" * 60)
    
    input("\n按回车键退出...")
