@echo off
chcp 65001
title 测试中学教务管理系统GUI启动器

echo ========================================
echo    🧪 中学教务管理系统GUI启动器测试
echo ========================================
echo.

:menu
echo 请选择测试选项：
echo.
echo [1] 测试试用期管理器
echo [2] 测试许可证验证器
echo [3] 运行GUI启动器
echo [4] 打包为可执行文件
echo [5] 测试打包后的程序
echo [6] 清理测试数据
echo [7] 退出
echo.
echo ========================================

set /p choice=请输入选择 (1-7): 

if "%choice%"=="1" goto test_trial
if "%choice%"=="2" goto test_license
if "%choice%"=="3" goto run_gui
if "%choice%"=="4" goto build_exe
if "%choice%"=="5" goto test_exe
if "%choice%"=="6" goto cleanup
if "%choice%"=="7" goto exit

echo 无效选择，请重新输入...
timeout /t 2 >nul
goto menu

:test_trial
echo.
echo 🔐 测试试用期管理器...
python academic_trial_manager.py
echo.
pause
goto menu

:test_license
echo.
echo 🔑 测试许可证验证器...
python academic_license_validator.py
echo.
pause
goto menu

:run_gui
echo.
echo 🖥️ 运行GUI启动器...
python academic_gui_launcher.py
echo.
pause
goto menu

:build_exe
echo.
echo 🔨 打包为可执行文件...
python build_academic_gui.py
echo.
pause
goto menu

:test_exe
echo.
echo 🧪 测试打包后的程序...
if exist "dist\中学教务管理系统.exe" (
    echo 启动可执行文件...
    start "" "dist\中学教务管理系统.exe"
    echo 程序已启动，请在GUI中进行测试
) else (
    echo ❌ 找不到可执行文件，请先执行打包操作
)
echo.
pause
goto menu

:cleanup
echo.
echo 🧹 清理测试数据...
echo.
echo 警告：此操作将删除以下数据：
echo • 试用期信息
echo • 许可证文件
echo • 机器ID文件
echo • 注册表项
echo.
set /p confirm=确定要清理吗？(y/N): 
if /i "%confirm%"=="y" (
    echo 正在清理...
    
    REM 删除配置文件
    if exist "%APPDATA%\SchoolAcademic" (
        rmdir /s /q "%APPDATA%\SchoolAcademic"
        echo ✅ 已删除配置目录
    )
    
    REM 清理注册表（需要管理员权限）
    reg delete "HKCU\SOFTWARE\SchoolManagement\Academic" /f >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ 已清理注册表项
    ) else (
        echo ⚠️ 清理注册表项失败（可能需要管理员权限）
    )
    
    REM 删除构建文件
    if exist "build" rmdir /s /q "build"
    if exist "dist" rmdir /s /q "dist"
    if exist "academic_gui.spec" del "academic_gui.spec"
    if exist "__pycache__" rmdir /s /q "__pycache__"
    
    echo ✅ 清理完成
) else (
    echo 取消清理操作
)
echo.
pause
goto menu

:exit
echo.
echo 👋 测试完成，再见！
timeout /t 2 >nul
exit

REM 错误处理
:error
echo.
echo ❌ 发生错误，请检查：
echo 1. Python是否正确安装
echo 2. 所需的依赖包是否已安装
echo 3. SchoolAcademicManager目录是否存在
echo.
pause
goto menu
