# 🎯 SchoolAcademicManager GUI启动器

专为中学教务管理系统设计的图形界面启动器，包含完整的试用期管理、许可证激活和系统启动功能。

## ✨ 功能特色

### 🔐 试用期管理
- **60天试用期** - 基于硬件ID绑定，防篡改
- **加密存储** - AES加密试用信息
- **多重保护** - 注册表+文件双重备份
- **防重置** - 重装系统不会重置试用期

### 🔑 许可证系统
- **多种许可证类型** - 基础版、标准版、专业版、企业版、终身版
- **格式验证** - XXXXX-XXXXX-XXXXX-XXXXX-XXXXX格式
- **机器绑定** - 基于硬件指纹绑定
- **测试许可证** - 内置测试许可证生成功能

### 🖥️ 图形界面
- **现代化设计** - 基于tkinter的专业界面
- **状态显示** - 实时显示试用期和许可证状态
- **进度条** - 可视化试用期使用情况
- **一键启动** - 自动启动Django服务器并打开浏览器

### 🚀 系统控制
- **智能启动** - 自动检测端口占用和环境配置
- **后台运行** - 无控制台窗口，专业用户体验
- **错误处理** - 详细的错误信息和解决建议
- **自动打包** - 支持打包为单文件可执行程序

## 📁 文件结构

```
SchoolAcademicManager GUI启动器/
├── academic_gui_launcher.py      # 主GUI启动器
├── academic_trial_manager.py     # 试用期管理器
├── academic_license_validator.py # 许可证验证器
├── build_academic_gui.py         # 打包脚本
├── install_requirements.py       # 依赖安装脚本
├── test_academic_gui.bat         # 测试脚本
├── README_GUI启动器.md           # 本文档
└── SchoolAcademicManager/        # Django项目目录
    ├── manage.py
    ├── SchoolAcademicManager/
    └── academic/
```

## 🛠️ 安装和使用

### 1. 环境要求
- **操作系统**: Windows 7/8/10/11 (64位)
- **Python**: 3.7或更高版本
- **内存**: 最低2GB，推荐4GB
- **硬盘**: 最低500MB可用空间

### 2. 安装依赖
```bash
# 自动安装所有依赖
python install_requirements.py

# 或手动安装
pip install cryptography wmi pyinstaller
```

### 3. 运行GUI启动器
```bash
# 直接运行
python academic_gui_launcher.py

# 或使用测试脚本
test_academic_gui.bat
```

### 4. 打包为可执行文件
```bash
# 运行打包脚本
python build_academic_gui.py

# 生成的文件在dist目录中
dist/中学教务管理系统.exe
```

## 🧪 测试功能

### 试用期测试
1. 首次运行会自动初始化60天试用期
2. 界面显示剩余天数和进度条
3. 试用期数据加密存储，防篡改

### 许可证测试
1. 点击"生成测试许可证"获取测试密钥
2. 点击"激活软件"输入许可证密钥
3. 支持多种许可证类型测试

### 系统启动测试
1. 确保SchoolAcademicManager目录存在
2. 点击"启动系统"启动Django服务器
3. 自动在浏览器中打开系统页面
4. 默认账户：admin / admin123

## 📋 许可证类型

| 类型 | 名称 | 有效期 | 适用场景 |
|------|------|--------|----------|
| BASIC | 基础版 | 1年 | 小型学校 |
| STANDARD | 标准版 | 2年 | 中型学校 |
| PROFESSIONAL | 专业版 | 3年 | 大型学校 |
| ENTERPRISE | 企业版 | 5年 | 教育集团 |
| LIFETIME | 终身版 | 99年 | 永久使用 |

## 🔧 技术实现

### 试用期管理
- **硬件指纹**: 基于CPU ID、主板序列号、BIOS序列号生成
- **加密算法**: AES-256加密存储试用信息
- **存储方式**: 文件+注册表双重备份
- **防篡改**: 机器ID验证，数据完整性检查

### 许可证验证
- **格式验证**: 正则表达式验证密钥格式
- **类型识别**: 从密钥中解析许可证类型
- **机器绑定**: 许可证与机器ID绑定
- **到期检查**: 自动检查许可证有效期

### GUI界面
- **框架**: tkinter + ttk主题
- **布局**: 响应式布局，支持不同分辨率
- **交互**: 异步操作，避免界面冻结
- **美化**: 现代化控件样式和图标

## 🚨 常见问题

### Q: 试用期可以重置吗？
A: 不可以。试用期基于硬件ID绑定，重装系统或删除文件都不会重置。

### Q: 许可证可以在多台机器使用吗？
A: 不可以。许可证与机器硬件绑定，只能在激活的机器上使用。

### Q: 端口8001被占用怎么办？
A: 关闭占用端口的程序，或修改Django配置使用其他端口。

### Q: 打包后的程序很大怎么办？
A: 这是正常的，因为包含了完整的Python运行时和Django框架。

### Q: 杀毒软件报毒怎么办？
A: 这是误报，可以添加到白名单或联系我们获取数字签名版本。

## 📞 技术支持

### 联系方式
- **邮箱**: <EMAIL>
- **电话**: 400-123-4567
- **QQ群**: 123456789

### 故障排除
1. **启动失败**: 检查Python环境和依赖包
2. **试用期问题**: 查看%APPDATA%\SchoolAcademic目录
3. **许可证问题**: 检查机器ID是否匹配
4. **系统访问**: 确保防火墙允许程序运行

## 📈 版本历史

### v1.0 (2024-12-30)
- ✅ 完整的试用期管理系统
- ✅ 许可证激活和验证
- ✅ 现代化GUI界面
- ✅ 一键打包功能
- ✅ 详细的错误处理

## 🎉 总结

这个GUI启动器为SchoolAcademicManager提供了完整的商业化解决方案：

- **用户友好**: 图形界面操作简单直观
- **安全可靠**: 多重加密和验证机制
- **商业就绪**: 完整的试用期和许可证系统
- **易于分发**: 支持打包为单文件可执行程序
- **专业品质**: 现代化界面和用户体验

立即开始使用，让您的教务管理系统更加专业和易用！ 🚀

---

**© 2024 学校管理系统开发团队**
