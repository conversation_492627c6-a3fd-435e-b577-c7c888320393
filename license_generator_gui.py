#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
许可证生成器 - GUI版本
用于为客户生成单机授权许可证
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import os
from single_machine_license import SingleMachineLicense, create_single_machine_license

class LicenseGeneratorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("许可证生成器 - 中学教务管理系统")
        self.root.geometry("800x700")
        self.license_system = SingleMachineLicense()
        
    def create_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="许可证生成器", 
                               font=("Microsoft YaHei", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="中学教务管理系统 - 单机授权许可证生成工具", 
                                  font=("Microsoft YaHei", 10))
        subtitle_label.pack(pady=(0, 30))
        
        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 生成许可证页面
        self.create_generate_tab(notebook)
        
        # 查看机器信息页面
        self.create_machine_info_tab(notebook)
        
        # 帮助页面
        self.create_help_tab(notebook)
        
    def create_generate_tab(self, notebook):
        """创建生成许可证页面"""
        generate_frame = ttk.Frame(notebook, padding="15")
        notebook.add(generate_frame, text="生成许可证")
        
        # 客户信息框架
        customer_frame = ttk.LabelFrame(generate_frame, text="客户信息", padding="10")
        customer_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(customer_frame, text="客户名称:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.customer_name = tk.StringVar()
        customer_entry = ttk.Entry(customer_frame, textvariable=self.customer_name, width=40)
        customer_entry.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(customer_frame, text="联系方式:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.customer_contact = tk.StringVar()
        contact_entry = ttk.Entry(customer_frame, textvariable=self.customer_contact, width=40)
        contact_entry.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # 许可证参数框架
        license_frame = ttk.LabelFrame(generate_frame, text="许可证参数", padding="10")
        license_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(license_frame, text="目标机器ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.machine_id = tk.StringVar()
        machine_entry = ttk.Entry(license_frame, textvariable=self.machine_id, width=50)
        machine_entry.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(license_frame, text="有效天数:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.days_valid = tk.StringVar(value="365")
        days_entry = ttk.Entry(license_frame, textvariable=self.days_valid, width=20)
        days_entry.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        ttk.Label(license_frame, text="产品名称:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.product_name = tk.StringVar(value="SchoolAcademicManager")
        product_entry = ttk.Entry(license_frame, textvariable=self.product_name, width=40)
        product_entry.grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        
        # 操作按钮框架
        button_frame = ttk.Frame(generate_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        ttk.Button(button_frame, text="生成许可证", 
                  command=self.generate_license).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="保存许可证", 
                  command=self.save_license).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空", 
                  command=self.clear_form).pack(side=tk.LEFT)
        
        # 生成的许可证显示框架
        result_frame = ttk.LabelFrame(generate_frame, text="生成的许可证", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        
        self.license_text = scrolledtext.ScrolledText(result_frame, height=12, width=80, 
                                                     font=("Courier", 9), wrap=tk.WORD)
        self.license_text.pack(fill=tk.BOTH, expand=True)
        
    def create_machine_info_tab(self, notebook):
        """创建查看机器信息页面"""
        machine_frame = ttk.Frame(notebook, padding="15")
        notebook.add(machine_frame, text="机器信息")
        
        ttk.Label(machine_frame, text="当前计算机硬件信息", 
                 font=("Microsoft YaHei", 14, "bold")).pack(pady=(0, 20))
        
        # 获取当前机器信息
        machine_id, fingerprint = self.license_system.get_machine_fingerprint()
        
        info_frame = ttk.LabelFrame(machine_frame, text="机器标识", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        machine_id_text = tk.Text(info_frame, height=2, width=80, font=("Courier", 10))
        machine_id_text.pack(fill=tk.X)
        machine_id_text.insert(tk.END, f"机器ID: {machine_id}")
        machine_id_text.config(state=tk.DISABLED)
        
        fingerprint_frame = ttk.LabelFrame(machine_frame, text="硬件指纹", padding="10")
        fingerprint_frame.pack(fill=tk.BOTH, expand=True)
        
        fingerprint_text = scrolledtext.ScrolledText(fingerprint_frame, height=15, width=80, 
                                                    font=("Courier", 9))
        fingerprint_text.pack(fill=tk.BOTH, expand=True)
        
        for i, fp in enumerate(fingerprint, 1):
            fingerprint_text.insert(tk.END, f"{i:2d}. {fp}\n")
        
        fingerprint_text.config(state=tk.DISABLED)
        
        # 复制按钮
        copy_frame = ttk.Frame(machine_frame)
        copy_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(copy_frame, text="复制机器ID", 
                  command=lambda: self.copy_to_clipboard(machine_id)).pack(side=tk.LEFT)
        
    def create_help_tab(self, notebook):
        """创建帮助页面"""
        help_frame = ttk.Frame(notebook, padding="15")
        notebook.add(help_frame, text="使用帮助")
        
        help_text = """许可证生成器使用说明

1. 生成许可证流程
   ① 获取客户的机器ID（客户运行软件时会显示）
   ② 在"生成许可证"页面填写客户信息和机器ID
   ③ 设置许可证有效期（建议365天）
   ④ 点击"生成许可证"按钮
   ⑤ 将生成的许可证发送给客户

2. 许可证特点
   • 单机绑定：每个许可证只能在一台计算机上激活
   • 硬件绑定：与客户计算机硬件指纹绑定
   • 防盗版：无法复制到其他计算机使用
   • 时间限制：可设置有效期

3. 客户激活流程
   ① 客户首次运行软件
   ② 点击"激活软件"按钮
   ③ 输入您提供的许可证密钥
   ④ 激活成功后软件与硬件绑定

4. 注意事项
   • 机器ID必须准确，否则无法激活
   • 许可证密钥较长，建议通过文件发送
   • 客户更换硬件后需要重新生成许可证
   • 保存好客户信息和许可证记录

5. 技术支持
   • 如有问题请联系技术支持
   • 邮箱: <EMAIL>
   • 电话: 400-123-4567"""
        
        help_text_widget = scrolledtext.ScrolledText(help_frame, wrap=tk.WORD, 
                                                    font=("Microsoft YaHei", 10))
        help_text_widget.pack(fill=tk.BOTH, expand=True)
        help_text_widget.insert(tk.END, help_text)
        help_text_widget.config(state=tk.DISABLED)
        
    def generate_license(self):
        """生成许可证"""
        machine_id = self.machine_id.get().strip()
        if not machine_id:
            messagebox.showwarning("输入错误", "请输入目标机器ID")
            return
        
        try:
            days = int(self.days_valid.get())
            if days <= 0:
                raise ValueError("天数必须大于0")
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的天数")
            return
        
        product = self.product_name.get().strip() or "SchoolAcademicManager"
        
        try:
            # 生成许可证
            license_key = self.license_system.generate_license_key(machine_id, days, product)
            
            # 显示许可证信息
            customer = self.customer_name.get().strip()
            contact = self.customer_contact.get().strip()
            
            license_info = f"""许可证生成成功！

客户信息:
  姓名: {customer or '未填写'}
  联系方式: {contact or '未填写'}

许可证信息:
  目标机器ID: {machine_id}
  产品名称: {product}
  有效期: {days} 天
  许可证类型: 单机授权

许可证密钥:
{license_key}

使用说明:
1. 将上述许可证密钥发送给客户
2. 客户运行软件后点击"激活软件"
3. 输入许可证密钥进行激活
4. 激活后软件与客户计算机绑定

注意: 此许可证只能在指定的计算机上使用，无法转移到其他计算机。"""
            
            self.license_text.delete(1.0, tk.END)
            self.license_text.insert(tk.END, license_info)
            
            # 保存当前许可证密钥
            self.current_license_key = license_key
            
            messagebox.showinfo("成功", "许可证生成成功！")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成许可证失败: {e}")
    
    def save_license(self):
        """保存许可证到文件"""
        if not hasattr(self, 'current_license_key'):
            messagebox.showwarning("警告", "请先生成许可证")
            return
        
        customer = self.customer_name.get().strip() or "客户"
        machine_id = self.machine_id.get().strip()[:16]
        
        filename = f"许可证_{customer}_{machine_id}.txt"
        
        file_path = filedialog.asksaveasfilename(
            title="保存许可证",
            defaultextension=".txt",
            initialvalue=filename,
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                content = self.license_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"许可证已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def clear_form(self):
        """清空表单"""
        self.customer_name.set("")
        self.customer_contact.set("")
        self.machine_id.set("")
        self.days_valid.set("365")
        self.product_name.set("SchoolAcademicManager")
        self.license_text.delete(1.0, tk.END)
        if hasattr(self, 'current_license_key'):
            delattr(self, 'current_license_key')
    
    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        messagebox.showinfo("成功", "已复制到剪贴板")
    
    def run(self):
        """运行程序"""
        self.create_gui()
        self.root.mainloop()

def main():
    """主函数"""
    app = LicenseGeneratorGUI()
    app.run()

if __name__ == '__main__':
    main()
