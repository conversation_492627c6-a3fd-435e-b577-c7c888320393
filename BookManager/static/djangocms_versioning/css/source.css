* {
    box-sizing: border-box;
}

body {
  padding: 0px;
  margin: 0;
}

.texttitle {
    text-align: left;
    padding-top: 20px;
    padding-bottom: 10px;
}
table {
    width: 100%;
    min-width: 480px;
    font-size: 12px;
    line-height: 20px;
    color: #032f62;
    border-collapse: collapse;
    border: 0;
    font-family: "SFMono-Regular",
        <PERSON><PERSON><PERSON>,
        "Liberation Mono",
        Menlo,
        Courier,
        monospace;
}
th {
    min-width: 60px;
    white-space: nowrap;
}
th,
td {
    vertical-align: top;
    padding: 0 10px;
}
th:not(.texttitle) {
    text-align: right;
    color: rgba(27, 31, 35, 0.3);
    font-weight: normal;
}
td + th {
    border-left: 1px solid #f6f8fa;
}
td {
    word-break: break-all;
    white-space: pre-wrap;
    width: 50%;
}
td.replace,
td.delete {
    background: #ffeef0;
}
td.replace ~ td.replace,
.empty + .empty + .replace + .replace,
td.insert {
    background: #e6ffed;
}

th.replace,
th.delete {
    background-color: #ffdce0;
}
del.diff {
    text-decoration: none;
    background-color: #fdb8c0;
    border-radius: 2px;
}
.empty + .empty + .replace,
th.replace ~ th.replace,
th.insert {
    background-color: #cdffd8;
}
ins.diff {
    text-decoration: none;
    background-color: #acf2bd;
    border-radius: 2px;
}
.empty {
    background: #fafbfc;
}

.fold {
    cursor: pointer;
}
.folded td, .folded th {
    /* background-color: #f1f8ff; */
    background-color: #f7f7f7;
}
.folded td {
    color: rgba(0,0,0,0.3);
}
.folded th.fold {
    /* background-color: #dbedff; */
    background-color: #ededed;
}
