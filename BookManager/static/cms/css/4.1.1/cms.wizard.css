/*!
 * @copyright: https://github.com/divio/django-cms
 */:root,:root[data-theme=auto],:root[data-theme=light]{--dca-light-mode:1;--dca-dark-mode:0;--dca-white:#FFFFFF;--dca-black:#000000;--dca-shadow:0,0,0;--dca-primary:#00bbff;--dca-gray:#666;--dca-gray-lightest:#f2f2f2;--dca-gray-lighter:#ddd;--dca-gray-light:#999;--dca-gray-darker:#454545;--dca-gray-darkest:#333;--dca-gray-super-lightest:#f7f7f7;--active-brightness:0.9;--focus-brightness:0.95}:root,:root[data-theme=auto]{color-scheme:dark light}:root[data-theme=light]{color-scheme:light}:root[data-theme=dark]{color-scheme:dark;--dca-light-mode:0;--dca-dark-mode:1;--dca-white:#2A2C2E;--dca-black:#FFF;--dca-primary:#58D1FC;--dca-gray:#ccc;--dca-gray-lightest:#444;--dca-gray-lighter:#666;--dca-gray-light:#888;--dca-gray-darker:#ddd;--dca-gray-darkest:#eee;--dca-gray-super-lightest:#333;--active-brightness:2;--focus-brightness:1.5}@media (prefers-color-scheme:dark){:root:not([data-theme]),:root[data-theme=auto]{--dca-light-mode:0;--dca-dark-mode:1;--dca-white:#2A2C2E;--dca-black:#FFF;--dca-primary:#58D1FC;--dca-gray:#999;--dca-gray-lightest:#444;--dca-gray-lighter:#666;--dca-gray-light:#888;--dca-gray-darker:#aaa;--dca-gray-darkest:#eee;--dca-gray-super-lightest:#333;--active-brightness:2;--focus-brightness:1.5}}.cms-content-wizard .clear{clear:both;overflow:hidden}.cms-content-wizard .errornote{box-sizing:border-box;display:block;width:100%}.cms-content-wizard .choice-wrapper{clear:both;overflow:hidden;margin:-1%}.cms-content-wizard .choice{background-image:none;margin-bottom:0;border-radius:3px;color:var(--dca-gray);background-color:var(--dca-white);border:1px solid var(--dca-gray-lighter);background-clip:padding-box;-webkit-appearance:none;box-sizing:border-box;position:relative;text-transform:none;height:85px;padding:20px 25px!important;margin:1%!important}.cms-content-wizard .choice.focus,.cms-content-wizard .choice:focus,.cms-content-wizard .choice:hover{color:var(--dca-gray);background-color:var(--dca-gray-lightest);border-color:var(--dca-gray-lighter)}.cms-content-wizard .choice.cms-btn-active,.cms-content-wizard .choice:active{color:var(--dca-gray);background-color:var(--dca-white);border-color:var(--dca-gray-lighter);filter:brightness(var(--active-brightness)) opacity(1);box-shadow:inset 0 3px 5px rgba(var(--dca-shadow),.125)}.cms-content-wizard .choice.cms-btn-active.focus,.cms-content-wizard .choice.cms-btn-active:focus,.cms-content-wizard .choice.cms-btn-active:hover,.cms-content-wizard .choice:active.focus,.cms-content-wizard .choice:active:focus,.cms-content-wizard .choice:active:hover{color:var(--dca-gray);background-color:var(--dca-white);border-color:var(--dca-gray-lighter);filter:brightness(calc(var(--focus-brightness) * var(--active-brightness))) opacity(1)}.cms-content-wizard .choice.cms-btn-active,.cms-content-wizard .choice:active{background-image:none}.cms-content-wizard .choice.cms-btn-disabled,.cms-content-wizard .choice.cms-btn-disabled.cms-btn-active,.cms-content-wizard .choice.cms-btn-disabled.focus,.cms-content-wizard .choice.cms-btn-disabled:active,.cms-content-wizard .choice.cms-btn-disabled:focus,.cms-content-wizard .choice.cms-btn-disabled:hover,.cms-content-wizard .choice[disabled],.cms-content-wizard .choice[disabled].cms-btn-active,.cms-content-wizard .choice[disabled].focus,.cms-content-wizard .choice[disabled]:active,.cms-content-wizard .choice[disabled]:focus,.cms-content-wizard .choice[disabled]:hover{background-color:var(--dca-white);border-color:var(--dca-gray-lighter);color:var(--dca-gray-lighter);cursor:not-allowed;box-shadow:none}.cms-content-wizard .choice.cms-btn-disabled.cms-btn-active:before,.cms-content-wizard .choice.cms-btn-disabled.focus:before,.cms-content-wizard .choice.cms-btn-disabled:active:before,.cms-content-wizard .choice.cms-btn-disabled:before,.cms-content-wizard .choice.cms-btn-disabled:focus:before,.cms-content-wizard .choice.cms-btn-disabled:hover:before,.cms-content-wizard .choice[disabled].cms-btn-active:before,.cms-content-wizard .choice[disabled].focus:before,.cms-content-wizard .choice[disabled]:active:before,.cms-content-wizard .choice[disabled]:before,.cms-content-wizard .choice[disabled]:focus:before,.cms-content-wizard .choice[disabled]:hover:before{color:var(--dca-gray-lighter)}@media (min-width:768px){.cms-content-wizard .choice{float:left!important;width:48%!important}}.cms-content-wizard .choice input{position:absolute;left:-9999px;opacity:0;visibility:hidden}.cms-content-wizard .choice strong{display:block;font-size:16px;margin-bottom:3px}.cms-content-wizard .choice .info{display:block;width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.cms-content-wizard .choice.active{border-color:var(--dca-primary);background:rgba(0,187,255,.45)}