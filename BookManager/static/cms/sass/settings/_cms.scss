// #############################################################################
// SETTINGS
@use "sass:math";

$speed-base: 200ms;

// COLORS
$white: var(--dca-white);
$black: var(--dca-black);
:root, :root[data-theme="light"], :root[data-theme="auto"] {
    --dca-light-mode: 1;
    --dca-dark-mode: 0;
    --dca-white: #FFFFFF;
    --dca-black: #000000;
    --dca-shadow: 0, 0, 0;
    --dca-primary: #00bbff;
    --dca-gray: #666;
    --dca-gray-lightest: #f2f2f2;
    --dca-gray-lighter: #ddd;
    --dca-gray-light: #999;
    --dca-gray-darker: #454545;
    --dca-gray-darkest: #333;
    --dca-gray-super-lightest: #f7f7f7;

    --active-brightness: 0.9;
    --focus-brightness: 0.95;
}

:root, :root[data-theme="auto"] {
    color-scheme: dark light;
}

:root[data-theme="light"] {
    color-scheme: light;
}

:root[data-theme="dark"] {
    color-scheme: dark;
    --dca-light-mode: 0;
    --dca-dark-mode: 1;
    --dca-white: #2A2C2E;
    --dca-black: #FFF;
    --dca-primary: #58D1FC;
    --dca-gray: #ccc;
    --dca-gray-lightest: #444;
    --dca-gray-lighter: #666;
    --dca-gray-light: #888;
    --dca-gray-darker: #ddd;
    --dca-gray-darkest: #eee;
    --dca-gray-super-lightest: #333;

    --active-brightness: 2;
    --focus-brightness: 1.5;
}

@media (prefers-color-scheme: dark) {
    :root:not([data-theme]), :root[data-theme="auto"] {
        --dca-light-mode: 0;
        --dca-dark-mode: 1;
        --dca-white: #2A2C2E;
        --dca-black: #FFF;
        --dca-primary: #58D1FC;
        --dca-gray: #ccc;
        --dca-gray-lightest: #444;
        --dca-gray-lighter: #666;
        --dca-gray-light: #888;
        --dca-gray-darker: #ddd;
        --dca-gray-darkest: #eee;
        --dca-gray-super-lightest: #333;

        --active-brightness: 2;
        --focus-brightness: 1.5;
    }
}


$color-primary: var(--dca-primary);
$color-primary-fallback: #00bbff;
$color-success: #693;
$color-danger: #f00;
$color-warning: #c93;

// COLORS gray
$gray:            var(--dca-gray); // lighten(#000, 40%); // #666;
$gray-lightest:   var(--dca-gray-lightest); //lighten($gray, 55%); // #f2f2f2
$gray-lighter:    var(--dca-gray-lighter); //lighten($gray, 46.5%); // #ddd
$gray-light:      var(--dca-gray-light); // // #999
$gray-darker:     var(--dca-gray-darker); // // #454545
$gray-darkest:    var(--dca-gray-darkest); // // #333

// used in pagetree / filer headers
$gray-super-lightest: var(--dca-gray-super-lightest); //;




$gradient-toolbar: to bottom, rgba($gray-lightest, 0.97) 0%, rgba($gray-lightest, 0.97) 50%, rgba($gray-lightest, 0.95) 100%;
$gradient-striped-light: 135deg, rgba($white, .1) 25%, transparent 25%, transparent 50%, rgba($white, .1) 50%, rgba($white, .1) 75%, transparent 75%, transparent;
$gradient-striped-dark: 135deg, rgba($white, .8) 25%, transparent 25%, transparent 50%, rgba($white, .8) 50%, rgba($white, .8) 75%, transparent 75%, transparent;

//##############################################################################
// BASE Variables
$font-size-small: 12px;
$font-size-normal: 14px;
$font-size-large: 16px;

$icon-size: 16px;

$line-height-normal: 20px;

$border-radius-base: 3px;
$border-radius-normal: 5px;

$padding-base: 3px;
$padding-normal: 10px;
$padding-large: 20px;

$screen-mobile: 320px;
$screen-tablet: 768px;
$screen-desktop: 1024px;

//##############################################################################
// z-index map
// the map is built from highest to lowest as it appears on screen
$z-layers: (
    "debug":            99999999,
    "modal": (
        "base":         9999999,
        "footer":       11,
        "body":         10,
        "frame":        10,
        "shim":         20,
        "breadcrumb":   100,
        "buttons":      101,
        "resize":       102,
    ),
    "messages":         999999,
    "toolbar": (
        "base":         9999999,
        "inset-inline-start": 10,
        "inset-inline-end": 10,
    ),
    "sideframe": (
        "base":         999999,
        "buttons":      40,
        "resize":       30,
        // shim-while-resizing: 20
        "frame":        10,
        "shim":         5,
        "dimmer":       1,
    ),
    "dropdown": (
        "base":         9999999,
        "backdrop":     9999990
    ),
    "structure": (
        "base":         9999,
        "content": (
            "base":     100,
            "dragbar":  9999,
            "droppable": 1000,
            "draggable": 99,
            "empty": (
                "addplugin": 3,
                "empty": 2,
                "droppable": 1
            ),
            // special case as parent z-index is changed in js
            "quicksearch": 1000,
            "dropdown":    1002,
        ),
    ),
    // pagetree zindex
    "pagetree": (
        "dropdown":     1000,
        "drag-marker":  102,
        "header":       auto,
        "tree":         auto,
        "section":      auto,
        "fixed-headers": 2,
        "container":    1,
    ),
    // parent dependent, but should be higher than quicksearch, but
    // lower than dropdown
    "hovertooltip":     1001,
    "screenblock":      100,
    // sideframe > sideframe-frame > iframe >
    "dialog":           99999,
    "dialog-dimmer":    9999,
    "base":             9999999,
    "default":          1,
    "below":            -1,
);


//##############################################################################
// BUTTONS
$btn-border-radius-base: $border-radius-base;
$btn-active-shadow: inset 0 3px 5px rgba(var(--dca-shadow), 0.125);

$btn-default-color: $gray;
$btn-default-bgcolor: $white;
$btn-default-border: $gray-lighter;

$btn-action-color: $white;
$btn-action-bgcolor: $color-primary;
$btn-action-border: $color-primary;

$btn-caution-color: $white;
$btn-caution-bgcolor: adjust-hue($color-danger, 15deg);
$btn-caution-border: adjust-hue($color-danger, 15deg);

$btn-active-color: $white;
$btn-active-bgcolor: $gray-darker;
$btn-active-border: $gray-darkest;


//##############################################################################
// Structureboard
$structure-max-width: 980px;
$structure-bgcolor: $gray-super-lightest;

$structure-dragbar-height: 44px;
$structure-dragbar-color: $black;

$structure-dragarea-margin-vertical: 20px;
$structure-dragarea-space: 0px;
$structure-dragarea-padding: 10px;
$structure-dragarea-padding-horizontal: 10px;
$structure-dragarea-use-background: 1;
@if ($structure-dragarea-use-background == 1) {
    $structure-dragarea-padding-horizontal: 28px;
}

$structure-draggable-inner-padding: 30px;
$structure-draggable-space: $structure-dragarea-space;
$structure-draggable-width-while-dragging: 200px;

$structure-dragitem-icon-size: 10px;
$structure-dragitem-height: 26px;
$structure-dragitem-icon-space: 22px + $structure-dragarea-padding-horizontal;
$structure-dragitem-icon-size: 12px;
$structure-dragitem-fullheight: $structure-dragitem-height + 2 * $structure-dragarea-padding;


//##############################################################################
// Structureboard
$submenu-icon-area-size: 36px;
$submenu-icon-size: 16px;
$submenu-icon-color: $gray;
$submenu-right:  math.div($structure-dragarea-padding * 2 + $structure-dragitem-height - $submenu-icon-area-size, 2);

$submenu-item-font-size: $font-size-normal;
$submenu-item-height: $line-height-normal + 6px;
$submenu-item-padding-horizontal: $padding-large;
$submenu-item-color: $gray;
$submenu-item-hover-color: $white;

$submenu-dropdown-bgcolor: $white;

$submenu-quicksearch-icon-size: 14px;
$submenu-quicksearch-padding: $structure-dragarea-padding * 2 + $submenu-quicksearch-icon-size;
$submenu-quicksearch-input-font-size: $font-size-normal - 1px;
$submenu-quicksearch-input-line-height: $submenu-quicksearch-input-font-size;


//##############################################################################
// Tooltip
$tooltip-font-size: $font-size-normal - 2px;
$tooltip-padding: 5px 7px;
$tooltip-line-height: $tooltip-font-size + 2 * nth($tooltip-padding, 1);
$tooltip-icon-area-size: 22px;
$tooltip-icon-size: 14px;
$tooltip-first-line-color: $gray-lighter;
$tooltip-second-line-color: $white;


//##############################################################################
// Toolbar
$toolbar-height: 46px; // has to be even number
$toolbar-shadow: 0 0 5px rgba(var(--dca-shadow), 0.2);
$toolbar-left-space: 15px;
$toolbar-border: $gray-lighter;

$toolbar-debug-height: 3px;
$toolbar-debug-bgcolor: #fad507;

$toolbar-logo-height: 21px;

$toolbar-right-space: 15px;

$toolbar-menu-item-padding: 0 math.div($line-height-normal, 2); // has to be 0 $value;;
$toolbar-menu-item-color: $black;
$toolbar-menu-item-hover-color: $white;
$toolbar-menu-item-hover-bgcolor: $color-primary;

$toolbar-dropdown-padding: 4px 0 3px;
$toolbar-dropdown-shadow: 0 1.5px 1.5px rgba(var(--dca-shadow), 0.4);
$toolbar-dropdown-bgcolor: $white;
$toolbar-dropdown-border-radius: $border-radius-base + 1px;

$toolbar-dropdown-min-width: 180px;
$toolbar-dropdown-item-height: 30px;
$toolbar-dropdown-item-padding: 0 25px 0 15px; // has to be 0 $value;
$toolbar-dropdown-item-icon-size: 10px;

$toolbar-button-height: 30px;
$toolbar-button-font-size: 12px;
$toolbar-button-padding-horizontal: 12px;
$toolbar-button-border-radius: $border-radius-base;


//##############################################################################
// Modal
$modal-header-title-font-size: $font-size-large;
$modal-shadow: 0 0 20px rgba(var(--dca-shadow), .5);
$modal-bgcolor: $white;
$modal-border-radius: $border-radius-normal;

$modal-header-height: $toolbar-height;
$modal-breadcrumb-height: $toolbar-height - 6px;
$modal-footer-height: $toolbar-height;

$modal-header-button-area-size: 30px;

$modal-resize-size: 25px;


//##############################################################################
// Loader
$loader-bgcolor: $white;


//##############################################################################
// Dialog
$dialog-width: 500px;
$dialog-border-radius: $border-radius-normal;
$dialog-shadow: $modal-shadow;


//##############################################################################
// Toolbar messages + screenblock + login form
$messages-width: 300px;
$messages-padding: 6px 10px 8px;
$messages-color: white;
$messages-font-size: 12px;
$messages-line-height: 16px;
$messages-border-radius: $border-radius-base;
$messages-bgcolor: rgba(var(--dca-shadow), 0.74);
$messages-font-weight: 200;
$messages-close-area-size: 20px;
$messages-close-icon-size: 10px;

$screenblock-color: $white;
$screenblock-bgcolor: rgba(var(--dca-shadow), 0.9);
$screenblock-inner-position: 300px;

$login-form-input-font-size: $font-size-normal;
$login-form-input-color: $gray;
$login-form-input-width: 100px;
$login-form-input-height: 28px;
$login-form-input-padding-horizontal: 5px;
$login-form-input-border: $gray-lighter;
$login-form-input-border-radius: $border-radius-base;

$login-form-submit-color: $white;
$login-form-submit-padding-horizontal: 15px;


//##############################################################################
// Sideframe
$sideframe-buttons-position-top: $toolbar-height;
$sideframe-buttons-position-right: 20px;
$sideframe-buttons-offset: 5px;
$sideframe-button-area-size: 32px;
$sideframe-button-icon-size: 12px;
$sideframe-button-space: 2px;
$sideframe-resize-width: 15px;
$sideframe-box-shadow: 10px 0 5px -5px rgba(var(--dca-shadow), 0.2);
$sideframe-dimmer: rgba(var(--dca-shadow), 0.6);


//##############################################################################
// Tooltip
$hover-tooltip-width: 160px;
$hover-tooltip-arrow-height: 6px;
$hover-tooltip-bgcolor: $black;
$hover-tooltip-padding: 6px 8px;


//##############################################################################
// Dropdown
$dropdown-width: 180px;
$dropdown-shadow: 0 0 10px rgba(var(--dca-shadow), 0.25);
$dropdown-item-height: 46px;
$dropdown-item-icon-space: 40px;
$dropdown-item-icon-position: 14px;
$dropdown-arrow-side: 10px;
$dropdown-arrow-position: $submenu-right + math.div($submenu-icon-area-size, 2) - math.div($dropdown-arrow-side, 2);

$dropdown-bg:                  $submenu-dropdown-bgcolor;
$dropdown-border:              transparent;
$dropdown-header-color:        $gray;
$dropdown-link-color:          $submenu-item-color;
$dropdown-link-hover-color:    $submenu-item-hover-color;
$dropdown-link-hover-bg:       $color-primary;
$dropdown-link-active-color:   $submenu-item-hover-color;
$dropdown-link-active-bg:      $color-primary;
$dropdown-link-disabled-color: $gray-light;
$dropdown-link-disabled-bg:    $white;

//##############################################################################
// Add plugin
$quicksearch-height: 40px;


//##############################################################################
// Welcome page
$welcome-width: 60%;
$welcome-min-width: 320px;

