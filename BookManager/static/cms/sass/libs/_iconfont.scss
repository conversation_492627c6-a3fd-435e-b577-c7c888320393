//##############################################################################
// ICONOGRAPHY

// default font file generated by gulp
@font-face {
    font-family: "<%= fontName %>";
    src: url("<%= fontPath %><%= fontName %>.eot");
    src: url("<%= fontPath %><%= fontName %>.eot#iefix") format("eot"),
         url("<%= fontPath %><%= fontName %>.woff2") format("woff2"),
         url("<%= fontPath %><%= fontName %>.woff") format("woff"),
         url("<%= fontPath %><%= fontName %>.ttf") format("truetype"),
         url("<%= fontPath %><%= fontName %>.svg#<%= fontName %>") format("svg");
    font-weight: normal;
    font-style: normal;
}

%icon {
    display: inline-block;
    font: normal normal normal 16px/1 <%= fontName %>;
    text-rendering: auto;
    transform: translate(0, 0);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@function icon-char($filename) {
    $char: "";
<% _.each(glyphs, function(glyph) { %>
    @if $filename == <%= glyph.fileName %> {
        $char: "<%= glyph.codePoint %>";
    }<% }); %>

    @return $char;
}

.cms-icon {
    @extend %icon;
}
@mixin icon($filename, $insert: before) {
    &:#{$insert} {
        content: #{"\"\\"}#{icon-char($filename) + "\""};
    }
}

// #############################################################################
// ICONS:start
// use unicode characters for accessibility reasons and use aria-hidden="true"
// for decorative icons
// DOCS: http://filamentgroup.com/lab/bulletproof_icon_fonts.html

<% _.each(glyphs, function(glyph) { %>
.cms-icon-<%= glyph.fileName %> {
    @include icon(<%= glyph.fileName %>);
}
<% }); %>
