// #############################################################################
// BUTTONS

// taken from bootstrap with adaptations
@function important($important) {
    @if($important == true) {
        @return !important;
    } @else {
        @return unquote('');
    }
}
@mixin button-variant($color, $background, $border, $important: false) {
    background-image: none important($important);
    margin-bottom: 0; // For input.btn
    border-radius: $btn-border-radius-base important($important);
    color: $color important($important);
    background-color: $background important($important);
    border: 1px solid $border important($important);
    background-clip: padding-box;
    -webkit-appearance: none;
    &:focus,
    &.focus,
    &:hover {
        color: $color important($important);
        @if $background == $white {
            background-color: $gray-lightest important($important);
            border-color: $border important($important);
        } @else {
            background-color: $background important($important);
            border-color: $border important($important);
            filter: brightness(var(--focus-brightness)) opacity(1) important($important);
            // Strange: removing opacity(1.) or correcting it makes item transparent
        }
      }
    &:active,
    &.cms-btn-active {
        color: $color important($important);
        background-color: $background important($important);
        border-color: $border important($important);
        filter: brightness(var(--active-brightness)) opacity(1) important($important);
        // Strange: removing opacity(1.) or correcting it makes item transparent
        box-shadow: $btn-active-shadow important($important);

        &:hover,
        &:focus,
        &.focus {
            color: $color important($important);
            background-color: $background important($important);
            border-color: $border important($important);
            filter: brightness(calc(var(--focus-brightness) * var(--active-brightness))) opacity(1) important($important);
        }  // Strange: removing opacity(1.) or correcting it makes item transparent        }
    }
    &:active,
    &.cms-btn-active {
        background-image: none important($important);
    }
    &.cms-btn-disabled,
    &[disabled] {
        &,
        &:hover,
        &:focus,
        &.focus,
        &:active,
        &.cms-btn-active {
            background-color: $background important($important);
            border-color: $border important($important);
            @if $color == $gray {
                color: $gray-lighter important(1);
            } @else {
                color: $color important(1);
                filter: brightness(0.6) opacity(1);  // Strange: removing opacity(1.) or correcting it makes item transparent
            }
            cursor: not-allowed;
            box-shadow: none important($important);
            &:before {
                @if $color == $gray {
                    color: $gray-lighter important(1);
                } @else {
                    color: $color important(1);
                    filter: brightness(0.6) opacity(1); // Strange: removing opacity(1.) or correcting it makes item transparent
                }
            }
        }
    }
}
