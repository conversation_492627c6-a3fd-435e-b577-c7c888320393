@charset "utf-8";
/*!
 * @copyright: https://github.com/divio/django-cms
 */

//##############################################################################
// IMPORT SETTINGS
@import "settings/all";
@import "mixins/all";

//##############################################################################
// IMPORT COMPONENTS
@import "components/reset";
@import "components/general";
@import "components/animation";
@import "components/iconography";
@import "components/hovertooltip";
@import "components/dialog";
@import "components/content";
@import "components/loader";
@import "components/dark-mode-toggle";

// div.cms needs to beat .cms-reset a selectors
div.cms {
    @import "components/tooltip";
    @import "components/button";
    @import "components/dropdown";
    @import "components/toolbar";
    @import "components/modal";
    @import "components/sideframe";
    @import "components/structureboard";
    @import "components/subnav";
    @import "components/clipboard";
    @import "components/pluginpicker";
    @import "components/shortcuts";

    *:not(.cms-modal):focus {
        outline: 2px dotted $gray-darker;
        outline-offset: -3px;

        &::-moz-focus-inner {
            border: 0 !important;
        }
        @media screen and (-webkit-min-device-pixel-ratio: 0) {
            outline: 5px auto -webkit-focus-ring-color;
            outline-offset: -3px;
        }

        .cms-hover-tooltip {
            display: none;
        }
    }

    @import "components/pagetree/node-state";
}

 // Can be removed once suffucently many users have browsers the support `:dir()`
div.cms[dir=rtl] {
    @import "libs/rtl_patch";
}
