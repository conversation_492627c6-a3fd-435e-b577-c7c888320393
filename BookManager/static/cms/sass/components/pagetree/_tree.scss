//##############################################################################
// PAGETREE
@use "sass:math";

// INFO: please note that all related styles to the pagetree itself are
// handled through theming after the "PAGETREE THEME" section in this file.

.cms-admin-sideframe:not(.djangocms-admin-style) {
    margin-block-start: $toolbar-height;
}

// HELPERS
.cms-btn-toolbar {
    display: inline-block;
    line-height: $toolbar-button-height + 2px;
    height: $toolbar-button-height + 2px;
    font-size: $toolbar-button-font-size;
    padding-block: 0;
    padding-inline: $toolbar-button-padding-horizontal + 10px;
}

.cms-hidden,
.hidden {
    display: none !important;
}
.cms-clear {
    clear: both;
    overflow: hidden;
}
.cms-clear-mobile {
    clear: both;
    overflow: hidden;
    @media (min-width: $pagetree-mobile) {
        display: none;
    }
}

// we need to add this to get drag & drop to work on internet explorer,
// more specifically on a microsoft surface
.jstree li {
    -ms-touch-action: none;
    touch-action: none;
}

.cms-pagetree-root#changelist {
    display: block;
    align-items: flex-start;
    justify-content: space-between;
}

//###########################################################
// CONTAINER
// general container around the jstree or elements
.cms-pagetree-root,
.cms-pagetree-root.filtered {
    min-height: 0;
    // important is to fight with admin-style
    padding: 0 !important;
    box-shadow: $pagetree-box-shadow !important;
}
.cms-pagetree {
    border: $pagetree-border;
    // box-shadow: $pagetree-box-shadow;

    .cms-hover-tooltip {
        z-index: auto;
    }
    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        margin: -1px;
        padding: 0;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        border: 0;
    }
    .cms-btn.cms-icon {
        display: inline-block;
        box-sizing: border-box;
        width: $pagetree-cell-inner-height + 7;
        height: $pagetree-cell-inner-height + 5;
        position: relative;
        &:before {
            position: absolute;
            inset-inline-start: 50%;
            inset-block-start: 50%;
            margin-block-start: -8px;
            margin-inline-start: -8px;
        }
    }
    .cms-btn.cms-btn-no-border {
        border: none !important;
    }
    .cms-pagetree-dropdown-menu-open .cms-btn {
        @extend .cms-btn-active;
    }
}

//###########################################################
// HEADER
// handles top header area styles
.cms-pagetree-header {
    position: relative;
    display: flex;
    width: 100%;
    box-sizing: border-box;
    z-index: z(pagetree, header);
    min-height: $pagetree-header-height;
    padding-block: $pagetree-header-padding-block;
    padding-inline: $pagetree-header-padding-inline;
    box-shadow: none;
    border-block-end: none;
    background: $pagetree-header-background;
    &:before {
        content: "";
        position: absolute;
        inset-block-end:  -6px;
        inset-inline-start:0;
        inset-inline-end: 0;
        height: 5px;
        background-color: $white;
    }
    + .module {
        padding-block-start: 0 !important;
    }
    .djangocms-admin-style & {
        margin-block: -40px 0;
        margin-inline: 0;
    }
}
.djangocms-admin-style.cms-admin-sideframe .cms-pagetree-header {
    margin-block-start: -25px;
}

.cms-pagetree-header-title {
    display: table-cell;
    width: 100%;
    line-height: $pagetree-header-height;
    height: $pagetree-header-height;
    h1 {
        line-height: $pagetree-header-height;
        margin: 0 !important;
        span {
            font-weight: normal;
        }
    }
}

.cms-pagetree-header-row {
    display: table;
    width: 100%;
    table-layout: fixed;
    @media (max-width: $pagetree-mobile) {
        display: block;
    }
}
.cms-pagetree-header-cell {
    display: table-cell;
    white-space: nowrap;
    vertical-align: middle;
    @media (max-width: $pagetree-mobile) {
        display: block;
    }
}
.cms-pagetree-header-cell-fluid {
    width: 100%;
}
.cms-pagetree-header-cell-search {
    width: $pagetree-header-search-width !important;
    position: relative;
    .cms-pagetree-header-filter-active & {
        position: static;
    }

    @media (max-width: $pagetree-mobile) {
        width: 100% !important;
    }
}
.cms-pagetree-header-body {
    @media (max-width: $pagetree-mobile) {
        text-align: end;
        padding-block-start: 10px;
    }
}
.cms-pagetree-search-container {
    .cms-pagetree-header-filter-active & {
        position: relative;
    }
}
// SEARCH
.cms-pagetree-header-search {
    position: absolute;
    inset-block-start: 0;
    display: table;
    width: 100%;
    inset-inline-start:0;
    inset-inline-end: 0;
    padding: 0;
    box-shadow: none;
    background: none;
    @media (max-width: $pagetree-mobile) {
        position: relative;
    }
    .cms-pagetree-header-search-btn {
        display: table-cell;
        vertical-align: middle;
        position: relative;
        outline: none;
        color: $white !important;
        padding-block: 8px !important;
        padding-inline: 10px !important;
        border: none !important;
        background: $color-primary !important;
        height: 36px;
        border-radius: $border-radius-base;

        &:hover {
            background: darken($color-primary-fallback, 5%) !important;
        }
        &:active,
        &:focus {
            color: $white !important;
            background: darken($color-primary-fallback, 15%) !important;
        }
    }
    .cms-pagetree-header-filter {
        position: relative;
        display: table-cell;
        width: 100%;
        padding-inline-end: 5px;
        vertical-align: middle;
        input {
            width: 100%;
            box-sizing: border-box;
            width: 100%;
            display: inline-block;
            height: 36px;
            padding: 6px;
            line-height: 24px;
            padding-inline-end: 35px !important;
            transition: width $speed-base;
        }
    }

    .cms-pagetree-header-filter-active & {
        inset-inline-start:0;
        inset-inline-end: 0;
        inset-block-start: 0;
    }

    .djangocms-admin-style & {
        margin-block: 5px 10px;
        margin-inline: 0;
    }
}
.cms-pagetree-header-filter-trigger {
    position: absolute;
    inset-inline-end: 5px;
    inset-block-start: 0;
    cursor: pointer;
    padding: 10px;
    transform: rotate(270deg);
    &:hover .cms-icon,
    &:active .cms-icon,
    &:focus .cms-icon {
        color: $color-primary;
    }
    .cms-icon {
        font-size: 11px;
        color: $gray;
    }
}
.cms-pagetree-header-filter-container {
    display: none;
    overflow: auto;
    position: absolute;
    inset-inline-start:0;
    inset-block-start: 38px;
    width: 100%;
    padding-block: 5px 15px;
    padding-inline: 20px;
    max-height: $pagetree-header-filter-height;
    box-shadow: $pagetree-box-shadow;
    box-sizing: border-box;
    border-radius: $border-radius-base;
    background: $white;
    z-index: z(pagetree, dropdown);
    h3 {
        color: $gray;
        font-size: 14px;
        font-weight: normal;
        text-transform: uppercase;
        padding-block: 10px 0;
        padding-inline: 0;
    }
    ul,
    li {
        list-style-type: none;
        padding-block: 2px;
        padding-inline: 0;
        margin: 0;
    }
    // color states
    li a {
        color: $gray-light;
    }
    .selected a {
        color: $color-primary;
        font-weight: bold;
    }
}
.cms-pagetree-header-search-close {
    position: absolute;
    inset-inline-end: 0;
    inset-block-start: 0;
    padding: 10px;
    &:hover .cms-icon,
    &:active .cms-icon,
    &:focus .cms-icon {
        color: $color-primary;
    }
    .cms-icon {
        font-size: 12px;
        color: $gray;
    }
}
.cms-pagetree-header-search-reset {
    font-size: 90%;
    padding-block: 0;
    padding-inline: $pagetree-header-space;
}

// DROPDOWN
.cms-pagetree-sites-list {
    display: inline-block;
    vertical-align: top;
    padding-inline-start: 10px;
    padding-inline-end: 10px;
    margin-inline-start: $pagetree-header-space;
    margin-inline-end: $pagetree-header-space;

    .cms-pagetree-dropdown-trigger {
        display: inline-block;
        color: $gray !important;
        padding: 10px;
        height: 16px;
    }

    .cms-pagetree-dropdown-menu {
        inset-inline-end: 50%;
        margin-inline-end: math.div(-$pagetree-header-height, 2) - 1px !important;
    }
}

.cms-pagetree-header-create {
    display: inline-block;
    vertical-align: top;
    height: 36px !important;
    box-sizing: border-box;
}

// BREADCRUMB
.djangocms-admin-style .cms-pagetree-breadcrumbs {
    margin-block-end: -30px !important;
    border: none !important;
}

//###########################################################
// SECTION
// separates different navigations
.cms-pagetree-section {
    position: relative;
    z-index: z(pagetree, section);
    min-height: $pagetree-section-height;
    padding-block: $pagetree-section-padding-block;
    padding-inline: $pagetree-section-padding-inline;
    box-shadow: none;
    border-block-end: none;
    background: $white;
    h2,
    p {
        padding: 0;
        margin: 0;
        border: none;
    }
    h2 {
        float: left;
        font-weight: bold;
        color: $gray-darkest;
        background: none;

        &:dir(rtl) {
            // inline-start & inline-end are not widely supported
            float: right;
        }
    }
    .cms-pagetree-container + & {
        margin-block-start: -1px;
    }
}

.cms-pagetree-section-header {
    display: flex;
    flex-direction: row;
    align-items: center;

    select {
        width: 120px !important;
        margin-inline-start: 15px;
    }
}
.cms-pagetree-section-condensed {
    padding-block: $pagetree-section-padding-condensed-block;
    padding-inline: $pagetree-section-padding-condensed-inline;
}
.cms-pagetree-section-nav {
    position: absolute;
    inset-inline-end: 4px;
    inset-block-start: 50%;
    margin-block-start: math.div(-($pagetree-cell-inner-height + 6px), 2);

    .cms-pagetree-dropdown-menu {
        inset-inline-end: 40px;
        inset-block-start: -2px;
        width: 205px;
    }
}

.cms-pagetree-dropdown-separator {
    padding: 0;
    margin-block: 4px !important;
    margin-inline: 1px !important;
    line-height: 1;
    height: 1px;
    background: $gray-lighter;
}

//##############################################################################
// JSTREE
.cms-pagetree-container {
    // overflow: auto;
    position: relative;
    z-index: z(pagetree, tree);
    border: 1px solid $gray-lighter;
    border-block-end: none;
}

.cms-tree-col {
    float: right;
    min-width: $pagetree-cell-inner-height;
    height: $pagetree-cell-inner-height;

    &:dir(rtl) {
        // inline-start & inline-end are not widely supported
        float: left;
    }
}
.cms-tree-col-padding-sm {
    margin-block-start: -3px;
    margin-inline-start: -4px;
    margin-inline-start: -4px;
    height: $pagetree-cell-inner-height + 6px;
}

.cms-tree-item {
    display: table;
    width: 100%;
    text-align: center;
    height: $pagetree-cell-inner-height;
    & + .cms-tree-item {
        padding-inline-start: 4px;
    }

}

.cms-tree-item-disabled {
    opacity: 0.3;
    pointer-events: none;
}

.cms-tree-item-wrapper {
    display: table-row;
    min-width: $pagetree-cell-inner-height;
    .cms-tree-item {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
        min-width: $pagetree-cell-inner-height;
    }
    .cms-tree-item-inner {
        display: block;
    }
}
.cms-tree-item-inner {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
}

.cms-tree-reload {
    float: right;

    &:dir(rtl) {
        // inline-start & inline-end are not widely supported
        float: left;
    }
}

.cms-tree-item-lang .cms-pagetree-dropdown-menu {
    inset-inline-end: 32px;
    inset-block-start: -5px;
    min-width: 180px;
}
.cms-tree-item-button .cms-pagetree-dropdown-menu {
    inset-inline-end: 40px;
    inset-block-start: -2px;
    width: 205px;
}
.cms-tree-item-button .cms-hover-tooltip {
    &:before,
    &:after {
        inset-block-end: math.div($pagetree-cell-inner-height, 2) + 7px;
    }
}
.cms-pagetree-alt-mode .cms-tree-item-button .cms-icon-settings:not(.cms-btn-disabled)[data-url] {
    @include icon(advanced-settings);
}

//###########################################################
// ICONS
// used for the sitemap and apphook icons
.cms-tree-item-icons {
    position: relative;

    .cms-icon {
        display: block;
    }

    span:before {
        color: $gray-light;
    }

    &-dark span:before {
        color: $gray-darker;
    }
}

//###########################################################
// PREVIEW
// shows preview icon
.cms-tree-item-preview {
    position: relative;

    a {
        @extend %icon;
        font-size: $pagetree-icon-size;
        line-height: $pagetree-icon-size;
        height: $pagetree-icon-size;
        display: block;

        &:before {
            display: block;
            color: $gray-darker;
        }
        &:hover:before {
            color: $black;
        }
    }

    // active state for current selected page
    &-active a:before {
        color: $black;
    }
}

//###########################################################
// LANGUAGE
// shows language bubbles
.cms-tree-item-lang {
    // required for tooltip
    position: relative;
    .cms-tree-item-inner > a, .cms-tree-item-inner > .cms-tree-lang-container {
        display: block;
        height: $pagetree-lang-size;
    }
}

//###########################################################
// LANGUAGE
// shows language bubbles
.cms-tree-item-menu {
    position: relative;

    .cms-tree-item-inner > a {
        display: block;
        height: $pagetree-icon-size - 4;
    }

    .cms-icon {
        display: block;
    }

    .cms-icon:before {
        display: block;
        font-size: $pagetree-icon-size - 4;
        color: $gray-darker;
    }
    .cms-icon-check-square:before {
        color: $color-success;
    }
}

//###########################################################
// INFO
// icon used at the end to display additional help
.cms-tree-item-info {
    position: relative;

    .cms-tree-item-inner > a {
        display: block;
        height: $pagetree-icon-size - 2px;
    }

    .cms-icon {
        display: block;
    }
    .cms-icon:before {
        display: block;
        font-size: $pagetree-icon-size - 2px;
    }

    p {
        color: $gray-darkest;
        padding: 0;
        margin: 0;
    }
    strong {
        color: $gray-light;
        font-weight: normal;
    }
    a {
        display: inline-block;
        line-height: 1;
    }
}

//###########################################################
// SEARCH & FILTERING
// searchform or filtering
.cms-tree-search {
    float: left;
    margin-inline-end: 5px;

    &:dir(rtl) {
        // inline-start & inline-end are not widely supported
        float: right;
    }

    label {
        display: none !important;
    }
    select {
        position: relative;
        inset-block-start: 2px;
        width: $pagetree-search-width !important;
    }
    // need to overwrite djangocms-admin-theme
    #toolbar form#changelist-search input[type="submit"] {
        margin-inline-start: 0 !important;
    }
    #toolbar form#changelist-search #searchbar {
        width: $pagetree-search-width !important;
    }
}
.cms-tree-filters {
    float: right;

    &:dir(rtl) {
        // inline-start & inline-end are not widely supported
        float: left;
    }

    .js-cms-tree-filter-trigger {
        position: absolute;
        inset-block-start: 0;
        inset-inline-end: 0;
        padding-inline-end: 20px;
        line-height: 30px;
        width: 175px;
        border-block-end: 1px solid $gray-lighter;
    }
    h2 {
        display: none;
    }
}

//###########################################################
// OTHER

// searchform or filtering
.cms-pagetree-empty {
    padding-block: $pagetree-header-padding-block;
    padding-inline: $pagetree-header-padding-inline;
    border-block-start: none;
    .addlink {
        padding-inline-start: 16px;
    }
}

//##############################################################################
// PAGETREE THEME

// this level is required to make sure djangocms-admin-style or django-admin
// are not interfering (increasing specificity). This section only defines
// resets added through inline styles
.jstree-django-cms {
    // parent and children ul
    .jstree-container-ul,
    .jstree-children,
    // children and children's children li
    .jstree-leaf,
    .jstree-node  {
        list-style-type: none;
        padding: 0;
        margin: 0;
        transform: translateZ(0);
    }

    .jstree-is-dragging {
        opacity: 0.1 !important;
        + .jstree-grid-cell {
            height: $pagetree-cell-height + 1;
            border-block-start: 1px solid $gray-lighter;
            margin-block-start: -1px;
        }
    }
    .jstree-is-dragging-copy {
        opacity: 0.9 !important;
    }

    // grid settings and resets added by `jstree.grid.js`
    .jstree-grid-header,
    .jstree-grid-separator {
        padding: 0;
        margin: 0;
        // there are inline styles set to this part
        // replacing this with margin-inline-start
        // won't do the job!
        margin-left: 0 !important;
        border: none;
        background: none;
    }
    .jstree-grid-header {
        z-index: 1;
        position: relative;
    }
    &.jstree-grid-cell {
        padding: 0;
    }

    // tooltips are hidden if overflow is prevented
    .jstree-grid-column,
    .jstree-grid-cell {
        box-sizing: border-box;
        overflow: visible;
    }

    .jstree-grid-column {
        // fixes layout bug in Safari
        vertical-align: top;
    }

    // disable highlight on iOS devices
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

// wrapper added by jstree
.jstree-grid-wrapper {
    box-sizing: border-box;
    overflow: visible !important;
    width: 100% !important;
}

//###########################################################
// DJANGO-CMS THEME

// general wrapper
.jstree-grid-wrapper {
    background: $gray-lightest;
}

// specificity required for admin styles and jstree resets
.jstree-django-cms {
    .jstree {
    }
    // header column
    .jstree-grid-header {
        font-size: $font-size-small;
        color: $gray;
        text-align: center;
        padding-block: $pagetree-jstree-header-padding-block;
        padding-inline: $pagetree-jstree-header-padding-inline;
        border-block-end: 1px solid $gray-lighter;
        background: $white;
    }
    .jstree-grid-header-fixed {
        position: fixed;
        z-index: z("pagetree", "fixed-headers");
    }
    // ensure first line is not centered
    .jstree-grid-column-0 .jstree-grid-header {
        text-align: start !important;
    }

    //###########################################################
    // styles for nesting elements
    .jstree-children {
        margin-inline-start: $pagetree-nesting-padding;
    }
    .jstree-container-ul {
        margin-inline-start: -1px;
    }
    .jstree-ocl {
        float: left;
        position: relative;
        inset-inline-start: $pagetree-nesting-padding;
        font-size: 12px;
        color: $gray-light;
        padding-block-start: 18px;
        padding-inline-end: 10px;
        padding-block-end: 10px;
        padding-inline-start: 0px;

        &:dir(rtl) {
            // inline-start & inline-end are not widely supported
            float: right;
        }

        @extend %icon;
        &:before {
            // can only apply transforms to non-inline elements
            display: inline-block;
        }
    }
    .jstree-open > .jstree-ocl,
    .jstree-closed > .jstree-ocl {
        cursor: pointer;
    }
    .jstree-open > .jstree-ocl {
        @include icon(arrow-right);
        &:before {
            transform: rotate(90deg);
        }
    }
    .jstree-closed > .jstree-ocl {
        @include icon(arrow-right);
    }
    .jstree-loading > .jstree-ocl {
        @include icon(loader);
        &:before {
            animation: cms-spin 2s infinite linear;
        }
    }

    //###########################################################
    // background color and paddings for nodes
    // first row styling
    .jstree-anchor {
        box-sizing: border-box;
        display: block;
        cursor: move;
        outline: none;
        color: $gray-darkest;
        // we need to add one pixel in to negate the negative margin
        // value to get the borders to work visually
        height: $pagetree-cell-height + 1;
        padding-block: $pagetree-anchor-padding-block;
        padding-inline: $pagetree-anchor-padding-inline;
        margin-block-start: -1px;
        border-block: 1px solid $gray-lighter;
        border-inline: 1px solid $gray-lighter;
        border-inline-end: none;
        background-color: $white;
        background-image: url('../../img/pagetree/tree-li-drag.gif');
        background-repeat: no-repeat;
        background-position: 2px center;
        image-rendering: pixelated;
        white-space: normal;

        &:hover {
            color: $gray !important;
        }

        &:dir(rtl) {
            // no direct logical property for background-position
            background-position: calc(100% - 2px) center;
        }

        em {
            color: $gray-light;
        }
    }

    .jstree-node[data-move-permission="false"] > .jstree-anchor {
        cursor: default;
        background-image: none;
    }

    .jstree-open,
    .jstree-closed,
    .jstree-loading {
        > .jstree-anchor {
            padding-inline: $pagetree-anchor-padding-inline-with-icon;
            padding-block: $pagetree-anchor-padding-block-with-icon;
        }
    }

    .cms-tree-node-filtered .jstree-anchor {
        cursor: default;
    }
    // style cells afterwards
    .jstree-grid-cell {
        height: $pagetree-cell-height;
        border-block-end: 1px solid $gray-lighter;
        background: $white;
        > span {
            display: block;
        }
        // don't apply these styles for the first column
        &.jstree-django-cms {
            height: auto;
            border: none;
            background: none;
        }
    }
    .jstree-grid-cell-regular {
        text-align: center;
        padding-block: $pagetree-cell-padding-block;
        padding-inline: $pagetree-cell-padding-inline;
        border-inline-start: 1px solid $gray-lighter;
    }
    .jstree-grid-cell-regular-options {
        border-inline-start: none;
        text-align: end;
    }
    // hover states
    .jstree-hovered,
    .jstree-clicked {
        background-color: $gray-lightest !important;
    }

    // special case for paste header
    .jstree-grid-column-1 .jstree-grid-header {
        text-align: end;
        a {
            font-size: $font-size-small;
            inset-block-start: 0;
        }
        span {
            line-height: 1;
        }
        // special icon for pasting
        .cms-icon {
            font-size: $font-size-small;
            padding-inline-end: $pagetree-icon-offset;
            margin-inline-start: $pagetree-icon-offset;
        }
    }
}

//###########################################################
// initial loading display of the tree
.jstree.jstree-loading {
    .jstree-container-ul {
        margin: 0;
    }
    .jstree-anchor {
        padding-block-start: 13px;
        padding-inline-start: 15px;
        border: none;
        background: none;
    }

    .jstree-loading > .jstree-ocl {
        display: none;
    }
}

//###########################################################
// success animation and paste functionality
// this should only be attached to the root
.cms-tree-node-success {
    > .jstree-anchor .jstree-themeicon {
        content: "";
        position: relative;
        inset-block-start: 4px;
        color: $color-success;
        @extend %icon;
        @include icon(check);
    }
}

//###########################################################
// shared pages look
.jstree-django-cms {
    $bg-shared: #f2f2f2;
    .cms-tree-node-shared-true {
        > .jstree-anchor {
            border-style: dashed;
            border-inline-end: none;
            color: #999;
            background-color: $bg-shared !important;

            &:hover {
                color: #999 !important;
            }

            &.jstree-hovered,
            &.jstree-clicked {
                background-color: $bg-shared !important;
            }
        }
    }

    .jstree-grid-cell.jstree-hovered,
    .jstree-grid-cell.jstree-clicked {
        background-color: $bg-shared;
    }

    .cms-tree-node-shared-false {
        > .jstree-anchor {
            border-style: solid;
            border-inline-end: none;
            background-color: $white !important;
            border-block-end: $pagetree-border;

            &.jstree-hovered,
            &.jstree-clicked {
                background-color: $gray-lightest !important;
            }
        }

        + .cms-tree-node-shared-true {
            > .jstree-anchor {
                border-block-start-style: solid;
            }
            &.jstree-hovered,
            &.jstree-clicked {
                background-color: $bg-shared;
            }
        }
    }

    .cms-page-tree-cell-shared--true {
        border-style: dashed;
        background-color: #f2f2f2;

        &.jstree-grid-cell-regular-options {
            border-inline-start: none;
        }

        .cms-btn-no-border {
            background-color: transparent !important;
            &:hover {
                background-color: #fff !important;
                border-block: 1px solid #ddd !important;
                border-inline: 1px solid #ddd !important;
            }
        }

        &.jstree-hovered,
        &.jstree-clicked {
            background-color: $bg-shared !important;
        }
    }

    .cms-page-tree-cell-shared--false {
        background-color: $white;

        &.jstree-hovered,
        &.jstree-clicked {
            background-color: $gray-lightest !important;
          }
    }

    .cms-page-tree-cell-shared--true + .cms-page-tree-cell-shared--false {
        border-block-start: $pagetree-border;
        margin-block-start: -1px;
        padding-block-start: 8px;
        height: 47px;
    }

}

//###########################################################
// drag & drop item
#jstree-dnd {
    box-sizing: border-box;
    display: block;
    min-width: 300px;
    height: $pagetree-cell-height;
    padding-block: $pagetree-anchor-padding-block !important;
    padding-inline: $pagetree-anchor-padding-inline !important;
    margin-inline-start: -$pagetree-marker-size + 1;
    background-color: $color-primary !important;
    color: $white;
    // copy icon triggered when pressing `CMD`
    .jstree-copy {
        color: $white;
        padding-block: 0px 2px;
        padding-inline: 5px;
        margin-inline-start: 5px;
        border-radius: $border-radius-base;
        background: $gray-light;
    }
    // error color displayed if we can/cannot move it
    .jstree-ok,
    .jstree-er {
        position: absolute;
        inset-block-start: 0;
        inset-inline-start: -5px;
        inset-block-end:  0;
        inset-inline-end: 0;
        background-color: rgba(white, 0.6);
    }
    .jstree-ok {
        display: none;
    }

}
.is-stacked {
    #jstree-dnd  {
        box-shadow: 1px 1px 1px rgba($black, 0.2), 4px 4px 0 0 $color-primary;
    }
    .jstree-er {
        box-shadow: 4px 4px 0 0 rgba(white, 0.6);
    }
}

#jstree-marker {
    position: absolute;
    z-index: z("pagetree", "drag-marker");
    margin-inline-start: -($pagetree-marker-size);
    margin-block-start: -($pagetree-marker-size);
    border-inline-end: 0;
    border-block-start: $pagetree-marker-size solid transparent;
    border-block-end: $pagetree-marker-size solid transparent;
    border-inline-start: $pagetree-marker-size solid $color-primary;
    height: 0;
    &:after {
        content: '';
        display: block;
        position: absolute;
        height: 3px;
        inset-block-start: -1px;
        inset-inline-start: -($pagetree-marker-size);
        inset-inline-end: 0px;
        background-color: $color-primary;
    }
    &.jstree-marker-child {
        border: 2px solid $color-primary;
        height: 46px;
        margin-block: -24px 0px;
        margin-inline: 0;
        background-color: rgba($color-primary, 0.1);
        box-sizing: border-box;

        &:after {
            display: none;
        }
    }
}


.djangocms-admin-style.cms-pagetree-wrapper.cms-admin-sideframe {
    .messagelist + #content {
        margin-block-start: 117px !important;
    }
    #container .breadcrumbs {
        inset-inline-start: 20px;
    }
    #container .breadcrumbs + .messagelist + #content,
    #container .breadcrumbs + #content {
        margin-inline-start: 0 !important;
        margin-inline-end: 0 !important;
    }
    #jstree-marker {
        inset-inline-end: 0;
    }
}

.cms-pagetree-header-lang {
    &,
    &:link,
    &:active,
    &:hover,
    &:focus {
        color: $gray-darkest !important;
    }
    &.active {
        font-weight: bold;
    }
}
