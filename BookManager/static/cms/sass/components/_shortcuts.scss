.cms-shortcuts {
    padding: 0 20px;

    h2 {
        padding-top: 20px;
        padding-left: 8px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    margin-bottom: 20px;
}

.cms-shortcut {
    clear: both;
}

.cms-shortcut-key-wrapper {
    width: 100px;
    text-align: right;
    vertical-align: top;
    padding-bottom: 5px;
    white-space: nowrap;
}
.cms-shortcut-key {
    display: inline-block;
    background-color: $gray-lightest;
    padding: 3px 6px;
    border-radius: 3px;
    font-family: 'Operator Mono', 'Menlo', 'Monaco', 'Consolas', 'monospace';
    border-top: 1px solid var(--dca-gray-super-lightest);
    box-shadow:
        inset 0 0 25px var(--dca-gray-lightest),
        0 1px 0 var(--dca-gray-light),
        0 2px 0 var(--dca-gray-light),
        0 2px 3px var(--dca-gray-darkest);
    text-shadow: 0px 1px 0px var(--dca-gray-super-lightest);
}

.cms-shortcut-desc {
    padding: 3px 10px;
    width: 100%;
    padding-bottom: 5px;
}
