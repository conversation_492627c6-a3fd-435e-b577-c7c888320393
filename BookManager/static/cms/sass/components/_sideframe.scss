//##############################################################################
// SIDEFRAME

@use "sass:math";

// "Pages" frame on the left
.cms-sideframe {
    display: none;
    position: fixed;
    top: 0;
    inline-start: 0;
    width: 0;
    bottom: 0;
    z-index: z(sideframe, base);

    @media (max-width: $screen-tablet) {
        width: 100% !important;
    }
}

.cms-sideframe-frame {
    position: absolute;
    left: 0;
    top: 0;
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
    z-index: z(sideframe, frame);
    width: 100%;
    height: 100%;
    background: $white;
    -webkit-backface-visibility: hidden;

    iframe {
        // even though on a touch device we actually scroll cms-sideframe-frame
        // this is still required
        -webkit-overflow-scrolling: touch;
        overflow-y: auto;
        transform: translateZ(0);
        display: block;
        width: 100%;
        height: 100%;
        border: none;
        background: $white;
    }
}

// when resizing the sideframe shim gets z-index between frame
// and resize in order for mouse events not to trigger on iframe, same as
// with modal
.cms-sideframe-shim {
    position: absolute;
    top: 0;
    left: 0;
    z-index: z(sideframe, shim);
    width: 100%;
    height: 100%;
    background: transparent;
}

.cms-sideframe-controls {
    position: absolute;
    top: $sideframe-buttons-position-top + $sideframe-buttons-offset;
    inset-inline-end: $sideframe-buttons-position-right;
    z-index: z(sideframe, buttons);
    box-shadow: $toolbar-shadow;
    background-color: $white;
    border-radius: $border-radius-base;
    border: 1px solid $gray-lighter;
}
&.cms-toolbar-debug .cms-sideframe-controls {
    top: $sideframe-buttons-position-top + $sideframe-buttons-offset + $toolbar-debug-height;
}
.cms-sideframe-close,
.cms-sideframe-history {
    display: inline-block;
    position: relative;
    cursor: pointer;
    vertical-align: top;
    font-size: $sideframe-button-icon-size;
    color: $gray-darker;
    text-align: center;
    width: $sideframe-button-area-size;
    height: $sideframe-button-area-size;

    span {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -(math.div($sideframe-button-area-size, 4));
        margin-top: -(math.div($sideframe-button-area-size, 4));

        &:hover {
            color: $gray-darkest;
        }
    }
}

.cms-sideframe-history {
    width: $sideframe-button-area-size * 2 + 1px;
    border-right: 1px solid $gray-lighter;

    .cms-icon {
        float: left;
        position: relative;
        left: 0;
        top: 0;
        padding: 8px 4px 8px 12px;
        margin: 0;
    }
    .cms-icon-arrow-forward {
        @include icon(arrow-right);
        padding: 8px 12px 8px 4px;
    }
    // disabled state if there is no history
    .cms-icon-disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.cms-sideframe-dimmer {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: z(sideframe, dimmer);
    width: 100%;
    height: 100%;
    background: $sideframe-dimmer;
}
