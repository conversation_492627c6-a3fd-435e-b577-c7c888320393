//##############################################################################
// ICONOGRAPHY

// default font file generated by gulp
@font-face {
    font-family: "django-cms-iconfont";
    src: url("../../fonts/4.1.1/django-cms-iconfont.eot");
    src: url("../../fonts/4.1.1/django-cms-iconfont.eot#iefix") format("eot"),
         url("../../fonts/4.1.1/django-cms-iconfont.woff2") format("woff2"),
         url("../../fonts/4.1.1/django-cms-iconfont.woff") format("woff"),
         url("../../fonts/4.1.1/django-cms-iconfont.ttf") format("truetype"),
         url("../../fonts/4.1.1/django-cms-iconfont.svg#django-cms-iconfont") format("svg");
    font-weight: normal;
    font-style: normal;
}

%icon {
    display: inline-block;
    font: normal normal normal 16px/1 django-cms-iconfont;
    text-rendering: auto;
    transform: translate(0, 0);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@function icon-char($filename) {
    $char: "";

    @if $filename == advanced-settings {
        $char: "E001";
    }
    @if $filename == alias {
        $char: "E002";
    }
    @if $filename == apphook {
        $char: "E003";
    }
    @if $filename == archive {
        $char: "E004";
    }
    @if $filename == arrow-right {
        $char: "E005";
    }
    @if $filename == arrow-wide {
        $char: "E006";
    }
    @if $filename == arrow {
        $char: "E007";
    }
    @if $filename == bin {
        $char: "E008";
    }
    @if $filename == broadcast-off {
        $char: "E009";
    }
    @if $filename == broadcast-on {
        $char: "E00A";
    }
    @if $filename == check-circle {
        $char: "E00B";
    }
    @if $filename == check-o {
        $char: "E00C";
    }
    @if $filename == check-square {
        $char: "E00D";
    }
    @if $filename == check {
        $char: "E00E";
    }
    @if $filename == close {
        $char: "E00F";
    }
    @if $filename == cogs {
        $char: "E010";
    }
    @if $filename == comment {
        $char: "E011";
    }
    @if $filename == compare {
        $char: "E012";
    }
    @if $filename == copy {
        $char: "E013";
    }
    @if $filename == cut {
        $char: "E014";
    }
    @if $filename == edit-new {
        $char: "E015";
    }
    @if $filename == edit {
        $char: "E016";
    }
    @if $filename == eye {
        $char: "E017";
    }
    @if $filename == forbidden {
        $char: "E018";
    }
    @if $filename == handler {
        $char: "E019";
    }
    @if $filename == highlight {
        $char: "E01A";
    }
    @if $filename == home {
        $char: "E01B";
    }
    @if $filename == info {
        $char: "E01C";
    }
    @if $filename == layers {
        $char: "E01D";
    }
    @if $filename == list-ol {
        $char: "E01E";
    }
    @if $filename == loader {
        $char: "E01F";
    }
    @if $filename == lock {
        $char: "E020";
    }
    @if $filename == logo {
        $char: "E021";
    }
    @if $filename == manage-versions {
        $char: "E022";
    }
    @if $filename == menu {
        $char: "E023";
    }
    @if $filename == minimize {
        $char: "E024";
    }
    @if $filename == minus-circle {
        $char: "E025";
    }
    @if $filename == minus-square-o {
        $char: "E026";
    }
    @if $filename == minus-square {
        $char: "E027";
    }
    @if $filename == minus {
        $char: "E028";
    }
    @if $filename == moderate {
        $char: "E029";
    }
    @if $filename == paste {
        $char: "E02A";
    }
    @if $filename == pencil {
        $char: "E02B";
    }
    @if $filename == pin {
        $char: "E02C";
    }
    @if $filename == plugins {
        $char: "E02D";
    }
    @if $filename == plus-circle {
        $char: "E02E";
    }
    @if $filename == plus-square-o {
        $char: "E02F";
    }
    @if $filename == plus {
        $char: "E030";
    }
    @if $filename == publish {
        $char: "E031";
    }
    @if $filename == puzzle {
        $char: "E032";
    }
    @if $filename == redo {
        $char: "E033";
    }
    @if $filename == rename {
        $char: "E034";
    }
    @if $filename == scissors {
        $char: "E035";
    }
    @if $filename == search {
        $char: "E036";
    }
    @if $filename == settings {
        $char: "E037";
    }
    @if $filename == sitemap {
        $char: "E038";
    }
    @if $filename == squares {
        $char: "E039";
    }
    @if $filename == theme-auto {
        $char: "E03A";
    }
    @if $filename == theme-dark {
        $char: "E03B";
    }
    @if $filename == theme-light {
        $char: "E03C";
    }
    @if $filename == undo {
        $char: "E03D";
    }
    @if $filename == unlock {
        $char: "E03E";
    }
    @if $filename == unpublish {
        $char: "E03F";
    }
    @if $filename == view {
        $char: "E040";
    }
    @if $filename == window {
        $char: "E041";
    }

    @return $char;
}

.cms-icon {
    @extend %icon;
}
@mixin icon($filename, $insert: before) {
    &:#{$insert} {
        content: #{"\"\\"}#{icon-char($filename) + "\""};
    }
}

// #############################################################################
// ICONS:start
// use unicode characters for accessibility reasons and use aria-hidden="true"
// for decorative icons
// DOCS: http://filamentgroup.com/lab/bulletproof_icon_fonts.html


.cms-icon-advanced-settings {
    @include icon(advanced-settings);
}

.cms-icon-alias {
    @include icon(alias);
}

.cms-icon-apphook {
    @include icon(apphook);
}

.cms-icon-archive {
    @include icon(archive);
}

.cms-icon-arrow-right {
    @include icon(arrow-right);
}

.cms-icon-arrow-wide {
    @include icon(arrow-wide);
}

.cms-icon-arrow {
    @include icon(arrow);
}

.cms-icon-bin {
    @include icon(bin);
}

.cms-icon-broadcast-off {
    @include icon(broadcast-off);
}

.cms-icon-broadcast-on {
    @include icon(broadcast-on);
}

.cms-icon-check-circle {
    @include icon(check-circle);
}

.cms-icon-check-o {
    @include icon(check-o);
}

.cms-icon-check-square {
    @include icon(check-square);
}

.cms-icon-check {
    @include icon(check);
}

.cms-icon-close {
    @include icon(close);
}

.cms-icon-cogs {
    @include icon(cogs);
}

.cms-icon-comment {
    @include icon(comment);
}

.cms-icon-compare {
    @include icon(compare);
}

.cms-icon-copy {
    @include icon(copy);
}

.cms-icon-cut {
    @include icon(cut);
}

.cms-icon-edit-new {
    @include icon(edit-new);
}

.cms-icon-edit {
    @include icon(edit);
}

.cms-icon-eye {
    @include icon(eye);
}

.cms-icon-forbidden {
    @include icon(forbidden);
}

.cms-icon-handler {
    @include icon(handler);
}

.cms-icon-highlight {
    @include icon(highlight);
}

.cms-icon-home {
    @include icon(home);
}

.cms-icon-info {
    @include icon(info);
}

.cms-icon-layers {
    @include icon(layers);
}

.cms-icon-list-ol {
    @include icon(list-ol);
}

.cms-icon-loader {
    @include icon(loader);
}

.cms-icon-lock {
    @include icon(lock);
}

.cms-icon-logo {
    @include icon(logo);
}

.cms-icon-manage-versions {
    @include icon(manage-versions);
}

.cms-icon-menu {
    @include icon(menu);
}

.cms-icon-minimize {
    @include icon(minimize);
}

.cms-icon-minus-circle {
    @include icon(minus-circle);
}

.cms-icon-minus-square-o {
    @include icon(minus-square-o);
}

.cms-icon-minus-square {
    @include icon(minus-square);
}

.cms-icon-minus {
    @include icon(minus);
}

.cms-icon-moderate {
    @include icon(moderate);
}

.cms-icon-paste {
    @include icon(paste);
}

.cms-icon-pencil {
    @include icon(pencil);
}

.cms-icon-pin {
    @include icon(pin);
}

.cms-icon-plugins {
    @include icon(plugins);
}

.cms-icon-plus-circle {
    @include icon(plus-circle);
}

.cms-icon-plus-square-o {
    @include icon(plus-square-o);
}

.cms-icon-plus {
    @include icon(plus);
}

.cms-icon-publish {
    @include icon(publish);
}

.cms-icon-puzzle {
    @include icon(puzzle);
}

.cms-icon-redo {
    @include icon(redo);
}

.cms-icon-rename {
    @include icon(rename);
}

.cms-icon-scissors {
    @include icon(scissors);
}

.cms-icon-search {
    @include icon(search);
}

.cms-icon-settings {
    @include icon(settings);
}

.cms-icon-sitemap {
    @include icon(sitemap);
}

.cms-icon-squares {
    @include icon(squares);
}

.cms-icon-theme-auto {
    @include icon(theme-auto);
}

.cms-icon-theme-dark {
    @include icon(theme-dark);
}

.cms-icon-theme-light {
    @include icon(theme-light);
}

.cms-icon-undo {
    @include icon(undo);
}

.cms-icon-unlock {
    @include icon(unlock);
}

.cms-icon-unpublish {
    @include icon(unpublish);
}

.cms-icon-view {
    @include icon(view);
}

.cms-icon-window {
    @include icon(window);
}
