{"version": 3, "sources": ["admin_filer.scss", "components/_base.scss", "settings/_custom.scss", "components/_image-info.scss", "mixins/_custom.scss", "components/_action-list.scss", "components/_filter-files.scss", "components/_navigator.scss", "components/_modal.scss", "components/_drag-and-drop.scss", "components/_tooltip.scss", "libs/_dropzone.scss"], "names": [], "mappings": "AAAA;;EAAA,CCGA,UAEI,eAAA,CACA,sBAAA,CAGJ,WACI,eAAA,CAEJ,YACI,gBAAA,CAEJ,gBACI,UAAA,CACA,aAAA,CACA,UAAA,CAEJ,wBACI,qBAAA,CAEJ,uBACI,uBAAA,CAIJ,MACI,yBAAA,CACA,qEAAA,CAGJ,WACI,iBAAA,CAEA,cCLc,CDMd,qEAAA,CACA,iBAAA,CACA,kBAAA,CACA,gBAAA,CACA,mBAAA,CACA,6BAAA,CAAA,qBAAA,CACA,iDCrCI,CDsCJ,gBAEI,cCfU,CDgBV,qEAAA,CAEJ,oBACI,WAAA,CAIR,yBACI,YAAA,CAEJ,uBACI,YAAA,CAKQ,iJAEI,wBAAA,CACA,yBAAA,CAEJ,yDACI,iBAAA,CACA,4BAAA,CAIZ,wBACI,cAAA,CACA,iCACI,SAAA,CACA,2CAAA,CAAA,mCAAA,CAEJ,yGAEI,yBAAA,CAGR,8BACI,iBAAA,CACA,OAAA,CAGJ,gBACI,YAAA,CAEJ,2BACI,uDCxFA,CD4FR,cACI,UAAA,CAGJ,wBAEI,oBAAA,CACA,oBAAA,CAGJ,kBACI,YAAA,CACA,YAAA,CAEJ,aACI,WAAA,CACA,YAAA,CAGJ,iBACI,UAAA,CACA,gBAAA,CAGJ,kBACI,sBAAA,CACA,8BAAA,CACA,iBAAA,CAGJ,SACI,iBAAA,CACA,SAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CACA,eAAA,CACA,qBAAA,CACA,QAAA,CAGJ,QACI,uBAAA,CAGJ,gBACI,eAAA,CACA,yBAAA,CACA,iBAAA,CACA,+CAAA,CAAA,uCAAA,CACA,uDC9II,CDiJR,8FAGI,cAAA,CACA,aAAA,CACA,YAAA,CAGJ,yBACI,uBAAA,CE7JJ,YACI,iBAAA,CACA,WAAA,CACA,6BAAA,CAAA,qBAAA,CACA,SAAA,CACA,YAAA,CACA,QAAA,CACA,iBAAA,CACA,iDDLI,CCMJ,2CAAA,CAAA,mCAAA,CACA,qDAEI,QAAA,CACA,SAAA,CACA,iFACI,aAAA,CACA,cAAA,CAEJ,2DACI,oBAAA,CAEJ,yDACI,cAAA,CAGR,8BAEI,eAAA,CACA,UAAA,CACA,UAAA,CACA,kBAAA,CACA,YAAA,CACA,eAAA,CC9BJ,yEAEI,WAAA,CACA,aAAA,CAEJ,oCACI,UAAA,CD0BA,0DACI,eAAA,CACA,iBAAA,CACA,8BAAA,CACA,iEACI,YAAA,CAGR,kDACI,UAAA,CAEJ,mDACI,UAAA,CACA,gBAAA,CAEJ,yFAEI,YAAA,CACA,mBAAA,CACA,qHACI,eAAA,CACA,kBAAA,CACA,SAAA,CAEJ,+FACI,UAAA,CACA,0DD3CE,CC4CF,cAAA,CAEA,2BAAA,CACA,kBAAA,CAEA,YAAA,CAEJ,+FACI,oDDvDE,CCwDF,cAAA,CAEA,2BAAA,CACA,iBAAA,CAEA,iBAAA,CAEJ,qGACI,cAAA,CACA,iBAAA,CACA,mHACI,cAAA,CAGR,+FACI,oDDvEE,CCwEF,yBAAA,CACA,6BAAA,CACA,wBAAA,CACA,mBAAA,CAEJ,6FACI,SAAA,CAGR,gDACI,eAAA,CACA,oDDnFM,CCoFN,kBAAA,CACA,sBAAA,CACA,eAAA,CACA,sDACI,UAAA,CACA,gBAAA,CAGR,uDACI,eAAA,CACA,eAAA,CACA,SAAA,CACA,2DACI,kBAAA,CAIJ,kDACI,cAAA,CACA,6DACI,UAAA,CAKhB,qCA3HJ,YA4HQ,UAAA,CACA,UAAA,CAEI,qGAEI,UAAA,CACA,aAAA,CAAA,CAMhB,kBACI,iBAAA,CACA,SAAA,CACA,UAAA,CACA,cAAA,CACA,cAAA,CAGJ,kBACI,gBAAA,CACA,0EAAA,CACA,oBACI,eAAA,CAIR,yBACI,iBAAA,CACA,aAAA,CACA,cAAA,CACA,wCACI,oBAAA,CACA,iBAAA,CACA,kBAAA,CACA,uEAAA,CACA,8HAAA,CACA,4CACI,aAAA,CAGR,8CACI,iBAAA,CACA,KAAA,CACA,OAAA,CACA,QAAA,CACA,MAAA,CACA,eAAA,CAEJ,+CACI,iBAAA,CACA,SAAA,CACA,UAAA,CACA,WAAA,CACA,oBAAA,CACA,YAAA,CACA,kBAAA,CACA,WAAA,CACA,+BAAA,CAEJ,8DACI,kBAAA,CACA,0EACI,YAAA,CAKZ,sBACI,iBAAA,CACA,iBAAA,CElMA,yBACI,aAAA,CACA,gBAAA,CAEJ,mCACI,oBAAA,CAEJ,oCACI,YAAA,CAEJ,oCACI,0EAAA,CACA,gDACI,YAAA,CAEJ,iDACI,oBAAA,CAIZ,cACI,uEAAA,CACA,yBACI,eAAA,CACA,2BACI,kBAAA,CAGR,gBACI,aAAA,CACA,cAAA,CACA,gBAAA,CACA,0EAAA,CAGA,gCACI,UAAA,CAEJ,+BACI,WAAA,CACA,cAAA,CAIZ,wBACI,oBAAA,CACA,QAAA,CACA,cAAA,CACA,qCAJJ,wBAKQ,UAAA,CACA,aAAA,CAAA,CAEJ,2BACI,oBAAA,CACA,gBAAA,CACA,qBAAA,CACA,cAAA,CACA,eAAA,CACA,qCACI,uCACI,cAAA,CAAA,CAGR,gCACI,qBAAA,CAEJ,6BACI,oDHtDM,CGyDd,oCACI,cAAA,CCvEJ,2CACI,eAAA,CAEJ,kDACI,iBAAA,CACA,KAAA,CACA,MAAA,CACA,OAAA,CACA,qCALJ,kDAMQ,eAAA,CAAA,CAGR,0CACI,iBAAA,CAEJ,uEACI,eAAA,CAIR,wBAEI,kBAAA,CACA,qBAAA,CACA,iBAAA,CACA,WAAA,CACA,QAAA,CACA,SAAA,CACA,eAAA,CACA,uBAAA,CAAA,eAAA,CACA,YAAA,CF7BA,6DAEI,WAAA,CACA,aAAA,CAEJ,8BACI,UAAA,CEwBJ,qCAXJ,wBAYQ,aAAA,CACA,UAAA,CACA,cAAA,CACA,eAAA,CACA,6CACI,UAAA,CAAA,CAGR,kDACI,iBAAA,CACA,KAAA,CACA,OAAA,CAEI,0OAII,oBAAA,CACA,gBAAA,CACA,iBAAA,CACA,UAAA,CACA,WAAA,CACA,SAAA,CAGR,uFACI,aAAA,CACA,uBAAA,CACA,0FACI,QAAA,CACA,SAAA,CACA,oBAAA,CAIZ,+CACI,iBAAA,CACA,UAAA,CACA,gBAAA,CACA,uBAAA,CACA,gBAAA,CACA,qCANJ,+CAOQ,UAAA,CAAA,CAEJ,8EACI,2BAAA,CACA,sBAAA,CAGR,6CACI,WAAA,CACA,iBAAA,CACA,kBAAA,CACA,WAAA,CACA,QAAA,CACA,sBAAA,CACA,mDACI,iBAAA,CACA,QAAA,CACA,yBAAA,CACA,kBAAA,CAGR,4CACI,mDJ9EU,CI+EV,yBAAA,CACA,gBAAA,CACA,kBAAA,CACA,6BAAA,CAAA,qBAAA,CACA,0BAAA,CACA,WAAA,CAEA,QAAA,CACA,gCAAA,CACA,YAAA,CACA,uBAAA,CAAA,oBAAA,CAAA,eAAA,CACA,kCAAA,CAAA,0BAAA,CAEA,uDACI,YAAA,CAGR,6CACI,uBAAA,CACA,4BAAA,CAGR,qBACI,eAAA,CCvHI,sCACI,uBAAA,CAEJ,mGAEI,iFAAA,CAGR,oBACI,kEAAA,CAGR,mBACI,cAAA,CACA,6DAAA,CACA,gEAAA,CACA,mBAAA,CACA,WAAA,CACA,YAAA,CAEJ,6BACI,yDAAA,CAEJ,2DAGI,UAAA,CACA,QAAA,CACA,kFAAA,CACA,mCAAA,CACA,yPAGI,eAAA,CACA,kBAAA,CACA,qBAAA,CACA,sBAAA,CACA,wBAAA,CACA,0EAAA,CACA,kCAAA,CACA,0BAAA,CAGA,uIACI,8DAAA,CAGR,8KAEI,2BAAA,CACA,uFAAA,CACA,oYAEI,+DAAA,CAGR,6FACI,iBAAA,CACA,UAAA,CACA,4BAAA,CACA,yGAEI,qBAAA,CACA,QAAA,CAGR,yFACI,8CL9DQ,CKgEZ,qFACI,UAAA,CAEA,wBAAA,CACA,2BAAA,CAEJ,yFACI,iBAAA,CACA,UAAA,CACA,kBAAA,CACA,6BAAA,CACA,6FACI,yBAAA,CACA,QAAA,CAEJ,uHHNJ,gCAAA,CACA,eAAA,CACA,4BAAA,CACA,8DAAA,CACA,oEAAA,CACA,gEAAA,CACA,2BAAA,CACA,uBAAA,CGCQ,sBAAA,CAAA,yYHGJ,8DAAA,CAEI,iFAAA,CACA,4DAAA,CAMJ,+BAAA,CAEJ,0RAEI,8DAAA,CACA,oEAAA,CACA,4DAAA,CACA,yEAAA,CAAA,iEAAA,CAEA,iGAAA,CAAA,yFAAA,CAEA,s5BAGI,8DAAA,CACA,oEAAA,CACA,4DAAA,CACA,yGAAA,CAAA,iGAAA,CAGR,0RAEI,gCAAA,CAIA,w3DAMI,oEAAA,CACA,4DAAA,CAII,wDAAA,CACA,yCAAA,CAAA,iCAAA,CAEJ,kBAAA,CACA,kCAAA,CAAA,0BAAA,CACA,giEAIQ,wDAAA,CACA,yCAAA,CAAA,iCAAA,CG1DR,iIACI,cAAA,CACA,gBAAA,CACA,qBAAA,CAIZ,+EACI,oDLnFU,CKoFV,cLrEW,CKsEX,iBAAA,CACA,yBAAA,CACA,iFAAA,CACA,yFACI,cAAA,CACA,iBAAA,CACA,uGACI,kBAAA,CAKR,+FACI,iBAAA,CACA,2EAAA,CACA,wCAAA,CAAA,gCAAA,CACA,mGAKI,yDAAA,CAJA,6GACI,8DAAA,CACA,kEAAA,CAMhB,uFACI,iBAAA,CACA,6HACI,uBAAA,CAEJ,6FACI,2EAAA,CACA,wCAAA,CAAA,gCAAA,CAKJ,oLACI,kEAAA,CAGA,gPACI,2EAAA,CAKhB,mBACI,iBAAA,CACA,UAAA,CACA,eAAA,CACA,iBAAA,CACA,sELlIe,CKmIf,0EAAA,CACA,kDACI,aAAA,CACA,UAAA,CACA,qCAHJ,kDAIQ,aAAA,CAAA,CAGR,gDACI,aAAA,CACA,kBAAA,CACA,UAAA,CACA,0EACI,kBAAA,CACA,UAAA,CACA,WAAA,CACA,qBAAA,CACA,+EACI,gBAAA,CACA,WAAA,CACA,qBAAA,CAIZ,0CACI,kBAAA,CACA,qBAAA,CACA,qCAHJ,0CAIQ,eAAA,CACA,iBAAA,CAAA,CAGR,oCAEI,kBAAA,CACA,qBAAA,CACA,gBAAA,CACA,cAAA,CH3LJ,qFAEI,WAAA,CACA,aAAA,CAEJ,0CACI,UAAA,CGsLA,qCANJ,oCAOQ,cAAA,CACA,eAAA,CAAA,CAGR,+BACI,oBAAA,CACA,oDLvLU,CKwLV,cAAA,CACA,gBAAA,CACA,kBAAA,CACA,aAAA,CACA,oCACI,qBAAA,CAGR,sCACI,iBAAA,CACA,QAAA,CAEJ,oCACI,QAAA,CACA,cAAA,CAEJ,8BACI,oBAAA,CACA,iBAAA,CACA,kBAAA,CACA,SAAA,CACA,WAAA,CACA,YAAA,CACA,qCACI,UAAA,CACA,aAAA,CACA,iBAAA,CACA,SAAA,CACA,YAAA,CACA,eAAA,CACA,SAAA,CACA,mELrNM,CK0Nd,8dASI,oBAAA,CACA,eAAA,CACA,unBACI,kBAAA,CACA,aAAA,CACA,eAAA,CACA,8vBACI,cAAA,CAIZ,gDACI,eAAA,CAEJ,mEACI,kBAAA,CACA,4GACI,uBAAA,CAGR,6EACG,+BAAA,CAEH,uCACI,kBAAA,CAEJ,8CACI,UAAA,CACA,eAAA,CACA,YAAA,CAEJ,6CACI,cAAA,CAEJ,mDACI,qBAAA,CAEJ,iEACI,UAAA,CACA,uBAAA,CAIR,iBAEI,kBAAA,CH9RA,+CAEI,WAAA,CACA,aAAA,CAEJ,uBACI,UAAA,CGyRJ,qCAHJ,iBAIQ,cAAA,CAAA,CAEJ,kCACI,oBAAA,CACA,eAAA,CACA,gBAAA,CACA,8EACI,qEAAA,CACA,kBAAA,CAEJ,qCARJ,kCAUQ,UAAA,CACA,aAAA,CH7SR,iFAEI,WAAA,CACA,aAAA,CAEJ,wCACI,UAAA,CAAA,CG0SI,oDACI,+DAAA,CACA,cAAA,CAEJ,0EACI,oBAAA,CAGR,oEACI,wEAAA,CACA,aAAA,CAGR,0BACI,YAAA,CACA,WAAA,CACA,qCAHJ,0BAKQ,UAAA,CACA,kBAAA,CHnUR,iEAEI,WAAA,CACA,aAAA,CAEJ,gCACI,UAAA,CAAA,CG+TA,8IAII,cL/SM,CKgTN,gBAAA,CACA,uBAAA,CAEJ,yEAEI,0DLhUM,CKkUV,qEAEI,gBAAA,CACA,iBAAA,CACA,wEAAA,CAGR,mDACI,oBAAA,CACA,gBAAA,CAIR,qCAEQ,0CACI,UAAA,CAEJ,oCACI,UAAA,CACA,sDACI,KAAA,CACA,QAAA,CAAA,CAMhB,0BACI,oBAAA,CACA,kBAAA,CACA,gBAAA,CACA,eAAA,CACA,gBAAA,CACA,qCANJ,0BAOQ,aAAA,CACA,UAAA,CACA,eAAA,CACA,YAAA,CACA,aAAA,CAAA,CAGR,kBACI,iBAAA,CACA,kGH/SA,gCAAA,CACA,eAAA,CACA,4BAAA,CACA,yDAAA,CACA,oEAAA,CACA,oEAAA,CACA,2BAAA,CACA,uBAAA,CG6SI,oBAAA,CACA,kBAAA,CACA,4BAAA,CH9SJ,8WAGI,yDAAA,CAKI,oEAAA,CACA,gEAAA,CACA,sCAAA,CAAA,8BAAA,CAEJ,+BAAA,CAEJ,4RAEI,yDAAA,CACA,oEAAA,CACA,gEAAA,CACA,yEAAA,CAAA,iEAAA,CAEA,iGAAA,CAAA,yFAAA,CAEA,o+BAGI,yDAAA,CACA,oEAAA,CACA,gEAAA,CACA,yGAAA,CAAA,iGAAA,CAGR,4RAEI,gCAAA,CAIA,gmEAMI,oEAAA,CACA,gEAAA,CAII,mDAAA,CACA,yCAAA,CAAA,iCAAA,CAEJ,kBAAA,CACA,kCAAA,CAAA,0BAAA,CACA,g7EAIQ,mDAAA,CACA,yCAAA,CAAA,iCAAA,CGqPhB,wBACI,iBAAA,CACA,gBAAA,CAEJ,6BACI,KAAA,CAEJ,0CACI,cAAA,CAIR,wBACI,oBAAA,CAEJ,uCACI,eAAA,CAGJ,WACI,iBAAA,CACA,eAAA,CACA,UAAA,CACA,gBACI,QAAA,CACA,SAAA,CACA,uBAAA,CAAA,eAAA,CAIR,0BACI,oBAAA,CACA,iBAAA,CACA,kBAAA,CACA,wFACI,cAAA,CAEJ,8FAEI,YAAA,CACA,OAAA,CACA,SAAA,CACA,QAAA,CACA,6CLhXU,CKgXV,qCLhXU,CKiXV,wGACI,aAAA,CACA,8CL/aI,CKgbJ,kBAAA,CACA,kBAAA,CACA,4BAAA,CACA,qCANJ,wGAOQ,kBAAA,CAAA,CAGR,0GACI,aAAA,CACA,2BAAA,CACA,mBAAA,CACA,UAAA,CACA,uBAAA,CACA,yBAAA,CAEJ,0GACI,iBAAA,CACA,OAAA,CACA,kBAAA,CACA,gBAAA,CAEJ,4JACI,OAAA,CACA,eAAA,CACA,YAAA,CACA,QAAA,CACA,6CL9YM,CK8YN,qCL9YM,CK+YN,0KACI,YAAA,CAEJ,gLACI,iBAAA,CACA,QAAA,CACA,UAAA,CACA,oDL3cE,CK4cF,cAAA,CACA,4LACI,8CLrdJ,CKwdJ,gKACI,qEAAA,CACA,kBAAA,CACA,wBAAA,CACA,iBAAA,CAEJ,wKACI,+DAAA,CACA,kBAAA,CACA,oBAAA,CACA,uBAAA,CACA,oLACI,aAAA,CAIZ,8GACI,uDAAA,CACA,8DAAA,CAGR,oDACI,aAAA,CACA,uDACI,QAAA,CACA,SAAA,CACA,oBAAA,CAGR,qCACI,iBAAA,CAKA,8JAII,oDLxfM,CKyfN,cAAA,CACA,gBAAA,CACA,WAAA,CACA,cAAA,CAGR,oDACI,UAAA,CACA,UAAA,CACA,iBAAA,CAIR,qBACI,iBAAA,CACA,QAAA,CACA,YAAA,CACA,YAAA,CACA,UAAA,CACA,eAAA,CACA,cAAA,CACA,uBAAA,CACA,SAAA,CACA,eAAA,CACA,cAAA,CACA,eAAA,CACA,uDL9hBI,CK+hBJ,iBAAA,CACA,2BAAA,CACA,4BACI,iBAAA,CACA,QAAA,CACA,SAAA,CACA,UAAA,CACA,UAAA,CACA,UAAA,CACA,WAAA,CACA,gBAAA,CACA,+BAAA,CAAA,uBAAA,CACA,uDL3iBA,CK6iBJ,iDACI,SAAA,CACA,UAAA,CAIR,uBAEI,kBAAA,CACA,qBAAA,CACA,cAAA,CACA,kBAAA,CACA,UAAA,CH1jBA,2DAEI,WAAA,CACA,aAAA,CAEJ,6BACI,UAAA,CGqjBJ,yBACI,8DAAA,CAEJ,6BACI,0DLhjBU,CKijBV,gBAAA,CACA,WAAA,CACA,YAAA,CACA,oCACI,qBAAA,CAGR,0BACI,oBAAA,CAGR,2CACI,kBAAA,CACA,eAAA,CACA,cAAA,CACA,gBAAA,CACA,qBAAA,CACA,kBAAA,CAEJ,mCACI,aAAA,CACA,eAAA,CACA,kBAAA,CACA,gBAAA,CACA,UAAA,CACA,WAAA,CAEJ,yCACI,aAAA,CACA,iBAAA,CACA,eAAA,CACA,gBAAA,CACA,WAAA,CACA,UAAA,CACA,sBAAA,CAEJ,gDACI,iBAAA,CACA,UAAA,CACA,qBAAA,CACA,gBAAA,CACA,sDACI,aAAA,CAEJ,gFACI,qBAAA,CACA,eAAA,CACA,SAAA,CACA,YAAA,CACA,QAAA,CACA,6CLjjBU,CKijBV,qCLjjBU,CKkjBV,mFACI,SAAA,CACA,qFACI,8CLjnBA,CKknBA,gCAAA,CACA,6EAAA,CACA,2FACI,uDAAA,CACA,8DAAA,CAGR,gGACI,kBAAA,CAGR,oFACI,iBAAA,CACA,QAAA,CACA,kBAAA,CACA,iBAAA,CAKZ,6BACI,iBAAA,CACA,SAAA,CACA,eAAA,CACA,UAAA,CACA,WAAA,CACA,gBAAA,CACA,YAAA,CACA,mCACI,UAAA,CACA,iBAAA,CACA,QAAA,CACA,QAAA,CACA,UAAA,CACA,WAAA,CACA,eAAA,CACA,+BAAA,CAAA,uBAAA,CACA,6CL3lBU,CK2lBV,qCL3lBU,CK+lBlB,6CACI,aAAA,CACA,eAAA,CACA,kEACI,SAAA,CACA,WAAA,CACA,yEACI,UAAA,CACA,SAAA,CAGR,+CACI,oBAAA,CAIR,yBACI,kBAAA,CACA,qBAAA,CAGJ,uCACI,eAAA,CACA,uDACI,uBAAA,CAGA,0EACI,oBAAA,CACA,wBAAA,CACA,QAAA,CACA,SAAA,CACA,iBAAA,CAEJ,4FACI,WAAA,CACA,kBAAA,CACA,sBAAA,CACA,8CLjsBI,CKksBJ,iHACE,eAAA,CACA,qBAAA,CAIV,uDAEI,oBAAA,CH/sBJ,2HAEI,WAAA,CACA,aAAA,CAEJ,6DACI,UAAA,CG2sBJ,uDACI,UAAA,CACA,oBAAA,CACA,YAAA,CACA,8CAAA,CACA,+CAAA,CACA,mEAAA,CACA,gBAAA,CACA,uDLxtBA,CKytBA,iBAAA,CACA,eAAA,CACA,gFACI,YAAA,CACA,8CAAA,CACA,+CAAA,CACA,mEAAA,CACA,gBAAA,CACA,uDLjuBJ,CKkuBI,sFACI,wBAAA,CAGR,2EACI,iBAAA,CACA,OAAA,CACA,QAAA,CACA,iFACG,QAAA,CACA,kBAAA,CAGP,yIAEI,UAAA,CACA,SAAA,CACA,gBAAA,CACA,kBAAA,CACA,6IACI,aAAA,CACA,WAAA,CACA,UAAA,CAEJ,iJACI,UAAA,CACA,WAAA,CAGR,kEACI,wBAAA,CACA,iBAAA,CACA,qBAAA,CAGR,+DACI,wBAAA,CAGA,oEACI,wBAAA,CAGR,4DACI,UAAA,CACA,+CAAA,CACA,gDAAA,CACA,QAAA,CACA,SAAA,CACA,8BAAA,CACA,qFACI,UAAA,CACA,QAAA,CACA,iBAAA,CAEJ,4EACI,QAAA,CACA,WAAA,CACA,UAAA,CAEJ,uEACI,iBAAA,CACA,qBAAA,CAMR,yBACI,qBAAA,CAEJ,uBACI,cAAA,CC3yBA,sPAII,qBAAA,CACA,yBAAA,CACA,0BAAA,CACA,sSACI,uBAAA,CAEJ,8QACI,qBAAA,CAIJ,qIAEI,eAAA,CAIZ,oCAEI,6BAAA,CAEJ,0BACI,SAAA,CACA,mBAAA,CACA,kBAAA,CACA,uBAAA,CAAA,eAAA,CACA,0EAAA,CAGA,0GAGI,WAAA,CAGR,uBACI,gBAAA,CAEJ,iDACI,OAAA,CAEJ,uCACI,mBAAA,CAEJ,iCACI,WAAA,CACA,qCAFJ,iCAGQ,UAAA,CAAA,CAIJ,2CACI,SAAA,CAEJ,iDACI,SAAA,CAIA,qCAFJ,4FAGQ,UAAA,CAAA,CC5DR,iSACI,YAAA,CAGR,sHAEI,YAAA,CAEJ,0EACI,UAAA,CAEJ,+BAEI,iBAAA,CACA,eAAA,CACA,mEAAA,CACA,iBPca,CObb,sEPRU,COSV,wCAAA,CAAA,gCAAA,CLtBJ,2EAEI,WAAA,CACA,aAAA,CAEJ,qCACI,UAAA,CKiBA,4CACI,iBAAA,CACA,KAAA,CACA,OAAA,CACA,QAAA,CACA,MAAA,CAEJ,6CACI,yDP1BI,CO2BJ,8BAAA,CAAA,sBAAA,CACA,oEAAA,CACA,0DACI,SAAA,CAEJ,yDACI,SAAA,CACA,wBAAA,CACA,kBAAA,CAEJ,wDACI,YAAA,CAEJ,wHACI,yDAAA,CAGR,sDACI,YAAA,CAEJ,2CACI,UAAA,CACA,eAAA,CACA,cAAA,CACA,eAAA,CACA,aAAA,CACA,mBAAA,CACA,0EAAA,CACA,oDACI,iBAAA,CACA,sEACI,YAAA,CAEJ,4EACI,aAAA,CAGR,uDACI,2BAAA,CACA,2BAAA,CACA,cAAA,CACA,gBAAA,CACA,SAAA,CACA,SAAA,CACA,8MAGI,UAAA,CACA,eAAA,CACA,6NACI,oDPtEN,COuEM,mBAAA,CACA,yCAAA,CAIZ,sDACI,gmBAAA,CACA,uBAAA,CACA,oBAAA,CACA,iBAAA,CACA,OAAA,CACA,UAAA,CACA,UAAA,CACA,UAAA,CACA,WAAA,CAEJ,6DACI,QAAA,CACA,MAAA,CACA,UAAA,CAEJ,sHAEI,OAAA,CACA,OAAA,CACA,SAAA,CACA,YAAA,CACA,oIACI,oDPnGF,COqGF,8HACI,YAAA,CAGR,4DAEI,UAAA,CACA,WAAA,CACA,2eAAA,CACA,uBAAA,CAEJ,0DAEI,umBAAA,CACA,UAAA,CACA,WAAA,CACA,uBAAA,CAEJ,uHAEI,8BAAA,CACA,2IACI,eAAA,CACA,UAAA,CACA,WAAA,CACA,mEAAA,CACA,eAAA,CACA,mJACI,UAAA,CACA,WAAA,CAIZ,wDACI,QAAA,CACA,MAAA,CACA,uBAAA,CACA,WAAA,CACA,gBAAA,CAGR,2CACI,WAAA,CACA,2DP/IM,COgJN,UAAA,CACA,eAAA,CAEJ,qCACI,iBAAA,CACA,OAAA,CACA,2DPtJM,COuJN,cAAA,CACA,iBAAA,CAEJ,0DLzFJ,gCAAA,CACA,eAAA,CACA,4BAAA,CACA,yDAAA,CACA,oEAAA,CACA,oEAAA,CACA,2BAAA,CACA,uBAAA,CKoFQ,qBAAA,CACA,eAAA,CAEA,gBPjJO,COkJP,qBAAA,CACA,sBAAA,CACA,4BAAA,CACA,eAAA,CACA,gBAAA,CACA,4BAAA,CACA,cAAA,CL7FR,gMAGI,yDAAA,CAKI,oEAAA,CACA,gEAAA,CACA,sCAAA,CAAA,8BAAA,CAEJ,+BAAA,CAEJ,0IAEI,yDAAA,CACA,oEAAA,CACA,gEAAA,CACA,yEAAA,CAAA,iEAAA,CAEA,iGAAA,CAAA,yFAAA,CAEA,kcAGI,yDAAA,CACA,oEAAA,CACA,gEAAA,CACA,yGAAA,CAAA,iGAAA,CAGR,0IAEI,gCAAA,CAIA,06BAMI,oEAAA,CACA,gEAAA,CAII,mDAAA,CACA,yCAAA,CAAA,iCAAA,CAEJ,kBAAA,CACA,kCAAA,CAAA,0BAAA,CACA,8/BAIQ,mDAAA,CACA,yCAAA,CAAA,iCAAA,CKmCR,oEACI,4CPpLR,COqLQ,cAAA,CACA,iBAAA,CACA,qBAAA,CAEJ,iEACI,YAAA,CAEJ,oNAGI,4CP/LR,COgMQ,QAAA,CAEJ,wEACI,YAAA,CAEJ,+DACI,YAAA,CAEJ,gFL3HR,gCAAA,CACA,eAAA,CACA,4BAAA,CACA,8DAAA,CACA,oEAAA,CACA,gEAAA,CACA,2BAAA,CACA,uBAAA,CKsHY,sBAAA,CACA,wBAAA,CACA,qBAAA,CACA,sBAAA,CLxHZ,kQAGI,8DAAA,CAEI,iFAAA,CACA,4DAAA,CAMJ,+BAAA,CAEJ,sLAEI,8DAAA,CACA,oEAAA,CACA,4DAAA,CACA,yEAAA,CAAA,iEAAA,CAEA,iGAAA,CAAA,yFAAA,CAEA,skBAGI,8DAAA,CACA,oEAAA,CACA,4DAAA,CACA,yGAAA,CAAA,iGAAA,CAGR,sLAEI,gCAAA,CAIA,krCAMI,oEAAA,CACA,4DAAA,CAII,wDAAA,CACA,yCAAA,CAAA,iCAAA,CAEJ,kBAAA,CACA,kCAAA,CAAA,0BAAA,CACA,swCAIQ,wDAAA,CACA,yCAAA,CAAA,iCAAA,CK8DJ,sFACI,kEAAA,CAEJ,qFACI,iBAAA,CACA,gBAAA,CAEJ,0FACI,mDPlKA,COmKA,yBAAA,CAEJ,6FACI,YAAA,CAEJ,8FACI,aAAA,CAEJ,uFACI,wBAAA,CAEJ,qFACI,aAAA,CAKZ,6CACI,qBAAA,CACA,sBAAA,CACA,SPpOG,COsOP,0CACI,iBAAA,CACA,OAAA,CAEA,SAAA,CACA,uBAAA,CACA,2DACI,uDPpPR,COuPI,gHACI,eAAA,CACA,kBAAA,CACA,sBAAA,CACA,wBAAA,CACA,WAAA,CACA,gBAAA,CAGJ,8CACI,UAAA,CACA,WAAA,CACA,iBAAA,CACA,mEAAA,CACA,iBPpOK,COqOL,kBAAA,CACA,2DACI,6BAAA,CAAA,qBAAA,CACA,cAAA,CACA,mEAAA,CACA,iBP1OC,CO8OT,4CACI,6BAAA,CAAA,qBAAA,CACA,2BAAA,CAGJ,+CACI,oBAAA,CACA,oDP3QE,CO4QF,kBAAA,CACA,iBAAA,CACA,eAAA,CACA,qEACI,qBAAA,CACA,wBAAA,CAIR,yDLnNR,gCAAA,CACA,eAAA,CACA,4BAAA,CACA,8DAAA,CACA,oEAAA,CACA,gEAAA,CACA,2BAAA,CACA,uBAAA,CK8MY,WAAA,CACA,wBAAA,CACA,oBAAA,CACA,UAAA,CACA,WAAA,CACA,iBAAA,CACA,cAAA,CLnNZ,6LAGI,8DAAA,CAEI,iFAAA,CACA,4DAAA,CAMJ,+BAAA,CAEJ,wIAEI,8DAAA,CACA,oEAAA,CACA,4DAAA,CACA,yEAAA,CAAA,iEAAA,CAEA,iGAAA,CAAA,yFAAA,CAEA,4bAGI,8DAAA,CACA,oEAAA,CACA,4DAAA,CACA,yGAAA,CAAA,iGAAA,CAGR,wIAEI,gCAAA,CAIA,85BAMI,oEAAA,CACA,4DAAA,CAII,wDAAA,CACA,yCAAA,CAAA,iCAAA,CAEJ,kBAAA,CACA,kCAAA,CAAA,0BAAA,CACA,k/BAIQ,wDAAA,CACA,yCAAA,CAAA,iCAAA,CKyJJ,qEACI,oBAAA,CAEJ,8DACI,iBAAA,CACA,gBAAA,CAMR,gEACI,iBAAA,CAEJ,iEACI,eAAA,CACA,kBAAA,CACA,sBAAA,CAEA,eAAA,CAEJ,mFACI,iBAAA,CAEI,qCACI,sHACI,sBAAA,CACA,wBAAA,CACA,eAAA,CAAA,CAGR,2QACI,wBAAA,CAMhB,gEACI,OAAA,CACA,2FACI,UAAA,CAIR,yBAxUJ,+BAyUQ,kBAAA,CAAA,mBAAA,CAAA,WAAA,CAAA,CAMZ,gBACI,2BAAA,CACA,2BACI,UAAA,CACA,yDP7VQ,CO+VZ,yBACI,eAAA,CACA,kBAAA,CACA,sBAAA,CACA,4BAAA,CAEJ,8BACI,oBAAA,CACA,eAAA,CACA,kBAAA,CACA,UAAA,CACA,WAAA,CACA,iBAAA,CACA,mEAAA,CACA,iBAAA,CACA,kaAAA,CACA,uBAAA,CACA,kCACI,iDPrXJ,COsXI,uFAEI,UAAA,CACA,WAAA,CACA,UAAA,CAMhB,6BACI,cAAA,CACA,WAAA,CACA,QAAA,CACA,SAAA,CACA,iBAAA,CACA,WAAA,CACA,gBAAA,CACA,eAAA,CACA,uBAAA,CACA,mBAAA,CACA,iBP1WiB,CO2WjB,iDP5YI,CO6YJ,2CP9Uc,CO8Ud,mCP9Uc,CO+Ud,mCACI,cAAA,CACA,8CP5YQ,CO8YZ,mCACI,iBAAA,CAGR,4BACI,eAAA,CAEA,sDACI,eAAA,CACA,kBAAA,CACA,sBAAA,CAEJ,kCACI,YAAA,CAGR,yBACI,UAAA,CACA,cAAA,CACA,yDPjaY,COoahB,uCACI,8CPraY,COsaZ,gBAAA,CACA,cAAA,CACA,uEAAA,CACA,uFAEI,qBAAA,CAEJ,2CACI,gBAAA,CAEJ,qDACI,eAAA,CACA,kBAAA,CACA,sBAAA,CACA,cAAA,CAIR,uBACI,gBAAA,CACA,uEAAA,CACA,sBAAA,CACA,yBACI,cPxaU,COyaV,+DAAA,CAGR,+DAEI,mBAAA,CAGJ,6BACI,mBAAA,CACA,iBAAA,CACA,0EAAA,CCndJ,uBACI,iBAAA,CAGJ,eACI,iBAAA,CACA,UAAA,CACA,WAAA,CACA,oDRSc,CQRd,iBAAA,CACA,yBAAA,CACA,2BAAA,CACA,kBAAA,CACA,cAAA,CACA,YAAA,CACA,uDRTI,CQUJ,2CAAA,CAAA,mCAAA,CACA,iBAAA,CACA,UAAA,CACA,cAAA,CACA,sBACI,iBAAA,CACA,QAAA,CACA,QAAA,CACA,UAAA,CACA,UAAA,CACA,SAAA,CACA,UAAA,CACA,gBAAA,CACA,+BAAA,CAAA,uBAAA,CACA,uDRxBA,CQ4BR,sBACI,YAAA,CACA,YAAA,CChCJ,mCACI,GACI,SAAA,CACA,kCAAA,CAAA,0BAAA,CAEJ,QAEI,SAAA,CACA,+BAAA,CAAA,uBAAA,CAEJ,KACI,SAAA,CACA,mCAAA,CAAA,2BAAA,CAAA,CAZR,2BACI,GACI,SAAA,CACA,kCAAA,CAAA,0BAAA,CAEJ,QAEI,SAAA,CACA,+BAAA,CAAA,uBAAA,CAEJ,KACI,SAAA,CACA,mCAAA,CAAA,2BAAA,CAAA,CAGR,4BACI,GACI,SAAA,CACA,kCAAA,CAAA,0BAAA,CAEJ,IACI,SAAA,CACA,+BAAA,CAAA,uBAAA,CAAA,CAPR,oBACI,GACI,SAAA,CACA,kCAAA,CAAA,0BAAA,CAEJ,IACI,SAAA,CACA,+BAAA,CAAA,uBAAA,CAAA,CAGR,yBACI,GACI,0BAAA,CAAA,kBAAA,CAEJ,IACI,4BAAA,CAAA,oBAAA,CAEJ,IACI,0BAAA,CAAA,kBAAA,CAAA,CARR,iBACI,GACI,0BAAA,CAAA,kBAAA,CAEJ,IACI,4BAAA,CAAA,oBAAA,CAEJ,IACI,0BAAA,CAAA,kBAAA,CAAA,CAGR,kCAEI,6BAAA,CAAA,qBAAA,CAGJ,gBACI,gBAAA,CACA,iBAAA,CACA,+BAAA,CACA,eAAA,CAEJ,6BACI,cAAA,CAEJ,+BACI,cAAA,CAEJ,oFAEI,cAAA,CAEJ,8BACI,kBAAA,CAEJ,0CACI,UAAA,CAEJ,4BACI,iBAAA,CACA,YAAA,CAEJ,4BACI,oBAAA,CACA,iBAAA,CACA,kBAAA,CACA,gBAAA,CACA,WAAA,CAEJ,kCACI,YAAA,CAEJ,8CACI,SAAA,CAEJ,sDACI,kBAAA,CACA,+DTlEc,CSmEd,+KAAA,CAAA,gJAAA,CAEJ,wDACI,SAAA,CAEJ,6CACI,eAAA,CAEJ,yDACI,qCAAA,CAAA,6BAAA,CAEJ,uCACI,aAAA,CACA,cAAA,CACA,iBAAA,CACA,WAAA,CACA,cAAA,CAEJ,6CACI,yBAAA,CAEJ,8CACI,SAAA,CAEJ,wCACI,iBAAA,CACA,KAAA,CACA,MAAA,CACA,UAAA,CACA,oBAAA,CACA,cAAA,CACA,gBAAA,CACA,iBAAA,CACA,cAAA,CACA,cAAA,CACA,eAAA,CACA,SAAA,CAEJ,iDACI,cAAA,CACA,iBAAA,CAEJ,qDACI,kBAAA,CAEJ,gEACI,qCAAA,CACA,qCAAA,CAEJ,iEACI,eAAA,CACA,sBAAA,CAEJ,sEACI,8BAAA,CAEJ,gHAEI,cAAA,CACA,iBAAA,CACA,qCAAA,CAEJ,gDACI,mCAAA,CAAA,2BAAA,CAEA,wBAAA,CAAA,gBAAA,CAEJ,sCACI,aAAA,CACA,iBAAA,CACA,eAAA,CACA,UAAA,CACA,WAAA,CACA,YAAA,CACA,kBAAA,CAEJ,0CACI,aAAA,CAEJ,wDACI,oEAAA,CAAA,4DAAA,CAEJ,oDACI,SAAA,CACA,6DAAA,CAAA,qDAAA,CAEJ,wFAEI,aAAA,CACA,iBAAA,CACA,OAAA,CACA,QAAA,CACA,WAAA,CACA,gBAAA,CACA,iBAAA,CACA,mBAAA,CACA,SAAA,CAEJ,gGAEI,aAAA,CACA,UAAA,CACA,WAAA,CAEJ,uDACI,SAAA,CACA,iCAAA,CAAA,yBAAA,CAEJ,qDACI,SAAA,CACA,sCAAA,CAAA,8BAAA,CAEJ,6DACI,wCAAA,CAAA,gCAAA,CAEJ,yCACI,iBAAA,CACA,OAAA,CACA,QAAA,CACA,eAAA,CACA,YAAA,CACA,UAAA,CACA,WAAA,CACA,eAAA,CACA,iBAAA,CACA,iBAAA,CACA,mBAAA,CACA,SAAA,CACA,+BAAA,CAEJ,oDACI,iBAAA,CACA,KAAA,CACA,QAAA,CACA,MAAA,CACA,OAAA,CACA,wDTzMc,CS0Md,mKAAA,CAAA,oIAAA,CACA,0CAAA,CAAA,kCAAA,CAEJ,uDACI,aAAA,CAEJ,6DACI,mBAAA,CACA,SAAA,CAEJ,8CACI,aAAA,CACA,YAAA,CACA,iBAAA,CACA,SAAA,CACA,UAAA,CACA,YAAA,CACA,4CT3OI,CS4OJ,cAAA,CACA,WAAA,CACA,kBAAA,CACA,iBAAA,CACA,mBAAA,CACA,SAAA,CACA,kBAAA,CACA,sFAAA,CAAA,uDAAA,CACA,mCAAA,CAAA,2BAAA,CAEJ,oDACI,UAAA,CACA,iBAAA,CACA,QAAA,CACA,SAAA,CACA,OAAA,CACA,QAAA,CACA,oCAAA,CACA,+BAAA,CACA,mCAAA", "file": "../admin_filer.css", "sourcesContent": ["/*!\n * @copyright: https://github.com/divio/django-filer\n */\n\n//##############################################################################\n// IMPORT SETTINGS\n@import \"settings/all\";\n@import \"mixins/all\";\n\n//##############################################################################\n// IMPORT COMPONENTS\n@import \"components/base\";\n@import \"components/image-info\";\n@import \"components/action-list\";\n@import \"components/filter-files\";\n@import \"components/navigator\";\n@import \"components/modal\";\n@import \"components/drag-and-drop\";\n@import \"components/tooltip\";\n\n//##############################################################################\n// IMPORT LIBS\n@import \"libs/dropzone\";\n", "//##############################################################################\n// BASE\n\nhtml,\nbody {\n    min-width: 320px;\n    height: 100% !important;\n}\n\n.text-left {\n    text-align: left;\n}\n.text-right {\n    text-align: right;\n}\n.clearfix:after {\n    content: \"\";\n    display: table;\n    clear: both;\n}\n.related-widget-wrapper {\n    float: none !important;\n}\n.related-lookup.hidden {\n    display: none !important;\n}\n\n// make sure that tiny styles like on size info has correct font size and color #666\n.tiny {\n    font-size: $font-size-small !important;\n    color: $gray-light !important;\n}\n\n.nav-pages {\n    position: relative;\n    // make sure that paginator has correct font size and color #666\n    font-size: $font-size-small;\n    color: $gray-light !important;\n    padding-left: 10px;\n    padding-right: 20px;\n    padding-top: 15px;\n    padding-bottom: 15px;\n    box-sizing: border-box;\n    background: $white;\n    span {\n        // make sure that paginator has correct font size and color #666\n        font-size: $font-size-small;\n        color: $gray-light !important;\n    }\n    .actions {\n        float: right;\n    }\n}\n\n#id_upload_button:before {\n    display: none;\n}\n#content #content-main {\n    margin-top: 0;\n}\n.filebrowser {\n    &.cms-admin-sideframe {\n        #container {\n            .breadcrumbs + #content,\n            .breadcrumbs + .messagelist + #content {\n                margin-left: 0 !important;\n                margin-right: 0 !important;\n            }\n            .breadcrumbs {\n                left: 0 !important;\n                padding-left: 20px !important;\n            }\n        }\n    }\n    #container {\n        min-width: auto;\n        #content {\n            padding: 0;\n            box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2);\n        }\n        .breadcrumbs + #content,\n        .breadcrumbs + .messagelist + #content {\n            margin-left: 3% !important;\n        }\n    }\n    h1.folder_header {\n        position: relative;\n        top: 6px;\n    }\n    // required for django CMS <= 3.1 #673\n    h2 {\n        display: none;\n    }\n    #content-main {\n        background-color: $white;\n    }\n}\n\n.filer-widget {\n    width: 100%;\n}\n\n.field-file,\n.field-sha1 {\n    word-wrap: break-word;\n    word-break: break-all;\n}\n\n.well.img-preview {\n    display: none;\n    margin-top: 0;\n}\n.img-wrapper {\n    width: 180px;\n    height: 180px;\n}\n\n.file-duplicates {\n    clear: both;\n    padding: 20px 0 0;\n}\n\nform .cancel-link {\n    height: auto !important;\n    line-height: inherit !important;\n    padding: 10px 15px;\n}\n\n.sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    margin: -1px;\n    padding: 0;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    border: 0;\n}\n\n.hidden {\n    display: none !important;\n}\n\n.filer-info-bar {\n    min-height: 15px;\n    margin: 0 0 2px !important;\n    padding: 15px 20px;\n    box-shadow: 0 0 10px -2px rgba(black, 0.2);\n    background-color: $white;\n}\n\n.navigator .actions span.all,\n.navigator .actions span.clear,\n.navigator .actions span.question {\n    font-size: 13px;\n    margin: 0 0.5em;\n    display: none;\n}\n\n#all-items-action-toggle {\n    display: none !important;\n}\n", "// #############################################################################\n// SETTINGS\n\n$speed-base: 200ms;\n\n// COLORS\n$white: var(--dca-white, var(--body-bg, #fff));\n$black: var(--dca-black, var(--body-fg, #000));\n$shadow-black: #000;\n\n$color-primary: var(--dca-primary, var(--primary, #0bf));\n// $color-primary-light: #f1faff;\n$color-success: #693;\n$color-danger: #f00;\n$color-warning: #c93;\n\n// COLORS gray\n$gray:            var(--dca-gray, var(--body-quiet-color, #666)); // #666;\n$gray-lightest:   var(--dca-gray-lightest, var(--darkened-bg, #f2f2f2)); // #f2f2f2\n$gray-lighter:    var(--dca-gray-lighter, var(--border-color, #ddd)); // #ddd\n$gray-light:      var(--dca-gray-light, var(--body-quiet-color, #999)); // #999\n$gray-darker:     var(--dca-gray-darker, #454545); // #454545\n$gray-darkest:    var(--dca-gray-darkest, var(--body-fg, #333)); // #333\n\n$gray-super-light: var(--dca-gray-super-lightest, var(--darkened-bg, #f7f7f7));\n$gray-dropzone: $gray-lightest;\n\n$hover-bg: $gray-lightest;\n\n//##############################################################################\n// BASE Variables\n$font-size-small: 12px;\n$font-size-normal: 14px;\n$font-size-large: 16px;\n\n$icon-size: 16px;\n\n$line-height-normal: 20px;\n\n$border-radius-base: 3px;\n$border-radius-normal: 5px;\n\n$padding-base: 3px;\n$padding-normal: 10px;\n$padding-large: 20px;\n\n$screen-mobile: 320px;\n$screen-tablet: 720px;\n$screen-desktop: 975px;\n\n$screen-tablet-filer: 810px;\n\n//##############################################################################\n// BUTTONS\n\n$btn-border-radius-base: $border-radius-base;\n$btn-active-shadow: inset 0 3px 5px rgba($black, 0.125);\n\n$btn-default-color: var(--dca-gray-light, var(--button-fg, #999));\n$btn-default-bgcolor: var(--dca-white, var(--button-bg, #fff));\n$btn-default-border: var(--dca-gray-lighter, transparent);\n\n$btn-action-color: var(--dca-white, var(--button-fg, #fff));\n$btn-action-bgcolor: $color-primary;\n$btn-action-border: $color-primary;\n\n//##############################################################################\n// #SHADOW\n\n$base-box-shadow: 0 0 5px 0 rgba($shadow-black, 0.2);\n$dropdown-shadow: 0 1px 10px rgba($shadow-black, 0.25);\n", "//##############################################################################\n// IMAGE INFO\n\n.image-info {\n    position: relative;\n    float: right;\n    box-sizing: border-box;\n    width: 28%;\n    margin-top: 0;\n    border: 0;\n    border-radius: 3px;\n    background: $white;\n    box-shadow: 0 0 5px 0 rgba(black,0.2);\n    .image-details,\n    .actions-list {\n        margin: 0;\n        padding: 0;\n        &.image-details {\n            margin: 10px 0;\n            padding: 0 10px;\n        }\n        li {\n            list-style-type: none;\n        }\n        a {\n            cursor: pointer;\n        }\n    }\n    &.image-info-detail {\n        @include clearfix();\n        position: static;\n        float: none;\n        width: 100%;\n        margin-bottom: 20px;\n        padding: 25px;\n        border-radius: 0;\n        // removes background color and shadow from object tools and fixes placement on image detail page\n        + #content-main .object-tools {\n            margin-top: 20px;\n            margin-right: 20px;\n            background-color: transparent;\n            &:before {\n                display: none;\n            }\n        }\n        .image-details-left {\n            float: left;\n        }\n        .image-details-right {\n            float: left;\n            margin-left: 50px;\n        }\n        .image-details,\n        .actions-list {\n            margin-top: 0;\n            border: 0 !important;\n            &.image-details {\n                margin-top: 20px;\n                margin-bottom: 15px;\n                padding: 0;\n            }\n            dt {\n                float: left;\n                color: $gray-light;\n                font-size: 13px;\n                // required for django CMS without admin styles #673\n                line-height: 1rem !important;\n                font-weight: normal;\n                // required for django CMS without admin styles #673\n                margin-top: 0;\n            }\n            dd {\n                color: $gray;\n                font-size: 13px;\n                // required for django CMS without admin styles #673\n                line-height: $font-size-large !important;\n                padding-left: 80px;\n                // required for django CMS without admin styles #673\n                margin-bottom: 5px;\n            }\n            .text {\n                font-size: 13px;\n                margin-right: 15px;\n                strong {\n                    font-size: 13px;\n                }\n            }\n            li {\n                color: $gray;\n                font-size: 13px !important;\n                font-weight: normal !important;\n                padding: 1px 0 !important;\n                border: 0 !important;\n            }\n            a {\n                padding: 0;\n            }\n        }\n        .image-info-title {\n            overflow: hidden;\n            color: $gray;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            padding: 0 0 5px;\n            .icon {\n                float: left;\n                margin-right: 5px;\n            }\n        }\n        .image-preview-container {\n            text-align: left;\n            margin: 20px 0 0;\n            padding: 0;\n            > img {\n                margin-bottom: 15px;\n            }\n        }\n        .actions-list {\n            .icon {\n                font-size: 16px;\n                &:last-child {\n                    float: none;\n                }\n            }\n        }\n    }\n    @media screen and (max-width: $screen-tablet) {\n        float: none;\n        width: 100%;\n        &.image-info-detail {\n            .image-details-left,\n            .image-details-right {\n                float: none;\n                margin-left: 0;\n            }\n        }\n    }\n}\n\n.image-info-close {\n    position: absolute;\n    top: -10px;\n    right: -7px;\n    font-size: 20px;\n    cursor: pointer;\n}\n\n.image-info-title {\n    padding: 5px 10px;\n    border-bottom: solid 1px $gray-lighter;\n    a {\n        margin-left: 5px;\n    }\n}\n\n.image-preview-container {\n    text-align: center;\n    margin: 10px 0;\n    padding: 0 10px;\n    .image-preview {\n        display: inline-block;\n        position: relative;\n        margin-bottom: 15px;\n        outline: 1px solid $gray-lightest;\n        background-image: url(\"data:image/gif;base64,R0lGODlhCAAIAKECAOPj4/z8/P///////yH5BAEKAAIALAAAAAAIAAgAAAINhBEZh8q6DoTPSWvoKQA7\");\n        img {\n            display: block;\n        }\n    }\n    .image-preview-field {\n        position: absolute;\n        top: 0;\n        right: 0;\n        bottom: 0;\n        left: 0;\n        overflow: hidden;\n    }\n    .image-preview-circle {\n        position: relative;\n        z-index: 1;\n        width: 26px;\n        height: 26px;\n        border: solid 2px $color-danger;\n        margin: -13px;\n        border-radius: 30px;\n        cursor: move;\n        background: rgba(255, 255, 255, 0.5);\n    }\n    audio, video {\n        margin-bottom: 15px;\n        &:focus {\n            outline: none;\n        }\n    }\n}\n\n.button-group .button {\n    margin-right: 10px;\n    padding: 10px 15px;\n}\n", "// #############################################################################\n// OTHER\n\n// add clearfix which doesnt add overflow:hidden\n@mixin clearfix() {\n    &:before,\n    &:after {\n        content: \" \";\n        display: table;\n    }\n    &:after {\n        clear: both;\n    }\n}\n// taken from bootstrap with adaptations\n@function important($important) {\n    @if($important == true) {\n        @return !important;\n    } @else {\n        @return true;\n    }\n}\n/* @mixin button-variant($color, $background, $border, $important: false) {\n    background-image: none important($important);\n    margin-bottom: 0; // For input.btn\n    padding: 6px 20px important($important);\n    border-radius: $btn-border-radius-base important($important);\n    color: $color important($important);\n    font-size: $font-size-small important($important);\n    line-height: $font-size-small;\n    font-weight: normal;\n    text-transform: none important($important);\n    letter-spacing: normal important($important);\n    background-color: $background important($important);\n    border: 1px solid $border important($important);\n    background-clip: padding-box;\n    appearance: none;\n    &:focus {\n        color: $color important($important);\n        background-color: darken($background, 5%) important($important);\n        border-color: darken($border, 5%) important($important);\n        text-decoration: none important($important);\n    }\n    &:hover {\n        color: $color important($important);\n        background-color: darken($background, 5%) important($important);\n        border-color: darken($border, 5%) important($important);\n        text-decoration: none important($important);\n    }\n    &:active {\n        color: $color important($important);\n        background-color: darken($background, 10%) important($important);\n        border-color: darken($border, 10%) important($important);\n        box-shadow: $btn-active-shadow important($important);\n\n        &:hover,\n        &:focus {\n            color: $color important($important);\n            background-color: darken($background, 17%) important($important);\n            border-color: darken($border, 25%) important($important);\n        }\n    }\n    &:active {\n        background-image: none important($important);\n    }\n    &[disabled] {\n        &,\n        &:hover,\n        &:focus,\n        &:active {\n            background-color: rgba($background, 0.4) important($important);\n            border-color: rgba($border, 0.4) important($important);\n            color: rgba($color, 0.8) important(1);\n            cursor: not-allowed;\n            box-shadow: none important($important);\n            &:before {\n                color: rgba($color, 0.4) important(1);\n            }\n        }\n    }\n}*/\n\n@mixin button-variant($color, $background, $border, $important: false) {\n    background-image: none important($important);\n    margin-bottom: 0; // For input.btn\n    border-radius: $btn-border-radius-base important($important);\n    color: $color important($important);\n    background-color: $background important($important);\n    border: 1px solid $border important($important);\n    background-clip: padding-box;\n    -webkit-appearance: none;\n    &:focus,\n    &.focus,\n    &:hover {\n        color: $color important($important);\n        @if $background == $btn-default-bgcolor {\n            background-color: $gray-lightest important($important);\n            border-color: $border important($important);\n        } @else {\n            background-color: $background important($important);\n            border-color: $border important($important);\n            filter: invert(0.05) important($important);\n        }\n        text-decoration: none important($important);\n    }\n    &:active,\n    &.cms-btn-active {\n        color: $color important($important);\n        background-color: $background important($important);\n        border-color: $border important($important);\n        filter: brightness(var(--active-brightness)) opacity(1) important($important);\n        // Strange: removing opacity(1.) or correcting it makes item transparent\n        box-shadow: $btn-active-shadow important($important);\n\n        &:hover,\n        &:focus,\n        &.focus {\n            color: $color important($important);\n            background-color: $background important($important);\n            border-color: $border important($important);\n            filter: brightness(calc(var(--focus-brightness) * var(--active-brightness))) opacity(1) important($important);\n        }  // Strange: removing opacity(1.) or correcting it makes item transparent\n    }\n    &:active,\n    &.cms-btn-active {\n        background-image: none important($important);\n    }\n    &.cms-btn-disabled,\n    &[disabled] {\n        &,\n        &:hover,\n        &:focus,\n        &.focus,\n        &:active,\n        &.cms-btn-active { // TODO: FABR\n            background-color: $background important($important);\n            border-color: $border important($important);\n            @if $color == $gray {\n                color: $gray-lighter important(1);\n            } @else {\n                color: $color important(1);\n                filter: brightness(0.6) opacity(1);  // Strange: removing opacity(1.) or correcting it makes item transparent\n            }\n            cursor: not-allowed;\n            box-shadow: none important($important);\n            &:before {\n                @if $color == $gray {\n                    color: $gray-lighter important(1);\n                } @else {\n                    color: $color important(1);\n                    filter: brightness(0.6) opacity(1); // Strange: removing opacity(1.) or correcting it makes item transparent\n                }\n            }\n        }\n    }\n}\n", "//##############################################################################\n// ACTION LIST\n\n.actions-list-dropdown {\n    a {\n        display: block;\n        padding: 5px 10px;\n    }\n    .caret-down {\n        display: inline-block;\n    }\n    .caret-right {\n        display: none;\n    }\n    &.js-collapsed {\n        border-bottom: solid 1px $gray-lighter;\n        .caret-down {\n            display: none;\n        }\n        .caret-right {\n            display: inline-block;\n        }\n    }\n}\n.actions-list {\n    border-top: solid 1px $gray-lighter;\n    &:last-child {\n        border-top: none;\n        a {\n            border-bottom: none;\n        }\n    }\n    a {\n        display: block;\n        font-size: 20px;\n        padding: 5px 10px;\n        border-bottom: solid 1px $gray-lighter;\n    }\n    .icon {\n        &:first-child {\n            width: 20px;\n        }\n        &:last-child {\n            float: right;\n            margin-top: 3px;\n        }\n    }\n}\n.actions-separated-list {\n    display: inline-block;\n    margin: 0;\n    padding-left: 0;\n    @media screen and (max-width: $screen-tablet) {\n        float: left;\n        margin-left: 0;\n    }\n    li {\n        display: inline-block;\n        line-height: 34px;\n        vertical-align: middle;\n        padding: 0 10px;\n        list-style: none;\n        @media screen and (max-width: $screen-tablet) {\n            &:first-child {\n                padding-left: 0;\n            }\n        }\n        span {\n            vertical-align: middle;\n        }\n        a {\n            color: $gray;\n        }\n    }\n    span:before {\n        font-size: 18px;\n    }\n}\n", "//##############################################################################\n// FILTER FILES\n\n.search-is-focused {\n    .filter-files-container {\n        position: static;\n    }\n    .filter-filers-container-inner {\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        @media screen and (max-width: $screen-tablet) {\n            position: static;\n        }\n    }\n    .breadcrumbs-container {\n        position: relative;\n    }\n    &.breadcrumb-min-width .filter-filers-container-inner {\n        position: static;\n    }\n}\n\n.filter-files-container {\n    @include clearfix;\n    display: table-cell;\n    vertical-align: middle;\n    position: relative;\n    width: 245px;\n    margin: 0;\n    padding: 0;\n    background: none;\n    box-shadow: none;\n    z-index: 1000;\n    @media screen and (max-width: $screen-tablet) {\n        display: block;\n        width: auto;\n        margin-right: 0;\n        margin-top: 10px;\n        .filter-files-button {\n            float: none;\n        }\n    }\n    .filer-dropdown-container {\n        position: absolute;\n        top: 0;\n        right: 0;\n        > a {\n            &,\n            &:visited,\n            &:link:visited,\n            &:link {\n                display: inline-block;\n                line-height: 34px;\n                text-align: center;\n                width: 34px;\n                height: 34px;\n                padding: 0;\n            }\n        }\n        &.open + .filer-dropdown-menu-checkboxes {\n            display: block;\n            width: calc(100% - 30px);\n            li {\n                margin: 0;\n                padding: 0;\n                list-style-type: none;\n            }\n        }\n    }\n    .filter-search-wrapper {\n        position: relative;\n        float: left;\n        text-align: right;\n        width: calc(100% - 43px);\n        margin-right: 5px;\n        @media screen and (max-width: $screen-tablet) {\n            float: left;\n        }\n        .filer-dropdown-container span {\n            line-height: 34px !important;\n            height: 34px !important;\n        }\n    }\n    .filter-files-button {\n        float: right;\n        text-align: center;\n        white-space: nowrap;\n        height: 35px;\n        margin: 0;\n        padding: 8px !important;\n        .icon {\n            position: relative;\n            left: 2px;\n            font-size: 16px !important;\n            vertical-align: top;\n        }\n    }\n    .filter-files-field {\n        color: $gray-darkest;\n        font-size: 12px !important;\n        line-height: 35px;\n        font-weight: normal;\n        box-sizing: border-box;\n        min-width: 200px !important;\n        height: 35px;\n        // required for django CMS <= 3.1 #673\n        margin: 0;\n        padding: 0 35px 0 10px !important;\n        outline: none;\n        appearance: none;\n        transition: max-width $speed-base;\n        // disable clear X on IE #690\n        &::-ms-clear {\n            display: none;\n        }\n    }\n    .filer-dropdown-menu {\n        margin-top: 0 !important;\n        margin-right: -1px !important;\n    }\n}\n.filter-files-cancel {\n    margin: 5px 20px;\n}\n", "//##############################################################################\n// NAVIGATOR\n\nbody {\n    &.dz-drag-hover {\n        .drag-hover-border {\n            display: none !important;\n        }\n        .navigator-table tbody td,\n        .navigator-table tbody .unfiled td {\n            background-color: $hover-bg !important;\n        }\n    }\n    &.reset-hover td {\n        background-color: $white !important;\n    }\n}\n.drag-hover-border {\n    position: fixed;\n    border-top: solid 2px $color-primary;\n    border-bottom: solid 2px $color-primary;\n    pointer-events: none;\n    z-index: 100;\n    display: none;\n}\n.thumbnail-drag-hover-border {\n    border: solid 2px $color-primary;\n}\n.filebrowser .navigator-list,\n.filebrowser .navigator-table {\n    // required for django CMS <= 3.1 #673\n    width: 100%;\n    margin: 0;\n    border-top: solid 1px $gray-lighter !important;\n    border-collapse: collapse !important;\n    .navigator-header,\n    thead th,\n    tbody td {\n        text-align: left;\n        font-weight: normal;\n        vertical-align: middle;\n        padding: 5px !important;\n        border-left: 0 !important;\n        border-bottom: 1px solid $gray-lighter;\n        border-top: 1px solid transparent;\n        background: none !important;\n    }\n    tbody tr.selected {\n        .action-button span {\n            color: $gray-darkest !important;\n        }\n    }\n    .navigator-body,\n    .unfiled td {\n        padding: 12px 5px !important;\n        background-color: $gray-super-light !important;\n        a,\n        a:hover {\n            color: $gray !important;\n        }\n    }\n    .column-checkbox {\n        text-align: center;\n        width: 20px;\n        padding-left: 20px !important;\n        input {\n            // makes sure that checkbox is vertical aligned #664\n            vertical-align: middle;\n            margin: 0;\n        }\n    }\n    .column-name a {\n        color: $color-primary;\n    }\n    .column-icon {\n        width: 50px;\n        // removes padding to make sure that column has correct height #664\n        padding-top: 0 !important;\n        padding-bottom: 0 !important;\n    }\n    .column-action {\n        text-align: center;\n        width: 90px;\n        white-space: nowrap;\n        padding-right: 20px !important;\n        a {\n            font-size: 16px !important;\n            margin: 0;\n        }\n        .action-button {\n            @include button-variant($btn-default-color, $btn-default-bgcolor, $btn-default-border, true);\n            padding: 9px !important;\n            span {\n                font-size: 17px;\n                line-height: 33px;\n                vertical-align: middle;\n            }\n        }\n    }\n    .no-files {\n        color: $gray;\n        font-size: $font-size-normal;\n        text-align: center;\n        padding: 40px 0 !important;\n        background-color: $gray-lightest !important;\n        span {\n            font-size: 20px;\n            margin-right: 10px;\n            &:before {\n                vertical-align: sub;\n            }\n        }\n    }\n    .dz-drag-hover {\n        td {\n            position: relative;\n            background: $hover-bg !important;\n            box-sizing: border-box !important;\n            a {\n                &.icon {\n                    color: $gray-darkest !important;\n                    background-color: $white !important;\n                }\n                color: $color-primary !important;\n            }\n        }\n    }\n    &.dz-drag-hover {\n        position: relative;\n        .drag-hover-border {\n            display: none !important;\n        }\n        td {\n            background: $hover-bg !important;\n            box-sizing: border-box !important;\n        }\n    }\n    .reset-hover,\n    &.reset-hover {\n        td {\n            background-color: $white !important;\n        }\n        .dz-drag-hover {\n            td {\n                background: $hover-bg !important;\n            }\n        }\n    }\n}\n.navigator-top-nav {\n    position: relative;\n    clear: both;\n    min-height: 35px;\n    padding: 15px 20px;\n    background: $gray-super-light;\n    border-bottom: $gray-lighter solid 1px;\n    .breadcrumbs-container-wrapper {\n        display: table;\n        width: 100%;\n        @media screen and (max-width: $screen-tablet) {\n            display: block;\n        }\n    }\n    .breadcrumbs-container-inner {\n        display: table;\n        table-layout: fixed;\n        width: 100%;\n        .filer-dropdown-container {\n            display: table-cell;\n            width: 30px;\n            height: 35px;\n            vertical-align: middle;\n            span {\n                line-height: 35px;\n                height: 35px;\n                vertical-align: middle;\n            }\n        }\n    }\n    .breadcrumbs-container {\n        display: table-cell;\n        vertical-align: middle;\n        @media screen and (max-width: $screen-tablet) {\n            position: static;\n            margin-right: 20px;\n        }\n    }\n    .tools-container {\n        @include clearfix;\n        display: table-cell;\n        vertical-align: middle;\n        text-align: right;\n        margin-top: 2px;\n        @media screen and (max-width: $screen-tablet) {\n            display: inline;\n            text-align: left;\n        }\n    }\n    .nav-button {\n        display: inline-block;\n        color: $gray;\n        font-size: 20px;\n        line-height: 34px;\n        vertical-align: top;\n        margin: 0 10px;\n        span {\n            vertical-align: middle;\n        }\n    }\n    .nav-button-filter {\n        position: relative;\n        top: -1px;\n    }\n    .nav-button-dots {\n        margin: 0;\n        padding: 0 15px;\n    }\n    .separator {\n        display: inline-block;\n        position: relative;\n        vertical-align: top;\n        width: 1px;\n        height: 34px;\n        margin: 0 5px;\n        &:before {\n            content: \"\";\n            display: block;\n            position: absolute;\n            top: -14px;\n            bottom: -11px;\n            overflow: hidden;\n            width: 1px;\n            background-color: $gray-lighter;\n        }\n    }\n}\n.breadcrumb-min-width {\n    .filer-navigator-breadcrumbs-dropdown-container,\n    .navigator-breadcrumbs-name-dropdown-wrapper,\n    .navigator-breadcrumbs-folder-name-wrapper,\n    .breadcrumbs-container-wrapper,\n    .breadcrumbs-container,\n    .tools-container,\n    .filter-files-container,\n    .navigator-breadcrumbs,\n    .navigator-button-wrapper {\n        display: inline-block;\n        text-align: left;\n        .actions-wrapper {\n            white-space: nowrap;\n            margin-left: 0;\n            margin-top: 10px;\n            li:first-child {\n                padding-left: 0;\n            }\n        }\n    }\n    .navigator-button-wrapper {\n        margin-top: 10px;\n    }\n    .navigator-breadcrumbs-name-dropdown-wrapper {\n        min-height: inherit;\n        .filer-dropdown-container .fa-caret-down {\n            vertical-align: text-top;\n        }\n    }\n    .breadcrumbs-container-inner .filer-dropdown-container {\n       display: inline-block !important;\n    }\n    .navigator-tools {\n        white-space: normal;\n    }\n    .filter-files-container {\n        width: 100%;\n        margin-top: 10px;\n        z-index: auto;\n    }\n    .breadcrumbs-container {\n        margin-right: 0;\n    }\n    .navigator-breadcrumbs .icon {\n        vertical-align: middle;\n    }\n    .navigator-breadcrumbs-folder-name-wrapper {\n        float: left;\n        width: calc(100% - 30px);\n    }\n}\n\n.navigator-tools {\n    @include clearfix;\n    white-space: nowrap;\n    @media screen and (max-width: $screen-tablet) {\n        display: inline;\n    }\n    .actions-wrapper {\n        display: inline-block;\n        margin-bottom: 0;\n        margin-left: 10px;\n        a, a:hover {\n            color: $gray-light !important;\n            cursor: not-allowed;\n        }\n        @media screen and (max-width: $screen-tablet) {\n            @include clearfix();\n            float: none;\n            margin-left: 0;\n        }\n        &.action-selected {\n            a {\n                color: $gray !important;\n                cursor: pointer;\n            }\n            .actions-separated-list {\n                display: inline-block;\n            }\n        }\n        + .filer-list-type-switcher-wrapper {\n            border-left: solid 1px $gray-lighter;\n            margin-left: 0;\n        }\n    }\n    .actions {\n        display: none;\n        float: right;\n        @media screen and (max-width: $screen-tablet) {\n            @include clearfix();\n            float: none;\n            margin-bottom: 10px;\n        }\n        .all,\n        .question,\n        .clear,\n        .action-counter {\n            font-size: $font-size-small;\n            line-height: 34px;\n            vertical-align: text-top;\n        }\n        .action-counter,\n        .all {\n            color: $gray-light;\n        }\n        .question,\n        .clear {\n            margin-left: 10px;\n            padding-left: 10px;\n            border-left: solid 1px $gray-lighter;\n        }\n    }\n    .filer-list-type-switcher-wrapper {\n        display: inline-block;\n        margin-left: 10px;\n    }\n\n}\n@media screen and (max-width: $screen-tablet) {\n    .navigator-top-nav {\n        .breadcrumbs-container {\n            float: none;\n        }\n        .navigator-tools {\n            float: none;\n            .separator:before {\n                top: 0;\n                bottom: 0;\n            }\n        }\n    }\n}\n// make sure that buttons break to new line on mobile view #677\n.navigator-button-wrapper {\n    display: inline-block;\n    vertical-align: top;\n    text-align: right;\n    margin-bottom: 0;\n    margin-left: 10px;\n    @media screen and (max-width: $screen-tablet) {\n        display: block;\n        float: none;\n        text-align: left;\n        margin-top: 0;\n        margin-left: 0;\n    }\n}\n.navigator-button {\n    margin-right: 10px;\n    &,\n    &:visited,\n    &:link:visited,\n    &:link {\n        @include button-variant($btn-action-color, $btn-action-bgcolor, $btn-action-border, true);\n        display: inline-block;\n        vertical-align: top;\n        padding: 10px 20px !important;\n    }\n    .icon {\n        position: relative;\n        margin-right: 3px;\n    }\n    .fa-folder {\n        top: 0;\n    }\n    &.navigator-button-upload {\n        margin-right: 0;\n    }\n}\n\n.upload-button-disabled {\n    display: inline-block;\n}\n.navigator-button + .filer-dropdown-menu {\n    margin-top: -2px;\n}\n\n.navigator {\n    position: relative;\n    overflow-x: auto;\n    width: 100%;\n    form {\n        margin: 0;\n        padding: 0;\n        box-shadow: none;\n    }\n}\n\n.filer-dropdown-container {\n    display: inline-block;\n    position: relative;\n    vertical-align: top;\n    .fa-caret-down, .cms-icon-caret-down {\n        font-size: 14px;\n    }\n    .filer-dropdown-menu,\n    + .filer-dropdown-menu {\n        display: none;\n        right: 0;\n        left: auto;\n        border: 0;\n        box-shadow: $dropdown-shadow;\n        > li > a {\n            display: block;\n            color: $color-primary;\n            font-weight: normal;\n            white-space: normal;\n            padding: 12px 20px !important;\n            @media screen and (min-width: $screen-tablet) {\n                white-space: nowrap;\n            }\n        }\n        label {\n            display: block;\n            line-height: 20px !important;\n            text-transform: none;\n            width: auto;\n            margin: 5px 0 !important;\n            padding: 0 10px !important;\n        }\n        input {\n            position: relative;\n            top: 4px;\n            vertical-align: top;\n            margin-right: 5px;\n        }\n        &.filer-dropdown-menu-checkboxes {\n            width: 0;\n            min-height: 50px;\n            padding: 15px;\n            border: 0;\n            box-shadow: $dropdown-shadow;\n            &:before {\n                display: none;\n            }\n            .fa-close {\n                position: absolute;\n                top: 10px;\n                right: 10px;\n                color: $gray;\n                cursor: pointer;\n                &:hover {\n                    color: $color-primary;\n                }\n            }\n            p {\n                color: $gray-light !important;\n                font-weight: normal;\n                text-transform: uppercase;\n                margin-bottom: 5px;\n            }\n            label {\n                color: $gray !important;\n                font-weight: normal;\n                padding: 0 !important;\n                margin-top: 0 !important;\n                input {\n                    margin-left: 0;\n                }\n            }\n        }\n        a:hover {\n            color: $white !important;\n            background: $color-primary !important;\n        }\n    }\n    &.open .filer-dropdown-menu {\n        display: block;\n        li {\n            margin: 0;\n            padding: 0;\n            list-style-type: none;\n        }\n    }\n    + .separator {\n        margin-right: 10px;\n    }\n}\n.filer-dropdown-container-down {\n    > a {\n        &,\n        &:link,\n        &:visited,\n        &:link:visited {\n            color: $gray;\n            font-size: 20px;\n            line-height: 35px;\n            height: 35px;\n            padding: 0 10px;\n        }\n    }\n    .filer-dropdown-menu {\n        right: auto;\n        left: -14px;\n        margin-right: 10px;\n    }\n}\n\n.filer-dropdown-menu {\n    position: absolute;\n    top: 100%;\n    z-index: 1000;\n    display: none;\n    float: left;\n    min-width: 160px;\n    margin: 2px 0 0;\n    margin-top: 0 !important;\n    padding: 0;\n    list-style: none;\n    font-size: 14px;\n    text-align: left;\n    background-color: $white;\n    border-radius: 4px;\n    background-clip: padding-box;\n    &:before {\n        position: absolute;\n        top: -5px;\n        left: 35px;\n        z-index: -1;\n        content: '';\n        width: 10px;\n        height: 10px;\n        margin-left: -5px;\n        transform: rotate(45deg);\n        background-color: $white;\n    }\n    &.create-menu-dropdown:before {\n        left: auto;\n        right: 17px;\n    }\n}\n\n.navigator-breadcrumbs {\n    @include clearfix;\n    display: table-cell;\n    vertical-align: middle;\n    font-size: 16px;\n    white-space: nowrap;\n    width: 60px;\n    > a {\n        color: $gray-darkest !important;\n    }\n    .icon {\n        color: $gray-light;\n        line-height: 35px;\n        height: 35px;\n        margin: 0 5px;\n        &:before {\n            vertical-align: middle;\n        }\n    }\n    li {\n        list-style-type: none;\n    }\n}\n.navigator-breadcrumbs-folder-name-wrapper {\n    display: table-cell;\n    overflow: hidden;\n    font-size: 16px;\n    font-weight: bold;\n    vertical-align: middle;\n    white-space: nowrap;\n}\n.navigator-breadcrumbs-folder-name {\n    display: block;\n    overflow: hidden;\n    white-space: normal;\n    line-height: 35px;\n    width: 100%;\n    height: 35px;\n}\n.navigator-breadcrumbs-folder-name-inner {\n    display: block;\n    position: relative;\n    overflow: hidden;\n    line-height: 35px;\n    height: 35px;\n    width: 100%;\n    text-overflow: ellipsis;\n}\n.filer-navigator-breadcrumbs-dropdown-container {\n    position: relative;\n    float: left;\n    vertical-align: middle;\n    margin: 0 7px 0 0;\n    > a img {\n        padding: 3px 0;\n    }\n    .navigator-breadcrumbs-dropdown {\n        left: -15px !important;\n        min-width: 200px;\n        padding: 0;\n        margin-top: 0;\n        border: 0;\n        box-shadow: $dropdown-shadow;\n        > li {\n            padding: 0;\n            > a {\n                color: $color-primary;\n                padding: 12px 20px 3px !important;\n                border-bottom: solid 1px $gray-lightest;\n                &:hover {\n                    color: $white !important;\n                    background: $color-primary !important;\n                }\n            }\n            &:last-child > a {\n                border-bottom: none;\n            }\n        }\n        img {\n            position: relative;\n            top: -5px;\n            vertical-align: top;\n            margin: 0 10px 0 0;\n        }\n    }\n}\n\n.navigator-dropdown-arrow-up {\n    position: relative;\n    left: 20px;\n    overflow: hidden;\n    width: 20px;\n    height: 20px;\n    margin-top: -20px;\n    z-index: 1001;\n    &:after {\n        content: \"\";\n        position: absolute;\n        top: 15px;\n        left: 5px;\n        width: 10px;\n        height: 10px;\n        background: white;\n        transform: rotate(45deg);\n        box-shadow: $dropdown-shadow;\n    }\n}\n\n.navigator-breadcrumbs-name-dropdown-wrapper {\n    display: table;\n    min-height: 35px;\n    .filer-dropdown-menu {\n        left: auto;\n        right: -80px;\n        &:before {\n            right: 80px;\n            left: auto;\n        }\n    }\n    a {\n        display: inline-block;\n    }\n}\n\n.empty-filer-header-cell {\n    display: table-cell;\n    vertical-align: middle;\n}\n\n.filebrowser .navigator-thumbnail-list {\n    overflow: hidden;\n    .navigator-list {\n        border-top: 0 !important;\n    }\n    .navigator-thumbnail-list-header {\n        & > * {\n            display: inline-block;\n            text-transform: uppercase;\n            margin: 0;\n            padding: 0;\n            padding-left: 10px;\n        }\n        .navigator-checkbox {\n            float: right;\n            padding-right: 20px;\n            text-transform: initial;\n            color: $color-primary;\n            input[type=\"checkbox\"] {\n              margin-left: 5px;\n              vertical-align: middle;\n            }\n        }\n    }\n    .navigator-body {\n        @include clearfix;\n        padding: 0 !important;\n    }\n    .thumbnail-item {\n        float: left;\n        display: inline-block;\n        padding: 10px;\n        width: calc(var(--thumbnail-size, 120px) + 5px);\n        height: calc(var(--thumbnail-size, 120px) + 5px);\n        border: 1px solid $gray-lighter;\n        margin: 16px 12px;\n        background-color: $white;\n        position: relative;\n        overflow: hidden;\n        .thumbnail-file-item-box {\n            padding: 10px;\n            width: calc(var(--thumbnail-size, 120px) + 5px);\n            height: calc(var(--thumbnail-size, 120px) + 5px);\n            border: 1px solid $gray-lighter;\n            margin: 16px 12px;\n            background-color: $white;\n            &:hover {\n                background-color: #f1faff;\n            }\n        }\n        .navigator-checkbox {\n            position: absolute;\n            top: 5px;\n            left: 5px;\n            input {\n               margin: 0;\n               vertical-align: top;\n            }\n        }\n        .item-thumbnail,\n        .item-icon {\n            height: 50%;\n            width: 50%;\n            margin: 10px auto;\n            margin-bottom: 18px;\n            a {\n                display: block;\n                height: 100%;\n                width: 100%;\n            }\n            img {\n                width: 100%;\n                height: 100%;\n            }\n        }\n        .item-name {\n            background: transparent;\n            text-align: center;\n            word-break: break-word;\n        }\n    }\n    .thumbnail-virtual-item {\n        background-color: initial;\n    }\n    .thumbnail-folder-item {\n        &:hover {\n            background-color: #f1faff;\n        }\n    }\n    .thumbnail-file-item {\n        float: none;\n        width: calc(var(--thumbnail-size, 120px) + 27px);\n        height: calc(var(--thumbnail-size, 120px) + 80px);\n        border: 0;\n        padding: 0;\n        background-color: transparent;\n        .thumbnail-file-item-box {\n            float: none;\n            margin: 0;\n            margin-bottom: 5px;\n        }\n        .item-thumbnail {\n            margin: 0;\n            height: 100%;\n            width: 100%;\n        }\n        .item-name {\n            position: relative;\n            word-break: break-word;\n        }\n    }\n}\n\n.insertlinkButton {\n    &:before {\n        content:\"\" !important;  // Necessary since djangocms-admin-style tries to add its own icon\n    }\n    span {\n        font-size: 17px;\n    }\n}\n", "//##############################################################################\n// MODAL\n\n.popup {\n    &.app-cmsplugin_filer_image {\n        .form-row.field-image .field-box,\n        .field-box.field-free_link,\n        .field-box.field-page_link,\n        .field-box.field-file_link {\n            float: none !important;\n            margin-right: 0 !important;\n            margin-top: 20px !important;\n            &:first-child {\n                margin-top: 0 !important\n            }\n            input {\n                width: 100% !important;\n            }\n        }\n        .form-row .field-box {\n            &.field-crop,\n            &.field-upscale {\n                margin-top: 30px;\n            }\n        }\n    }\n    &.delete-confirmation .colM ul {\n        // makes sure that between list and button is a space #744\n        margin-bottom: 25px !important;\n    }\n    .image-info-detail {\n        padding: 0;\n        padding-bottom: 25px;\n        margin-bottom: 30px;\n        box-shadow: none;\n        border-bottom: solid 1px $gray-lighter;\n    }\n    &.change-list.filebrowser {\n        #result_list tbody th,\n        #result_list tbody td {\n            // makes sure that changelist columns has correct height on modal window #665\n            height: auto;\n        }\n    }\n    .filer-dropzone {\n        padding: 5px 20px;\n    }\n    form .form-row .filer-dropzone .filerFile {\n        top: 8px;\n    }\n    &.filebrowser #container #content {\n        margin: 0 !important;\n    }\n    .navigator-button-wrapper {\n        float: right;\n        @media screen and (max-width: $screen-tablet) {\n            float: none;\n        }\n    }\n    .navigator-top-nav {\n        .tools-container {\n            width: 70%;\n        }\n        .breadcrumbs-container {\n            width: 30%;\n        }\n        .tools-container,\n        .breadcrumbs-container {\n            @media screen and (max-width: $screen-tablet) {\n                width: 100%;\n            }\n        }\n    }\n}\n", "//##############################################################################\n// DRAG AND DROP\n\nform .form-row {\n    &[class*=\"file\"],\n    &[class*=\"folder\"],\n    &[class*=\"img\"],\n    &[class*=\"image\"],\n    &[class*=\"visual\"] {\n        .related-widget-wrapper-link {\n            display: none;\n        }\n    }\n    .filer-widget + .related-widget-wrapper-link,\n    .filer-widget + * + .related-widget-wrapper-link {\n        display: none;\n    }\n    .related-widget-wrapper:has(.filer-widget,.filer-dropzone) {\n        width: 100%;\n    }\n    .filer-dropzone {\n        @include clearfix;\n        position: relative;\n        min-width: 215px;\n        border: solid 1px $gray-lighter;\n        border-radius: $border-radius-base;\n        background-color: $gray-lightest;\n        box-sizing: border-box !important;\n        .z-index-fix {\n            position: absolute;\n            top: 0;\n            right: 0;\n            bottom: 0;\n            left: 0;\n        }\n        &.dz-drag-hover {\n            background-color: $color-primary;\n            filter: brightness(1.5);\n            border: solid 2px $color-primary !important;\n            .z-index-fix {\n                z-index: 1;\n            }\n            .dz-message {\n                opacity: 1;\n                display: block !important;\n                visibility: visible;\n            }\n            .filerFile {\n                display: none;\n            }\n            .dz-message, .dz-message .icon {\n                color: $color-primary !important;\n            }\n        }\n        &.dz-started .fileUpload {\n            display: none;\n        }\n        .dz-preview {\n            width: 100%;\n            min-height: auto;\n            margin-right: 0;\n            margin-bottom: 0;\n            margin-left: 0;\n            padding-bottom: 10px;\n            border-bottom: solid 1px $gray-lighter;\n            &.dz-error {\n                position: relative;\n                .dz-error-message {\n                    display: none;\n                }\n                &:hover .dz-error-message {\n                    display: block;\n                }\n            }\n            .dz-details {\n                min-width: calc(100% - 80px);\n                max-width: calc(100% - 80px);\n                margin-top: 7px;\n                margin-left: 40px;\n                padding: 0;\n                opacity: 1;\n                .dz-filename,\n                .dz-filename:hover,\n                .dz-size {\n                    float: left;\n                    text-align: left;\n                    span {\n                        color: $gray;\n                        border: 0 !important;\n                        background-color: transparent !important;\n                    }\n                }\n            }\n            .dz-remove {\n                background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' class='bi bi-trash' viewBox='0 0 16 16'%3E%3Cpath d='M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6Z'/%3E%3Cpath d='M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1ZM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118ZM2.5 3h11V2h-11v1Z'/%3E%3C/svg%3E%0A\");\n                background-size:contain;\n                display: inline-block;\n                position: absolute;\n                top: 7px;\n                right: 25px;\n                font: 0/0 a;\n                width: 18px;\n                height: 18px;\n            }\n            .dz-error-message {\n                top: 65px;\n                left: 0;\n                width: 100%;\n            }\n            .dz-success-mark,\n            .dz-error-mark {\n                top: 5px;\n                right: 0;\n                left: auto;\n                margin-top: 0;\n                &:before {\n                    color: $gray;\n                }\n                svg {\n                    display: none;\n                }\n            }\n            .dz-success-mark {\n                // Check icon\n                width: 16px;\n                height: 16px;\n                background-image: url(\"data:image/svg+xml,%3Csvg width='13' height='13' viewBox='0 0 1792 1792' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%2370bf2b' d='M1412 734q0-28-18-46l-91-90q-19-19-45-19t-45 19l-408 407-226-226q-19-19-45-19t-45 19l-91 90q-18 18-18 46 0 27 18 45l362 362q19 19 45 19 27 0 46-19l543-543q18-18 18-45zm252 162q0 209-103 385.5t-279.5 279.5-385.5 103-385.5-103-279.5-279.5-103-385.5 103-385.5 279.5-279.5 385.5-103 385.5 103 279.5 279.5 103 385.5z'/%3E%3C/svg%3E%0A\");\n                background-size: contain;\n            }\n            .dz-error-mark {\n                // Remove icon\n                background-image: url(\"data:image/svg+xml,%3Csvg width='13' height='13' viewBox='0 0 1792 1792' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23dd4646' d='M1277 1122q0-26-19-45l-181-181 181-181q19-19 19-45 0-27-19-46l-90-90q-19-19-46-19-26 0-45 19l-181 181-181-181q-19-19-45-19-27 0-46 19l-90 90q-19 19-19 46 0 26 19 45l181 181-181 181q-19 19-19 45 0 27 19 46l90 90q19 19 46 19 26 0 45-19l181-181 181 181q19 19 45 19 27 0 46-19l90-90q19-19 19-46zm387-226q0 209-103 385.5t-279.5 279.5-385.5 103-385.5-103-279.5-279.5-103-385.5 103-385.5 279.5-279.5 385.5-103 385.5 103 279.5 279.5 103 385.5z'/%3E%3C/svg%3E%0A\");\n                width: 16px;\n                height: 16px;\n                background-size: contain;\n            }\n            &.dz-image-preview,\n            &.dz-file-preview {\n                background-color: transparent;\n                .dz-image {\n                    overflow: hidden;\n                    width: 36px;\n                    height: 36px;\n                    border: solid 1px $gray-lighter;\n                    border-radius: 0;\n                    img {\n                        width: 100%;\n                        height: auto;\n                    }\n                }\n            }\n            .dz-progress {\n                top: 18px;\n                left: 0;\n                width: calc(100% - 40px);\n                height: 10px;\n                margin-left: 40px;\n            }\n        }\n        .dz-message {\n            float: right;\n            color: $gray-dropzone;\n            width: 100%;\n            margin: 15px 0 0;\n        }\n        .icon {\n            position: relative;\n            top: 3px;\n            color: $gray-dropzone;\n            font-size: 24px;\n            margin-right: 10px;\n        }\n        .filerFile .related-lookup {\n            @include button-variant($btn-action-color, $btn-action-bgcolor, $btn-action-border, true);\n            float: left !important;\n            overflow: hidden;\n            // makes true that button has correct height #668\n            line-height: $font-size-normal;\n            width: auto !important;\n            height: auto !important;\n            padding: 10px 20px !important;\n            margin-top: 24px;\n            margin-left: 10px;\n            text-align: center !important;\n            cursor: pointer;\n            .cms-icon {\n                color: $white;\n                font-size: 17px;\n                margin: 0 10px 0 0;\n                vertical-align: middle;\n            }\n            &:before {\n                display: none;\n            }\n            .choose-file,\n            .replace-file,\n            .edit-file {\n                color: $white;\n                margin: 0;\n            }\n            .replace-file {\n                display: none;\n            }\n            &.edit {\n                display: none;\n            }\n            &.related-lookup-change {\n                @include button-variant($btn-default-color, $btn-default-bgcolor, $btn-default-border, true);\n                float: right !important;\n                padding: 5px 0 !important;\n                width: 36px !important;\n                height: 36px !important;\n                &:focus {\n                    background-color: $white !important;\n                }\n                span {\n                    text-align: center;\n                    line-height: 24px;\n                }\n                .cms-icon {\n                    color: $btn-default-color;\n                    margin-right: 0 !important;\n                }\n                .choose-file {\n                    display: none;\n                }\n                .replace-file {\n                    display: block;\n                }\n                &.lookup {\n                    display: block !important;\n                }\n                &.edit {\n                    display: block;\n                }\n            }\n        }\n        // makes sure that filer clear button has correct size #669\n        .filerClearer {\n            width: 36px !important;\n            height: 36px !important;\n            color: $color-danger;\n        }\n        .filerFile {\n            position: absolute;\n            top: 9px;\n            // required for django CMS <= 3.1 #673\n            left: 20px;\n            width: calc(100% - 40px);\n            img[src*=nofile] {\n                background-color: $white;\n            }\n            // make sure that text crops if there is not enough space #670\n            span:not(:empty):not(.choose-file):not(.replace-file):not(.edit-file) {\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n                width: calc(100% - 260px);\n                height: 80px;\n                line-height: 80px;\n            }\n            // required for django CMS <= 3.1 #673\n            img {\n                width: 80px;\n                height: 80px;\n                margin-right: 10px;\n                border: solid 1px $gray-lighter;\n                border-radius: $border-radius-base;\n                vertical-align: top;\n                &[src*=\"nofile\"] {\n                    box-sizing: border-box;\n                    margin-right: 0;\n                    border: solid 1px $gray-lighter;\n                    border-radius: $border-radius-base;\n                }\n            }\n            // required for django CMS <= 3.1\n            a {\n                box-sizing: border-box;\n                padding-top: 10px !important;\n            }\n            // required for django CMS <= 3.1 #673\n            span {\n                display: inline-block;\n                color: $gray;\n                font-weight: normal;\n                margin-bottom: 6px;\n                text-align: left;\n                &:empty + .related-lookup {\n                    float: none !important;\n                    margin-left: 0 !important;\n                }\n            }\n            // required for django CMS <= 3.1 #673\n            a.filerClearer {\n                @include button-variant($btn-default-color, $btn-default-bgcolor, $btn-default-border, true);\n                float: right;\n                padding: 5px 0  !important;\n                margin: 24px 0 0 10px;\n                width: 36px;\n                height: 36px;\n                text-align: center;\n                cursor: pointer;\n                span:before {\n                    color: $color-danger !important;\n                }\n                span {\n                    text-align: center;\n                    line-height: 24px;\n                }\n            }\n\n        }\n        &.filer-dropzone-mobile {\n            .filerFile {\n                text-align: center;\n            }\n            .dz-message {\n                overflow: hidden;\n                white-space: nowrap;\n                text-overflow: ellipsis;\n                // make sure that drag and drop widget looks nice on mobile #670\n                margin-top: 75px;\n            }\n            &.js-object-attached .filerFile {\n                text-align: center;\n                &.js-file-selector {\n                    @media screen and (max-width: $screen-tablet-filer) {\n                        .description_text {\n                            text-overflow: ellipsis;\n                            width: calc(100% - 250px);\n                            overflow: hidden;\n                        }\n                    }\n                    >span:not(.choose-file):not(.replace-file):not(.edit-file), .dz-name {\n                        width: calc(100% - 250px);\n                    }\n                }\n            }\n\n        }\n        &.filer-dropzone-folder .filerFile {\n            top: 8px;\n            #id_folder_description_txt {\n                float: left;\n            }\n        }\n\n        @media (max-width: 767px) {\n            flex-grow: 1;\n        }\n\n    }\n}\n\n.filer-dropzone {\n    min-height: 100px !important;\n    .dz-upload {\n        height: 5px;\n        background-color: $color-primary;\n    }\n    .dz-name {\n        overflow: hidden;\n        white-space: nowrap;\n        text-overflow: ellipsis;\n        max-width: calc(100% - 145px);\n    }\n    .dz-thumbnail {\n        display: inline-block;\n        overflow: hidden;\n        vertical-align: top;\n        width: 80px;\n        height: 80px;\n        margin-right: 10px;\n        border: solid 1px $gray-lighter;\n        border-radius: 3px;\n        background: $white url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24'%3E%3Cpath fill='%232980b9' d='M5 2c-1.105 0-2 .9-2 2v18c0 1.1.895 2 2 2h14c1.105 0 2-.9 2-2V8l-6-6z'/%3E%3Cpath fill='%233498db' d='M5 1c-1.105 0-2 .9-2 2v18c0 1.1.895 2 2 2h14c1.105 0 2-.9 2-2V7l-6-6z'/%3E%3Cpath fill='%232980b9' d='m21 7-6-6v4c0 1.1.895 2 2 2z'/%3E%3C/svg%3E\");\n        background-size: contain;\n        img {\n            background: $white;\n            &[src=\"\"],\n            &:not([src]) {\n                width: 104%;\n                height: 104%;\n                margin: -2%;\n            }\n        }\n    }\n}\n\n.filer-dropzone-info-message {\n    position: fixed;\n    bottom: 35px;\n    left: 50%;\n    z-index: 2;\n    text-align: center;\n    width: 270px;\n    max-height: 300px;\n    overflow-y: auto;\n    margin: -50px 0 0 -150px;\n    padding: 15px 15px 0;\n    border-radius: $border-radius-base;\n    background: $white;\n    box-shadow: $base-box-shadow;\n    .icon {\n        font-size: 35px;\n        color: $color-primary;\n    }\n    .text {\n        margin: 5px 0 10px;\n    }\n}\n.filer-dropzone-upload-info {\n    margin-top: 10px;\n    // make sure that file name on upload progress is cut #675\n    .filer-dropzone-file-name {\n        overflow: hidden;\n        white-space: nowrap;\n        text-overflow: ellipsis;\n    }\n    &:empty {\n        margin-top: 0;\n    }\n}\n.filer-dropzone-progress {\n    height: 5px;\n    margin-top: 5px;\n    background-color: $color-primary;\n}\n\n.filer-dropzone-upload-welcome .folder {\n    color: $color-primary;\n    padding: 10px 0 0;\n    margin: 0 -15px;\n    border-top: solid 1px $gray-lighter;\n    img,\n    span {\n        vertical-align: middle;\n    }\n    img {\n        margin-right: 5px;\n    }\n    .folder-inner {\n        overflow: hidden;\n        white-space: nowrap;\n        text-overflow: ellipsis;\n        padding: 0 10px;\n    }\n}\n\n.filer-dropzone-cancel {\n    padding-top: 10px;\n    border-top: solid 1px $gray-lighter;\n    margin: 15px -15px 10px;\n    a {\n        font-size: $font-size-small;\n        color: $gray !important;\n    }\n}\n.filer-dropzone-upload-success,\n.filer-dropzone-upload-canceled {\n    margin: 0 -15px 10px;\n}\n\n.filer-dropzone-upload-count {\n    padding-bottom: 10px;\n    margin: 10px -15px;\n    border-bottom: solid 1px $gray-lighter;\n}\n", ".filer-tooltip-wrapper {\n    position: relative;\n}\n\n.filer-tooltip {\n    position: absolute;\n    left: -30px;\n    right: -30px;\n    color: $gray;\n    text-align: center;\n    font-size: $font-size-small !important;\n    line-height: 15px !important;\n    white-space: normal;\n    margin-top: 5px;\n    padding: 10px;\n    background-color: $white;\n    box-shadow: 0 0 10px rgba(black,.25);\n    border-radius: 5px;\n    z-index: 10;\n    cursor: default;\n    &:before {\n        position: absolute;\n        top: -3px;\n        left: 50%;\n        z-index: -1;\n        content: '';\n        width: 9px;\n        height: 9px;\n        margin-left: -5px;\n        transform: rotate(45deg);\n        background-color: $white;\n    }\n}\n\n.disabled-btn-tooltip {\n    display: none;\n    outline: none;\n}\n", "/*\n * The MIT License\n * Copyright (c) 2012 <PERSON><PERSON> <<EMAIL>>\n */\n@keyframes passing-through {\n    0% {\n        opacity: 0;\n        transform: translateY(40px);\n    }\n    30%,\n    70% {\n        opacity: 1;\n        transform: translateY(0);\n    }\n    100% {\n        opacity: 0;\n        transform: translateY(-40px);\n    }\n}\n@keyframes slide-in {\n    0% {\n        opacity: 0;\n        transform: translateY(40px);\n    }\n    30% {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n@keyframes pulse {\n    0% {\n        transform: scale(1);\n    }\n    10% {\n        transform: scale(1.1);\n    }\n    20% {\n        transform: scale(1);\n    }\n}\n.filer-dropzone,\n.filer-dropzone * {\n    box-sizing: border-box;\n}\n\n.filer-dropzone {\n    min-height: 150px;\n    padding: 20px 20px;\n    border: 2px solid rgba(0, 0, 0, 0.3);\n    background: white;\n}\n.filer-dropzone.dz-clickable {\n    cursor: pointer;\n}\n.filer-dropzone.dz-clickable * {\n    cursor: default;\n}\n.filer-dropzone.dz-clickable .dz-message,\n.filer-dropzone.dz-clickable .dz-message * {\n    cursor: pointer;\n}\n.filer-dropzone.dz-drag-hover {\n    border-style: solid;\n}\n.filer-dropzone.dz-drag-hover .dz-message {\n    opacity: 0.5;\n}\n.filer-dropzone .dz-message {\n    text-align: center;\n    margin: 2em 0;\n}\n.filer-dropzone .dz-preview {\n    display: inline-block;\n    position: relative;\n    vertical-align: top;\n    min-height: 100px;\n    margin: 16px;\n}\n.filer-dropzone .dz-preview:hover {\n    z-index: 1000;\n}\n.filer-dropzone .dz-preview:hover .dz-details {\n    opacity: 1;\n}\n.filer-dropzone .dz-preview.dz-file-preview .dz-image {\n    border-radius: 20px;\n    background: $gray-light;\n    background: linear-gradient(to bottom, $gray-lightest, $gray-lighter);\n}\n.filer-dropzone .dz-preview.dz-file-preview .dz-details {\n    opacity: 1;\n}\n.filer-dropzone .dz-preview.dz-image-preview {\n    background: white;\n}\n.filer-dropzone .dz-preview.dz-image-preview .dz-details {\n    transition: opacity 0.2s linear;\n}\n.filer-dropzone .dz-preview .dz-remove {\n    display: block;\n    font-size: 14px;\n    text-align: center;\n    border: none;\n    cursor: pointer;\n}\n.filer-dropzone .dz-preview .dz-remove:hover {\n    text-decoration: underline;\n}\n.filer-dropzone .dz-preview:hover .dz-details {\n    opacity: 1;\n}\n.filer-dropzone .dz-preview .dz-details {\n    position: absolute;\n    top: 0;\n    left: 0;\n    z-index: 20;\n    color: rgba(0, 0, 0, 0.9);\n    font-size: 13px;\n    line-height: 150%;\n    text-align: center;\n    min-width: 100%;\n    max-width: 100%;\n    padding: 2em 1em;\n    opacity: 0;\n}\n.filer-dropzone .dz-preview .dz-details .dz-size {\n    font-size: 16px;\n    margin-bottom: 1em;\n}\n.filer-dropzone .dz-preview .dz-details .dz-filename {\n    white-space: nowrap;\n}\n.filer-dropzone .dz-preview .dz-details .dz-filename:hover span {\n    border: 1px solid rgba(200, 200, 200, 0.8);\n    background-color: rgba(255, 255, 255, 0.8);\n}\n.filer-dropzone .dz-preview .dz-details .dz-filename:not(:hover) {\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n.filer-dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {\n    border: 1px solid transparent;\n}\n.filer-dropzone .dz-preview .dz-details .dz-filename span,\n.filer-dropzone .dz-preview .dz-details .dz-size span {\n    padding: 0 0.4em;\n    border-radius: 3px;\n    background-color: rgba(255, 255, 255, 0.4);\n}\n.filer-dropzone .dz-preview:hover .dz-image img {\n    transform: scale(1.05, 1.05);\n\n    filter: blur(8px);\n}\n.filer-dropzone .dz-preview .dz-image {\n    display: block;\n    position: relative;\n    overflow: hidden;\n    z-index: 10;\n    width: 120px;\n    height: 120px;\n    border-radius: 20px;\n}\n.filer-dropzone .dz-preview .dz-image img {\n    display: block;\n}\n.filer-dropzone .dz-preview.dz-success .dz-success-mark {\n    animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);\n}\n.filer-dropzone .dz-preview.dz-error .dz-error-mark {\n    opacity: 1;\n    animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);\n}\n.filer-dropzone .dz-preview .dz-success-mark,\n.filer-dropzone .dz-preview .dz-error-mark {\n    display: block;\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    z-index: 500;\n    margin-top: -27px;\n    margin-left: -27px;\n    pointer-events: none;\n    opacity: 0;\n}\n.filer-dropzone .dz-preview .dz-success-mark svg,\n.filer-dropzone .dz-preview .dz-error-mark svg {\n    display: block;\n    width: 54px;\n    height: 54px;\n}\n.filer-dropzone .dz-preview.dz-processing .dz-progress {\n    opacity: 1;\n    transition: all 0.2s linear;\n}\n.filer-dropzone .dz-preview.dz-complete .dz-progress {\n    opacity: 0;\n    transition: opacity 0.4s ease-in;\n}\n.filer-dropzone .dz-preview:not(.dz-processing) .dz-progress {\n    animation: pulse 6s ease infinite;\n}\n.filer-dropzone .dz-preview .dz-progress {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    overflow: hidden;\n    z-index: 1000;\n    width: 80px;\n    height: 16px;\n    margin-top: -8px;\n    margin-left: -40px;\n    border-radius: 8px;\n    pointer-events: none;\n    opacity: 1;\n    background: rgba(255, 255, 255, 0.9);\n}\n.filer-dropzone .dz-preview .dz-progress .dz-upload {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    width: 0;\n    background: $gray-darkest;\n    background: linear-gradient(to bottom, $gray, $gray-darkest);\n    transition: width 300ms ease-in-out;\n}\n.filer-dropzone .dz-preview.dz-error .dz-error-message {\n    display: block;\n}\n.filer-dropzone .dz-preview.dz-error:hover .dz-error-message {\n    pointer-events: auto;\n    opacity: 1;\n}\n.filer-dropzone .dz-preview .dz-error-message {\n    display: block;\n    display: none;\n    position: absolute;\n    top: 130px;\n    left: -10px;\n    z-index: 1000;\n    color: $white;\n    font-size: 13px;\n    width: 140px;\n    padding: 0.5em 1.2em;\n    border-radius: 8px;\n    pointer-events: none;\n    opacity: 0;\n    background: #be2626;\n    background: linear-gradient(to bottom, #be2626, #a92222);\n    transition: opacity 0.3s ease;\n}\n.filer-dropzone .dz-preview .dz-error-message:after {\n    content: \"\";\n    position: absolute;\n    top: -6px;\n    left: 64px;\n    width: 0;\n    height: 0;\n    border-right: 6px solid transparent;\n    border-bottom: 6px solid #be2626;\n    border-left: 6px solid transparent;\n}\n"]}