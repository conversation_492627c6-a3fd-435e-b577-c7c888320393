<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:version="1.0beta2 (2b71d25, 2019-12-03)"
   sodipodi:docname="file.svg"
   id="svg22"
   version="1.1"
   width="24"
   height="24">
  <metadata
     id="metadata28">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs26" />
  <sodipodi:namedview
     inkscape:current-layer="svg22"
     inkscape:window-maximized="0"
     inkscape:window-y="28"
     inkscape:window-x="0"
     inkscape:cy="7.820029"
     inkscape:cx="12"
     inkscape:zoom="28.708333"
     showgrid="false"
     id="namedview24"
     inkscape:window-height="776"
     inkscape:window-width="1440"
     inkscape:pageshadow="2"
     inkscape:pageopacity="0"
     guidetolerance="10"
     gridtolerance="10"
     objecttolerance="10"
     borderopacity="1"
     inkscape:document-rotation="0"
     bordercolor="#666666"
     pagecolor="#ffffff" />
  <g
     id="g56">
    <path
       inkscape:connector-curvature="0"
       id="path10"
       fill="#2980b9"
       d="M 5,2 C 3.8954,2 3,2.9 3,4 v 8 4 6 c 0,1.1 0.8954,2 2,2 h 14 c 1.105,0 2,-0.9 2,-2 V 16 12 8 L 15,2 Z" />
    <path
       inkscape:connector-curvature="0"
       id="path12"
       fill="#3498db"
       d="M 5,1 C 3.8954,1 3,1.9 3,3 v 8 4 6 c 0,1.1 0.8954,2 2,2 h 14 c 1.105,0 2,-0.9 2,-2 V 15 11 7 L 15,1 Z" />
    <path
       inkscape:connector-curvature="0"
       id="path14"
       fill="#2980b9"
       d="M 21,7 15,1 v 4 c 0,1.1 0.895,2 2,2 z" />
  </g>
</svg>
