@font-face {
    font-family: 'feather-icons';
    src: url('../fonts/feather-icons.eot?j5kdrl');
    src: url('../fonts/feather-icons.eot?j5kdrl#iefix') format('embedded-opentype'),
        url('../fonts/feather-icons.ttf?j5kdrl') format('truetype'),
        url('../fonts/feather-icons.woff?j5kdrl') format('woff'),
        url('../fonts/feather-icons.svg?j5kdrl#feather-icons') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.feather-icon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'feather-icons' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.feather-loader:before {
    content: "\e900";
}

.feather-lock:before {
    content: "\e901";
}

.feather-log-in:before {
    content: "\e902";
}

.feather-log-out:before {
    content: "\e903";
}

.feather-mail:before {
    content: "\e904";
}

.feather-map:before {
    content: "\e905";
}

.feather-map-pin:before {
    content: "\e906";
}

.feather-maximize:before {
    content: "\e907";
}

.feather-maximize-2:before {
    content: "\e908";
}

.feather-meh:before {
    content: "\e909";
}

.feather-menu:before {
    content: "\e90a";
}

.feather-message-circle:before {
    content: "\e90b";
}

.feather-message-square:before {
    content: "\e90c";
}

.feather-mic:before {
    content: "\e90d";
}

.feather-mic-off:before {
    content: "\e90e";
}

.feather-minimize:before {
    content: "\e90f";
}

.feather-minimize-2:before {
    content: "\e910";
}

.feather-minus:before {
    content: "\e911";
}

.feather-minus-circle:before {
    content: "\e912";
}

.feather-minus-square:before {
    content: "\e913";
}

.feather-monitor:before {
    content: "\e914";
}

.feather-moon:before {
    content: "\e915";
}

.feather-more-horizontal:before {
    content: "\e916";
}

.feather-more-vertical:before {
    content: "\e917";
}

.feather-mouse-pointer:before {
    content: "\e918";
}

.feather-move:before {
    content: "\e919";
}

.feather-music:before {
    content: "\e91a";
}

.feather-navigation:before {
    content: "\e91b";
}

.feather-navigation-2:before {
    content: "\e91c";
}

.feather-octagon:before {
    content: "\e91d";
}

.feather-package:before {
    content: "\e91e";
}

.feather-paperclip:before {
    content: "\e91f";
}

.feather-pause:before {
    content: "\e920";
}

.feather-pause-circle:before {
    content: "\e921";
}

.feather-pen-tool:before {
    content: "\e922";
}

.feather-percent:before {
    content: "\e923";
}

.feather-phone:before {
    content: "\e924";
}

.feather-phone-call:before {
    content: "\e925";
}

.feather-phone-forwarded:before {
    content: "\e926";
}

.feather-phone-incoming:before {
    content: "\e927";
}

.feather-phone-missed:before {
    content: "\e928";
}

.feather-phone-off:before {
    content: "\e929";
}

.feather-phone-outgoing:before {
    content: "\e92a";
}

.feather-pie-chart:before {
    content: "\e92b";
}

.feather-play:before {
    content: "\e92c";
}

.feather-play-circle:before {
    content: "\e92d";
}

.feather-plus:before {
    content: "\e92e";
}

.feather-plus-circle:before {
    content: "\e92f";
}

.feather-plus-square:before {
    content: "\e930";
}

.feather-pocket:before {
    content: "\e931";
}

.feather-power:before {
    content: "\e932";
}

.feather-printer:before {
    content: "\e933";
}

.feather-radio:before {
    content: "\e934";
}

.feather-refresh-ccw:before {
    content: "\e935";
}

.feather-refresh-cw:before {
    content: "\e936";
}

.feather-repeat:before {
    content: "\e937";
}

.feather-rewind:before {
    content: "\e938";
}

.feather-rotate-ccw:before {
    content: "\e939";
}

.feather-rotate-cw:before {
    content: "\e93a";
}

.feather-rss:before {
    content: "\e93b";
}

.feather-save:before {
    content: "\e93c";
}

.feather-scissors:before {
    content: "\e93d";
}

.feather-search:before {
    content: "\e93e";
}

.feather-send:before {
    content: "\e93f";
}

.feather-server:before {
    content: "\e940";
}

.feather-settings:before {
    content: "\e941";
}

.feather-share:before {
    content: "\e942";
}

.feather-share-2:before {
    content: "\e943";
}

.feather-shield:before {
    content: "\e944";
}

.feather-shield-off:before {
    content: "\e945";
}

.feather-shopping-bag:before {
    content: "\e946";
}

.feather-shopping-cart:before {
    content: "\e947";
}

.feather-shuffle:before {
    content: "\e948";
}

.feather-sidebar:before {
    content: "\e949";
}

.feather-skip-back:before {
    content: "\e94a";
}

.feather-skip-forward:before {
    content: "\e94b";
}

.feather-slack:before {
    content: "\e94c";
}

.feather-slash:before {
    content: "\e94d";
}

.feather-sliders:before {
    content: "\e94e";
}

.feather-smartphone:before {
    content: "\e94f";
}

.feather-smile:before {
    content: "\e950";
}

.feather-speaker:before {
    content: "\e951";
}

.feather-square:before {
    content: "\e952";
}

.feather-star:before {
    content: "\e953";
}

.feather-stop-circle:before {
    content: "\e954";
}

.feather-sun:before {
    content: "\e955";
}

.feather-sunrise:before {
    content: "\e956";
}

.feather-sunset:before {
    content: "\e957";
}

.feather-tablet:before {
    content: "\e958";
}

.feather-tag:before {
    content: "\e959";
}

.feather-target:before {
    content: "\e95a";
}

.feather-terminal:before {
    content: "\e95b";
}

.feather-thermometer:before {
    content: "\e95c";
}

.feather-thumbs-down:before {
    content: "\e95d";
}

.feather-thumbs-up:before {
    content: "\e95e";
}

.feather-toggle-left:before {
    content: "\e95f";
}

.feather-toggle-right:before {
    content: "\e960";
}

.feather-tool:before {
    content: "\e961";
}

.feather-trash:before {
    content: "\e962";
}

.feather-trash-2:before {
    content: "\e963";
}

.feather-trello:before {
    content: "\e964";
}

.feather-trending-down:before {
    content: "\e965";
}

.feather-trending-up:before {
    content: "\e966";
}

.feather-triangle:before {
    content: "\e967";
}

.feather-truck:before {
    content: "\e968";
}

.feather-tv:before {
    content: "\e969";
}

.feather-twitch:before {
    content: "\e96a";
}

.feather-twitter:before {
    content: "\e96b";
}

.feather-type:before {
    content: "\e96c";
}

.feather-umbrella:before {
    content: "\e96d";
}

.feather-underline:before {
    content: "\e96e";
}

.feather-unlock:before {
    content: "\e96f";
}

.feather-upload:before {
    content: "\e970";
}

.feather-upload-cloud:before {
    content: "\e971";
}

.feather-user:before {
    content: "\e972";
}

.feather-user-check:before {
    content: "\e973";
}

.feather-user-minus:before {
    content: "\e974";
}

.feather-user-plus:before {
    content: "\e975";
}

.feather-users:before {
    content: "\e976";
}

.feather-user-x:before {
    content: "\e977";
}

.feather-video:before {
    content: "\e978";
}

.feather-video-off:before {
    content: "\e979";
}

.feather-voicemail:before {
    content: "\e97a";
}

.feather-volume:before {
    content: "\e97b";
}

.feather-volume-1:before {
    content: "\e97c";
}

.feather-volume-2:before {
    content: "\e97d";
}

.feather-volume-x:before {
    content: "\e97e";
}

.feather-watch:before {
    content: "\e97f";
}

.feather-wifi:before {
    content: "\e980";
}

.feather-wifi-off:before {
    content: "\e981";
}

.feather-wind:before {
    content: "\e982";
}

.feather-x:before {
    content: "\e983";
}

.feather-x-circle:before {
    content: "\e984";
}

.feather-x-octagon:before {
    content: "\e985";
}

.feather-x-square:before {
    content: "\e986";
}

.feather-youtube:before {
    content: "\e987";
}

.feather-zap:before {
    content: "\e988";
}

.feather-zap-off:before {
    content: "\e989";
}

.feather-zoom-in:before {
    content: "\e98a";
}

.feather-zoom-out:before {
    content: "\e98b";
}

.feather-activity:before {
    content: "\e98c";
}

.feather-airplay:before {
    content: "\e98d";
}

.feather-alert-circle:before {
    content: "\e98e";
}

.feather-alert-octagon:before {
    content: "\e98f";
}

.feather-alert-triangle:before {
    content: "\e990";
}

.feather-align-center:before {
    content: "\e991";
}

.feather-align-justify:before {
    content: "\e992";
}

.feather-align-left:before {
    content: "\e993";
}

.feather-align-right:before {
    content: "\e994";
}

.feather-anchor:before {
    content: "\e995";
}

.feather-aperture:before {
    content: "\e996";
}

.feather-archive:before {
    content: "\e997";
}

.feather-arrow-down:before {
    content: "\e998";
}

.feather-arrow-down-circle:before {
    content: "\e999";
}

.feather-arrow-down-left:before {
    content: "\e99a";
}

.feather-arrow-down-right:before {
    content: "\e99b";
}

.feather-arrow-left:before {
    content: "\e99c";
}

.feather-arrow-left-circle:before {
    content: "\e99d";
}

.feather-arrow-right:before {
    content: "\e99e";
}

.feather-arrow-right-circle:before {
    content: "\e99f";
}

.feather-arrow-up:before {
    content: "\e9a0";
}

.feather-arrow-up-circle:before {
    content: "\e9a1";
}

.feather-arrow-up-left:before {
    content: "\e9a2";
}

.feather-arrow-up-right:before {
    content: "\e9a3";
}

.feather-at-sign:before {
    content: "\e9a4";
}

.feather-award:before {
    content: "\e9a5";
}

.feather-bar-chart:before {
    content: "\e9a6";
}

.feather-bar-chart-2:before {
    content: "\e9a7";
}

.feather-battery:before {
    content: "\e9a8";
}

.feather-battery-charging:before {
    content: "\e9a9";
}

.feather-bell:before {
    content: "\e9aa";
}

.feather-bell-off:before {
    content: "\e9ab";
}

.feather-bluetooth:before {
    content: "\e9ac";
}

.feather-bold:before {
    content: "\e9ad";
}

.feather-book:before {
    content: "\e9ae";
}

.feather-bookmark:before {
    content: "\e9af";
}

.feather-book-open:before {
    content: "\e9b0";
}

.feather-box:before {
    content: "\e9b1";
}

.feather-briefcase:before {
    content: "\e9b2";
}

.feather-calendar:before {
    content: "\e9b3";
}

.feather-camera:before {
    content: "\e9b4";
}

.feather-camera-off:before {
    content: "\e9b5";
}

.feather-cast:before {
    content: "\e9b6";
}

.feather-check:before {
    content: "\e9b7";
}

.feather-check-circle:before {
    content: "\e9b8";
}

.feather-check-square:before {
    content: "\e9b9";
}

.feather-chevron-down:before {
    content: "\e9ba";
}

.feather-chevron-left:before {
    content: "\e9bb";
}

.feather-chevron-right:before {
    content: "\e9bc";
}

.feather-chevrons-down:before {
    content: "\e9bd";
}

.feather-chevrons-left:before {
    content: "\e9be";
}

.feather-chevrons-right:before {
    content: "\e9bf";
}

.feather-chevrons-up:before {
    content: "\e9c0";
}

.feather-chevron-up:before {
    content: "\e9c1";
}

.feather-chrome:before {
    content: "\e9c2";
}

.feather-circle:before {
    content: "\e9c3";
}

.feather-clipboard:before {
    content: "\e9c4";
}

.feather-clock:before {
    content: "\e9c5";
}

.feather-cloud:before {
    content: "\e9c6";
}

.feather-cloud-drizzle:before {
    content: "\e9c7";
}

.feather-cloud-lightning:before {
    content: "\e9c8";
}

.feather-cloud-off:before {
    content: "\e9c9";
}

.feather-cloud-rain:before {
    content: "\e9ca";
}

.feather-cloud-snow:before {
    content: "\e9cb";
}

.feather-code:before {
    content: "\e9cc";
}

.feather-codepen:before {
    content: "\e9cd";
}

.feather-codesandbox:before {
    content: "\e9ce";
}

.feather-coffee:before {
    content: "\e9cf";
}

.feather-columns:before {
    content: "\e9d0";
}

.feather-command:before {
    content: "\e9d1";
}

.feather-compass:before {
    content: "\e9d2";
}

.feather-copy:before {
    content: "\e9d3";
}

.feather-corner-down-left:before {
    content: "\e9d4";
}

.feather-corner-down-right:before {
    content: "\e9d5";
}

.feather-corner-left-down:before {
    content: "\e9d6";
}

.feather-corner-left-up:before {
    content: "\e9d7";
}

.feather-corner-right-down:before {
    content: "\e9d8";
}

.feather-corner-right-up:before {
    content: "\e9d9";
}

.feather-corner-up-left:before {
    content: "\e9da";
}

.feather-corner-up-right:before {
    content: "\e9db";
}

.feather-cpu:before {
    content: "\e9dc";
}

.feather-credit-card:before {
    content: "\e9dd";
}

.feather-crop:before {
    content: "\e9de";
}

.feather-crosshair:before {
    content: "\e9df";
}

.feather-database:before {
    content: "\e9e0";
}

.feather-delete:before {
    content: "\e9e1";
}

.feather-disc:before {
    content: "\e9e2";
}

.feather-divide:before {
    content: "\e9e3";
}

.feather-divide-circle:before {
    content: "\e9e4";
}

.feather-divide-square:before {
    content: "\e9e5";
}

.feather-dollar-sign:before {
    content: "\e9e6";
}

.feather-download:before {
    content: "\e9e7";
}

.feather-download-cloud:before {
    content: "\e9e8";
}

.feather-dribbble:before {
    content: "\e9e9";
}

.feather-droplet:before {
    content: "\e9ea";
}

.feather-edit:before {
    content: "\e9eb";
}

.feather-edit-2:before {
    content: "\e9ec";
}

.feather-edit-3:before {
    content: "\e9ed";
}

.feather-external-link:before {
    content: "\e9ee";
}

.feather-eye:before {
    content: "\e9ef";
}

.feather-eye-off:before {
    content: "\e9f0";
}

.feather-facebook:before {
    content: "\e9f1";
}

.feather-fast-forward:before {
    content: "\e9f2";
}

.feather-feather:before {
    content: "\e9f3";
}

.feather-figma:before {
    content: "\e9f4";
}

.feather-file:before {
    content: "\e9f5";
}

.feather-file-minus:before {
    content: "\e9f6";
}

.feather-file-plus:before {
    content: "\e9f7";
}

.feather-file-text:before {
    content: "\e9f8";
}

.feather-film:before {
    content: "\e9f9";
}

.feather-filter:before {
    content: "\e9fa";
}

.feather-flag:before {
    content: "\e9fb";
}

.feather-folder:before {
    content: "\e9fc";
}

.feather-folder-minus:before {
    content: "\e9fd";
}

.feather-folder-plus:before {
    content: "\e9fe";
}

.feather-framer:before {
    content: "\e9ff";
}

.feather-frown:before {
    content: "\ea00";
}

.feather-gift:before {
    content: "\ea01";
}

.feather-git-branch:before {
    content: "\ea02";
}

.feather-git-commit:before {
    content: "\ea03";
}

.feather-github:before {
    content: "\ea04";
}

.feather-gitlab:before {
    content: "\ea05";
}

.feather-git-merge:before {
    content: "\ea06";
}

.feather-git-pull-request:before {
    content: "\ea07";
}

.feather-globe:before {
    content: "\ea08";
}

.feather-grid:before {
    content: "\ea09";
}

.feather-hard-drive:before {
    content: "\ea0a";
}

.feather-hash:before {
    content: "\ea0b";
}

.feather-headphones:before {
    content: "\ea0c";
}

.feather-heart:before {
    content: "\ea0d";
}

.feather-help-circle:before {
    content: "\ea0e";
}

.feather-hexagon:before {
    content: "\ea0f";
}

.feather-home:before {
    content: "\ea10";
}

.feather-image:before {
    content: "\ea11";
}

.feather-inbox:before {
    content: "\ea12";
}

.feather-info:before {
    content: "\ea13";
}

.feather-instagram:before {
    content: "\ea14";
}

.feather-italic:before {
    content: "\ea15";
}

.feather-key:before {
    content: "\ea16";
}

.feather-layers:before {
    content: "\ea17";
}

.feather-layout:before {
    content: "\ea18";
}

.feather-life-buoy:before {
    content: "\ea19";
}

.feather-link:before {
    content: "\ea1a";
}

.feather-link-2:before {
    content: "\ea1b";
}

.feather-linkedin:before {
    content: "\ea1c";
}

.feather-list:before {
    content: "\ea1d";
}
