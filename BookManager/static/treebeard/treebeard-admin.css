/* Treebeard Admin */

#roots {
    margin: 0;
    padding: 0;
}

#roots li {
    list-style: none;
    padding: 5px !important;
    line-height: 13px;
    border-bottom: 1px solid #EEE;
}

#roots li a {
    font-weight: bold;
    font-size: 12px;
}

#roots li input {
    margin: 0 5px;
}

.oder-grabber {
    width: 1.5em;
    text-align: center;
}

.drag-handler span {
    width: 16px;
    background: transparent url(expand-collapse.png) no-repeat left -48px;
    height: 16px;
    margin: 0 5px;
    display: inline-block;
}

.drag-handler span.active {
    background: transparent url(expand-collapse.png) no-repeat left -32px;
    cursor: move;
}

.spacer {
    width: 10px;
    margin: 0 10px;
}

.collapse {
    width: 16px;
    height: 16px;
    display: inline-block;
    text-indent: -999px;
}

.collapsed {
    background: transparent url(expand-collapse.png) no-repeat left -16px;
}

.expanded {
    background: transparent url(expand-collapse.png) no-repeat left 0;
}

#drag_line {
    border-top: 5px solid #A0A;
    background: #A0A;
    display: block;
    position: absolute;
}

#drag_line span {
    position: relative;
    display: block;
    width: 100px;
    background: #FFD;
    color: #000;
    left: 100px;
    text-align: center;
    border: 1px solid #000;
    vertical-align: center;
}

/*tr:target { I'm handling the highlight with js to have more control
background-color: #FF0;
}*/
