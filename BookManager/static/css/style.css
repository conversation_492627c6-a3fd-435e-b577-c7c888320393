body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', sans-serif;
    background-color: #f9f7f1;
    color: #333;
}

.container {
    max-width: 960px;
    margin: 0 auto;
    padding: 20px;
}



header {
    background-color: #3e4e42;
    padding: 10px 20px !important;
    color: white;
    border-radius: 8px;
    margin-bottom: 0 !important;
}

header h1 {
    margin: 0;
}

nav {
    margin-top: 5px !important;
}

nav a {
    color: #e2dfd2;
    text-decoration: none;
    margin-right: 15px;
    font-weight: bold;
}

nav a:hover {
    text-decoration: underline;
}

.intro {
    background-color: #ffffff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-top: 30px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #3e4e42;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    margin-top: 15px;
}

.btn:hover {
    background-color: #2e3d30;
}

footer {
    text-align: center;
    margin-top: 40px;
    font-size: 14px;
    color: #777;
}


.book-table {
    width: 80%;
    margin: 2rem auto;
    border-collapse: collapse;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.book-table th, .book-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.book-table th {
    background-color: #f4f4f4;
    font-weight: bold;
}

.book-table tr:hover {
    background-color: #f9f9f9;
}

.site-title {
    font-size: 28px;           /* 固定字体大小 */
    font-weight: bold;
    margin: 0;
    padding: 0.5rem 1rem;
    color: white;
    display: inline-block;
    vertical-align: middle;
}

/* 广告栏样式 */
.main-layout {
    display: flex !important;
    min-height: 100vh !important;
    position: relative !important;
}

.sidebar-ad {
    width: 200px !important;
    color: white !important;
    padding: 20px !important;
    position: fixed !important;
    height: 100vh !important;
    overflow-y: auto !important;
    z-index: 99999 !important;
    top: 0 !important;
    border: 5px solid #000 !important;
}

.sidebar-ad.left {
    left: 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.sidebar-ad.right {
    right: 0 !important;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.main-content {
    flex: 1 !important;
    margin-left: 220px !important;
    margin-right: 220px !important;
    padding: 2rem !important;
    min-height: 100vh !important;
    background-color: #f9f5ec !important;
}

.ad-item {
    background: rgba(255,255,255,0.3) !important;
    border-radius: 10px !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
    text-align: center !important;
    border: 2px solid white !important;
}

.ad-item h4 {
    margin-bottom: 10px !important;
    font-size: 16px !important;
    font-weight: bold !important;
    color: white !important;
}

.ad-item p {
    font-size: 14px !important;
    margin-bottom: 0 !important;
    line-height: 1.4 !important;
    color: white !important;
}

.ad-icon {
    font-size: 24px !important;
    margin-bottom: 10px !important;
    display: block !important;
}

/* 响应式设计 */
@media (max-width: 1000px) {
    .sidebar-ad {
        display: none !important;
    }
    .main-content {
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding: 2rem !important;
    }
}
