{"version": 3, "file": "static/djangocms_text/css/bundle.tiptap.min.css", "mappings": "AAAA,wCACI,WAAY,CAGZ,yCAA0C,CAE1C,iBAAkB,CAClB,eAAgB,CAFhB,qBAAsB,CAStB,cAAe,CAHf,4BAA8B,CAF9B,kCAAmC,CAGnC,iCAAkC,CAClC,4BAA8B,CAX9B,iBAAkB,CAClB,kBAAmB,CAOnB,2BAA4B,CAK5B,IAEI,kBAAmB,CADnB,iBAEJ,CACA,sBACI,kBACJ,CACA,eAKI,YAAa,CADb,qBAAsB,CADtB,sBAAuB,CAIvB,eAAgB,CANhB,WAAY,CACZ,iBAAkB,CAIlB,WAEJ,CACJ,CAEA,uBACE,GACE,iBACF,CACF,CAEA,wEACI,4CACJ,CCzCA,2BACI,uBACI,eAAiB,CACjB,iBAAkB,CAClB,iBAEI,2TAA4T,CAC5T,wBAAyB,CAFzB,sBAGJ,CACA,wBACI,eACJ,CACA,mEAEI,kBACJ,CACA,wBASI,2BAA4B,CAD5B,wCAAyC,CAKzC,yBAA0B,CAD1B,2BAA4B,CAE5B,mDAAoD,CAJpD,gBAAiB,CAHjB,sBAAuB,CADvB,oBAAqB,CAFrB,oBAAqB,CACrB,aAAc,CAFd,iBAAkB,CAQlB,WAAY,CATZ,iBAAkB,CADlB,SAAU,CAcV,qBACI,SAAU,CACV,eACJ,CACA,IAII,kDAAmD,CADnD,eAAmB,CAFnB,iBAAmB,CACnB,kBAAmB,CAGnB,aACI,kBACJ,CACA,uBAEI,mCAAoC,CADpC,eAEJ,CACA,wBAEI,qBAAsB,CADtB,iBAEJ,CACA,uBACI,cACJ,CACA,6BACI,6BAA8B,CAC9B,sBACJ,CACJ,CACJ,CACJ,CACJ,CC7DA,2CAEI,qCACI,YACJ,CAEA,6CACI,YAAa,CACb,gBAAiB,CACjB,WAAY,CAEZ,cAAe,CADf,UAAW,CAEX,eACI,WACJ,CACA,aAGI,kEAAoE,CAFpE,iBAAkB,CAClB,8BAAkC,CAElC,gBACI,YACJ,CACJ,CACJ,CACJ,CAEA,2BAaI,iBAAkB,CAZlB,mBACI,8DAAgE,CAGhE,iBAAkB,CADlB,eAAgB,CADhB,WAGJ,CACA,yBACI,wBAAyB,CACzB,yBAA0B,CAE1B,eAAgB,CADhB,eAEJ,CAEA,EACI,mBACJ,CACA,aACI,mBAAoB,CACpB,EACI,mBACJ,CACA,6BACI,yBAA0B,CAC1B,kBACJ,CACJ,CACA,eAqBI,0CAA4C,CAH5C,4DAA8D,CAjB9D,uBAAwB,CAkBxB,mDAAoD,CACpD,qCAAuC,CAJvC,YAAa,CACb,kBAAmB,CANnB,cAAe,CACf,eAAmB,CAFnB,kBAAqB,CAYrB,SAAU,CAdV,iBAAmB,CAOnB,kBAAmB,CADnB,iBAAkB,CADlB,iBAAkB,CARlB,4CACI,kBACJ,CAgBA,YAKI,kBAAmB,CAFnB,mBAAoB,CACpB,sBAAuB,CAFvB,QAAS,CADT,SAKJ,CACJ,CACA,uBAYI,8DAAmB,CAAnB,kBAAmB,CAJnB,0BAA2B,CAC3B,2BAA4B,CAE5B,eAAgB,CALhB,qBAAsB,CALtB,aAAc,CAMd,YAAa,CAHb,eAAgB,CAFhB,KAAM,CACN,kBAAmB,CAEnB,UAAW,CAQX,kBACI,sBAAuB,CACvB,oBACJ,CACJ,CACA,qBAYI,0CAA4C,CAF5C,qBAAwB,CAGxB,iBAAkB,CAFlB,qCAAuC,CAFvC,cAAe,CAFf,mBAAoB,CACpB,sBAAuB,CAQvB,eAAgB,CAChB,yBAA4B,CAf5B,iBAAkB,CAYlB,iBAAkB,CAClB,qBAAsB,CAdtB,oBAAuB,CAEvB,GACI,4BAA6B,CAC7B,eACJ,CAYA,kBACI,+DACJ,CACA,8BAEI,wDAA4D,CAD5D,iDAEJ,CACA,WACI,2BAA4B,CAC5B,kBAAmB,CACnB,mBACJ,CACA,WAEI,eAAiB,CACjB,yBAA4B,CAF5B,iBAAkB,CAGlB,QACI,WAAY,CAGZ,eAAiB,CAFjB,sBAAuB,CACvB,uBAEJ,CACJ,CACA,IACI,cAAe,CAGf,aAAc,CAFd,qBAAsB,CACtB,YAEJ,CACJ,CACA,kBAWI,0CAA4C,CAF5C,4DAA8D,CAP9D,eAAgB,CAQhB,mDAAoD,CATpD,qCAAuC,CAMvC,YAAa,CACb,aAAc,CAHd,sBAAuB,CACvB,oBAAqB,CAMrB,iBAAmB,CARnB,iBAAkB,CADlB,iBAAkB,CAUlB,WACI,gBAAiB,CACjB,cAAe,CACf,SAAU,CACV,OAEI,qBAAsB,CACtB,mCAAsC,CAFtC,gBAAiB,CAGjB,MACI,QACJ,CACJ,CACJ,CACA,UACI,gBAAiB,CACjB,eAAgB,CAChB,gBAAiB,CACjB,QACI,wDAA0D,CAE1D,oBAAsB,CADtB,iBAAmB,CAEnB,wBACJ,CACA,OAMI,gBAAiB,CACjB,kBAAmB,CANnB,GAEI,uBAAyB,CACzB,0BAA4B,CAF5B,YAGJ,CAGJ,CACJ,CACJ,CACA,WACI,eAAgB,CAChB,gBAAiB,CACjB,YACI,sCACJ,CACA,QACI,uCACJ,CAEJ,CACA,4BAOI,YAAa,CAFb,WAAY,CAHZ,mBAAoB,CACpB,oBAAqB,CAFrB,cAAe,CAGf,UAAW,CAEX,UAEJ,CACA,6CACI,YACJ,CACJ", "sources": ["webpack://djangocms-text/./private/css/cms.balloon-toolbar.css", "webpack://djangocms-text/./private/css/cms.linkfield.css", "webpack://djangocms-text/./private/css/cms.tiptap.css"], "sourcesContent": [".cms-editor-inline-wrapper .cms-balloon {\n    --size: 1rem;\n    position: absolute;\n    visibility: visible;\n    background-color: var(--dca-gray-lightest);\n    color: var(--dca-gray);\n    border-radius: 3px;\n    box-shadow: none;\n    /* box-shadow: 0 1.5px 1.5px rgba(var(--dca-shadow),.4); */\n    inset-inline-end: calc(100% + 1rem);\n    width: calc(1.6*var(--size));\n    height:  calc(1.6*var(--size));\n    line-height: calc(1.3*var(--size));\n    padding: calc(0.3*var(--size));\n    cursor: pointer;\n    svg {\n        width: var(--size);\n        height: var(--size);\n    }\n    &.show [role=\"menubar\"] {\n        visibility: visible;\n    }\n    [role=\"menubar\"] {\n        padding: 3px;\n        position: absolute;\n        inset-block-start: 100%;\n        inset-block-end: unset;\n        bottom: unset;\n        width: unset;\n        max-width: 100vw;\n    }\n}\n\n@keyframes delayedHide {\n  to {\n    visibility: hidden;\n  }\n}\n\n.cms-editor-inline-wrapper:not(:has(.ProseMirror-focused)) .cms-balloon {\n    animation: 0s linear 0.2s forwards delayedHide;\n}\n", ".cms-editor-inline-wrapper {\n    .cms-linkfield-wrapper {\n        font-size: 0.8rem;\n        position: relative;\n        input[type=\"text\"] {\n            padding-inline-end: 3em;\n            background: var(--dca-white) url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"16\" fill=\"%23808080\" viewBox=\"0 0 16 16\"><path d=\"M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z\"/></svg>') no-repeat inline-end center;\n            background-size: auto 1em;\n        }\n        .cms-linkfield-selected {\n            font-weight: bold;\n        }\n        .cms-linkfield-dropdown:not(:empty), .cms-linkfield-dropdown:active {\n            /* Hide dropdown when empty */\n            visibility: visible;\n        }\n        .cms-linkfield-dropdown {\n            z-index: 1;\n            visibility: hidden;\n            position: absolute;\n            max-block-size: 400px;\n            overflow: auto;\n            inset-inline-start: 0;\n            inset-block-start: 100%;\n            border: 1px solid var(--dca-gray-lighter);\n            background: var(--dca-white);\n            inline-size: 120%;\n            resize: both;\n            border-end-start-radius: 4px;\n            border-end-end-radius: 4px;\n            box-shadow: 0 1.5px 1.5px rgba(var(--dca-shadow),.4);\n            .cms-linkfield-error {\n                color: red;\n                font-size: 0.8rem;\n            }\n            div {\n                padding: 0.3rem 6px;\n                white-space: nowrap;\n                font-weight: normal;\n                border-block-end: 1px solid var(--dca-gray-lighter);\n                &:last-child {\n                    border-bottom: none;\n                }\n                &.cms-linkfield-parent {\n                    font-weight: bold;\n                    background: var(--dca-gray-lightest);\n                }\n                &.cms-linkfield-message {\n                    font-style: italic;\n                    color: var(--dca-gray);\n                }\n                &.cms-linkfield-option {\n                    cursor: pointer;\n                }\n                &.cms-linkfield-option:hover {\n                    background: var(--dca-primary);\n                    color: var(--dca-white);\n                }\n            }\n        }\n    }\n}\n", ".app-djangocms_text.model-text.change-form {\n\n    textarea.CMS_Editor, textarea#id_json {\n        display: none;\n    }\n\n    textarea.CMS_Editor + div#id_body_editor.fixed {\n        display: flex;\n        flex-flow: column;\n        height: 100%;\n        width: 100%;\n        max-width: 100%;  /* for djangocms-admin-style */\n        [role=\"menubar\"] {\n            border: none;\n        }\n        .ProseMirror {\n            overflow-y: scroll;\n            padding: 1rem 0.8rem 0.2rem 0.2rem;\n            border-top: 2px solid var(--dca-gray-lighter, var(--hairline-color));\n            &:focus-visible {\n                outline: none;\n            }\n        }\n    }\n}\n\n.cms-editor-inline-wrapper {\n    &.textarea .tiptap {\n        border: 1px solid var(--dca-gray-lighter, var(--hairline-color));\n        padding: 6px;\n        min-height: 3rem;\n        border-radius: 3px;\n    }\n    &.textarea.fixed .tiptap {\n        border-top-left-radius: 0;\n        border-top-right-radius: 0;\n        resize: vertical;\n        overflow-y: auto;\n    }\n    position: relative;\n    a {\n        pointer-events: none;\n    }\n    & cms-plugin {\n        pointer-events: auto;\n        a {\n            pointer-events: none;  /* for text-enabled link plugins */\n        }\n        &.ProseMirror-selectednode > * {\n            outline: 2px solid #fad507;\n            outline-offset: 2px;\n        }\n    }\n    [role=\"menubar\"] {\n        bottom: calc(100% - 1px);\n        &.show {\n            visibility: visible;\n        }\n        [role=\"button\"].show > .dropdown-content {\n            visibility: visible;\n        }\n        padding: 6px 0.8rem;\n        /* border-radius: 3px; */\n        margin: 0  !important;\n        font-size: 1rem;\n        font-weight: normal;\n        visibility: hidden;\n        position: absolute;\n        pointer-events: all;\n        display: flex;\n        flex-flow: row wrap;\n        border: solid 1px var(--dca-gray-light, var(--hairline-color));\n        box-shadow: 0 1.5px 1.5px rgba(var(--dca-shadow),.4);\n        color: var(--dca-black, var(--body-fg));\n        background: var(--dca-white, var(--body-bg));\n        opacity: 1;\n        div.grouper {\n            padding: 0;\n            margin: 0;\n            display: inline-flex;\n            justify-content: center;\n            break-inside: avoid;\n        }\n    }\n    &.fixed [role=\"menubar\"]  {\n        display: block;\n        top: 0;\n        visibility: visible;\n        position: static;\n        width: 100%;\n        box-sizing: border-box;\n        outline: none;\n        border-top-left-radius: 3px;\n        border-top-right-radius: 3px;\n        border: 1px solid var(--dca-gray-lighter, var(--hairline-color));\n        box-shadow: none;\n        border-bottom: none;\n        .dropdown-content {\n            inset-block-start: 100%;\n            inset-inline-start: 0;\n        }\n    }\n    button, [role=\"button\"] {\n        width: auto  !important;  /* for djangocms-admin-style */\n        position: relative;\n        li {\n            color: var(--dca-gray-darker);\n            font-weight: normal;\n        }\n        display: inline-flex;\n        justify-content: center;\n        cursor: pointer;\n        border: none  !important;\n        color: var(--dca-black, var(--body-fg));\n        background: var(--dca-white, var(--body-bg));\n        border-radius: 2px;\n        text-align: center;\n        vertical-align: middle;\n        line-height: 1.2;\n        padding: 6px 4px  !important;\n        &:active, &.active {\n            background: var(--dca-gray-lighter, var(--selected-bg))  !important;\n        }\n        &:hover:not(:disabled),&.show {\n            color: var(--dca-white, var(--button-fg)) !important;\n            background: var(--dca-primary, var(--button-bg))  !important;\n        }\n        &:disabled {\n            color: var(--dca-gray-light);\n            cursor: not-allowed;\n            pointer-events: none;\n        }\n        &.dropdown {\n            position: relative;\n            font-size: 0.8rem;\n            padding: 6px 4px  !important;\n            &:after{\n                content: \"▼\";\n                margin-block-start: 3px;\n                margin-inline-start: 6px;\n                font-size: 0.8rem;\n            }\n        }\n        svg {\n            display: inline;\n            vertical-align: middle;\n            width: 1.2rem;\n            height: 1.2rem;\n        }\n    }\n    .dropdown-content {\n        color: var(--dca-black, var(--body-fg));\n        border-radius: 0;\n        visibility: hidden;\n        position: absolute;\n        inset-block-start: 100%;\n        inset-inline-start: 0;\n        display: flex;\n        flex-flow: row;\n        border: solid 1px var(--dca-gray-light, var(--hairline-color));\n        box-shadow: 0 1.5px 1.5px rgba(var(--dca-shadow),.4);\n        background: var(--dca-white, var(--body-bg));\n        padding: 6px 0.8rem;\n        &.vertical {\n            flex-flow: column;\n            font-size: 1rem;\n            padding: 0;\n            button {\n                text-align: start;\n                justify-content: start;\n                padding: 8px 2rem 8px 1rem  !important;\n                small {\n                    margin: 0;\n                }\n            }\n        }\n        &.plugins {\n            max-height: 16rem;\n            overflow-y: auto;\n            text-align: start;\n            .header {\n                background: var(--dca-gray-lighter, var(--hairline-color));\n                padding-top: 0.4rem;\n                padding-bottom: 0.4rem;\n                padding-inline-start: 6px;\n            }\n            button {\n                > * {\n                    width: 1.2rem;\n                    margin-inline-end: 0.8rem;\n                    margin-inline-start: -0.3rem;\n                }\n                text-align: start;\n                white-space: nowrap;\n            }\n        }\n    }\n    span:empty {\n        margin-left: 4px;\n        margin-right: 4px;\n        &.separator {\n            border-left: dashed 1px var(--dca-gray);\n        }\n        &.space {\n            border-left: dashed 1px var(--dca-white);\n        }\n\n    }\n    .dropback, .toolbar-dropback {\n        position: fixed;\n        inset-block-start: 0;\n        inset-inline-start: 0;\n        width: 100%;\n        height: 100%;\n        z-index: -1;\n        cursor: unset;  /* browser default */\n    }\n    :not(:has(.dropdown.show)) .toolbar-dropback {\n        display: none;\n    }\n}\n"], "names": [], "sourceRoot": ""}