.cms-admin.app-djangocms_text.model-text .form-row.field-body > div > div {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.popup.cms-admin.app-djangocms_text.model-text #content {
    padding: 0;
    margin: 0;
    .form-row {
        padding: 0;
    };
    label {
        display: none;
    }
}


#cms-top dialog.cms-dialog {
    padding: 0;
    resize: both;
    top: 50%;
    left: 50%;
    inset-inline-start: 50%;
    inset-inline-end: unset;
    transform: translate(calc(-50% + 250px), calc(-50% + 121px));
    width: 32rem;
    height: 24rem;
    min-height: 16rem;
    min-width: 16rem;
    .cms-modal-foot {
        margin-inline-end: 1rem;
        .cms-modal-buttons {
            padding-inline-end: 10px;
        }
    }
    .cms-modal-body iframe {
        width: 100%;
        height: 100%;
        border: none;
    }
}

[dir="rtl"] dialog.cms-dialog {
    inset-inline-start: unset;
    inset-inline-end: 50%;
}

dialog.cms-form-dialog {
    &::before {
        position: absolute;
        background: var(--dca-white);
        border: 1px solid var(--dca-gray-light);
        box-shadow: 0 0 10px rgba(var(--dca-shadow), .25);
        height: 10px;
        width:  10px;
        left: 24px;
        top: 8px;
        transform: rotate(-135deg);
        transform-origin: 0 0;
        content: "";
    }
    &.right::before {
        right: 24px;
        left: auto;
    }
    &::after {
        position: absolute;
        background: var(--dca-white);
        height: 10px;
        left: 10px;
        top: 0;
        width: 40px;
        content: "";
    }
    &.right::after {
        right: 10px;
        left: auto;
    }
    .dropback {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        cursor: unset;  /* browser default */
    }
    z-index: 1001;
    position: absolute;
    margin: unset;
    left: auto;
    transform: translate(-50%, -50%);
    min-width: 200px;
    padding: 10px 15px;
    background-color: var(--dca-white);
    border: 1px solid var(--dca-gray-light);
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(var(--dca-shadow), .25);
    form {
        display: flex;
        flex-flow: row;
        justify-content: space-between;
        align-content: baseline;
        align-items: center;
    }
    .cancel {
        display: inline-flex;
        color: #f00;
        margin-left: 0.5em;
        margin-right: 0.5em;
        cursor: pointer;
    }
    .submit {
        display: inline-flex;
        color: #693;
        cursor: pointer;
    }
}


form.cms-form {
    display: flex;
    flex-flow: row;
    justify-content: space-between;
    align-content: baseline;
    align-items: center;
    text-align: start;
    zoom: 1;
    input, select {
        min-width: 200px;
        width: 100%;
        margin-bottom: 3px;
        font-size: 0.8rem;
        min-height: 1rem;
        line-height: unset;
        height: unset;
        padding: 3px 6px !important;
    }
    select {  /* !important for djangocms-admin-style */
        background: var(--dca-white) url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="16" fill="%23808080" viewBox="0 0 16 16"><path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/></svg>') no-repeat right center  !important;
        background-size: auto 1em  !important;
        appearance: none;
    }
    label {
        font-size: 0.7rem;
        padding-bottom: 4px;
    }
    hr {
        margin: 0.3em -1em;
    }
    .cms-form-buttons {
        display: inline-flex;
        margin-inline-start: 1em;
        .cancel {
            color: #f00;
            cursor: pointer;
            zoom: 1.2;
        }
        .submit {
            margin-left: 0.5em;
            margin-right: 0.5em;
            color: #693;
            cursor: pointer;
            zoom: 1.2;
        }
    }
}
