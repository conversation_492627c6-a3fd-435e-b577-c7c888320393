{"version": 3, "file": "static/djangocms_text/js/editor.js", "mappings": ";;;;;;AAAa;;AAEb;AACA;AACA;;AAEA;AACA,gBAAgB;AAChB,wBAAwB;;AAExB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,kCAAkC;AAClC,gDAAgD;AAChD;;AAEA;AACA;AACA;AACA;AACA,iFAAiF;AACjF;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mFAAmF;AACnF;AACA,sBAAsB,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;;AAErB;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,8CAA8C;AAC9C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA,iBAAiB;AACjB;AACA,KAAK;;AAEL;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,yCAAyC;AACzC;AACA;AACA,iBAAiB;AACjB;AACA;AACA,yBAAyB,mDAAmD;AAC5E,iBAAiB;AACjB;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "sources": ["webpack://djangocms-text/./private/js/cms.editor.js"], "sourcesContent": ["\"use strict\";\n\nwindow.CMS_Editor = {\n    // CMS Editor\n    // ---------------\n\n    _editors: [],\n    _options: {},\n    _editor_settings: {},\n\n    // CMS Editor: init\n    // Initialize a single editor\n    init: function (el) {\n        const editor_type = el.dataset.type || 'HTMLField';\n        let options, content;\n\n        // Get content: json > textarea > innerHTML\n        if (el.dataset.json) {\n            content = JSON.parse(el.dataset.json);\n        } else  {\n            content = el.innerHTML;\n        }\n        if (el.tagName === 'TEXTAREA') {\n            el.visible = false;\n            content = el.value;\n            // el = el.insertAdjacentElement('afterend', document.createElement('div'));\n        }\n        if (!el.id) {\n            el.id = \"cms-edit-\" + Math.random().toString(36).slice(2, 9);\n        }\n        const settings = this.get_settings(el);\n        // Element options overwrite\n        options = Object.assign({},\n            this._options[editor_type],\n            settings.options || {},\n            JSON.parse(el.dataset.options || '{}')\n        );\n\n        window.cms_editor_plugin.create(\n            el,\n            el.tagName !== 'TEXTAREA',\n            content, options,\n            el.tagName !== 'TEXTAREA' ? () => CMS_Editor.save_data(el) : () => {},\n        );\n        this._editors.push(el);\n    },\n\n    // CMS Editor: initInlineEditors\n    // Register all plugins on the page for inline editing\n    // This is called from init_all\n    initInlineEditors: function () {\n        if (window.CMS === undefined || window.CMS._plugins === undefined) {\n            // Check the CMS frontend for plugins\n            // no plugins -> no inline editors\n            return;\n        }\n        const plugins = window.CMS._plugins;\n\n        CMS_Editor.observer = this.observer || new IntersectionObserver(function (entries, opts) {\n            entries.forEach((entry) => {\n                if (entry.isIntersecting) {\n                    var plugin_id = entry.target.dataset.cmsPluginId;\n                    var url = entry.target.dataset.cmsEditUrl;\n                    CMS_Editor.init(entry.target);\n                }\n            });\n        }, {\n            root: null,\n            threshold: 0.05\n        });\n\n        plugins.forEach(function (plugin) {\n            if (plugin[1].plugin_type === 'TextPlugin') {\n                const url = plugin[1].urls.edit_plugin;\n                const id = plugin[1].plugin_id;\n                const elements = document.querySelectorAll('.cms-plugin.cms-plugin-' + id);\n                let wrapper;\n\n                if (elements.length > 0) {\n                    if (elements.length === 1 && elements[0].tagName === 'DIV') {  // already wrapped?\n                        wrapper = elements[0].classList.add('cms-editor-inline-wrapper');\n                    } else {  // no, wrap now!\n                        wrapper = document.createElement('div');\n                        wrapper.classList.add('cms-editor-inline-wrapper', 'wrapped');\n                        wrapper = this._wrapAll(elements, wrapper);\n                        wrapper.classList.add('cms-plugin', 'cms-plugin-' + id);\n                        for (let child of wrapper.children) {\n                            child.classList.remove('cms-plugin', 'cms-plugin-' + id);\n                        }\n                    }\n                    wrapper.dataset.cmsEditUrl = url;\n                    wrapper.dataset.cmsPluginId = id;\n\n                    // Catch CMS single click event to highlight the plugin\n                    // Catch CMS double click event if present, since double click is needed by Editor\n                    if (window.CMS) {\n                        window.CMS.$(wrapper).on('dblclick.cms-editor', function (event) {\n                            event.stopPropagation();\n                        });\n                        wrapper.addEventListener('focusin.cms-editor', function () {\n                            this._highlightTextplugin(id);\n                        }, true);\n                    }\n\n                    // Prevent tooltip on hover\n                    document.addEventListener('pointerover.cms-editor', (event) => {\n                        // use time out to let other event handlers (CMS' !) run first.\n                        setTimeout(function () {\n                            // do not show tooltip on inline editing text fields.\n                            CMS.API.Tooltip.displayToggle(false, event.target, '', id);\n                        }, 0);\n                    });\n\n                    this.observer.observe(wrapper);\n                }\n            }\n        }, this);\n\n        window.addEventListener('beforeunload', (event) =>  {\n            if (document.querySelector('.cms-editor-inline-wrapper[data-changed=\"true\"]')) {\n                console.error(\"prevented\");\n\n                event.preventDefault();\n                event.returnValue = true;\n                return 'Do you really want to leave this page?';\n            }\n        });\n    },\n\n    // CMS Editor: get_settings\n    // Get settings from json script element\n    get_settings: function (el) {\n        if (typeof el === \"string\") {\n            if (this._editor_settings[el]) {\n                return this._editor_settings[el];\n            }\n            el = document.getElementById(el);\n        }\n        const settings_el = (\n            document.getElementById(el.dataset.settings) ||\n            document.getElementById('cms-cfg-' + el.dataset.cmsPluginId)\n        );\n        if (settings_el) {\n            this._editor_settings[el.id] = JSON.parse(settings_el.textContent);\n            return this._editor_settings[el.id];\n        }\n        return {};\n    },\n\n    // CMS Editor: init_all\n    init_all: function () {\n        try {\n            CMS_Editor._options = JSON.parse(document.getElementById('cms-editor-cfg').textContent);\n        } catch (e) {\n            CMS_Editor._options = {};\n        }\n        // All textareas with class CMS_Editor: typically on admin site\n        document.querySelectorAll('textarea.CMS_Editor').forEach(\n            (el) => CMS_Editor.init(el), this\n        );\n        // Register all plugins on the page for inline editing\n        CMS_Editor.initInlineEditors();\n    },\n\n    // CMS Editor: destroy\n    destroy_all: function () {\n        while (CMS_Editor._editors.length) {\n            window.cms_editor_plugin.destroy_editor(CMS_Editor._editors.pop());\n        }\n    },\n\n    save_data: function (el, action) {\n        if (el && el.dataset.changed === \"true\") {\n            // CMS.CKEditor.storeCSSlinks();  // store css that ckeditor loaded before save\n            const html = cms_editor_plugin.get_html(el),\n                json = cms_editor_plugin.get_json(el);\n\n            let url = el.dataset.cmsEditUrl;\n            let csrf = el.dataset.cmsCsrfToken;\n            if (window.CMS) {\n                CMS.API.Toolbar.showLoader();\n                url = CMS.API.Helpers.updateUrlWithPath(url);\n                csrf = CMS.config.csrf;\n            }\n\n            fetch(url, {  // send changes\n                method: 'POST',\n                body: new URLSearchParams({\n                    csrfmiddlewaretoken: csrf,\n                    body: html,\n                    json: JSON.stringify(json) || '',\n                    _save: 'Save'\n                }),\n            })\n                .then(response => {\n                        el.dataset.changed = 'false';\n                        if (window.CMS) {\n                            CMS.API.Toolbar.hideLoader();\n                        }\n                        if (action !== undefined) {\n                            action(el, response);\n                        }\n/*\n                    if (instance.child_changed) {\n                        var scripts = $(response).find('script:not([src])').addClass('cms-ckeditor-result');\n\n                        CMS.CKEditor._destroyAll();\n                        scripts.each(function (item, element) {\n                            $('body').append(element);\n                        });\n                    } else {\n                        CMS.CKEditor.loadToolbar();\n                    }\n*/\n                    CMS_Editor._loadToolbar();\n                })\n                .catch(error => {\n                        el.dataset.changed = 'true';\n                    if (window.CMS) {\n                        CMS.API.Messages.open({\n                            message: error.message,\n                            error: true\n                        });\n                    } else {\n                        console.error(error.message);\n                    }\n                });\n        }\n    },\n\n    _resetInlineEditors: function () {\n        CMS_Editor.destroy_all();\n        CMS_Editor.init_all();\n    },\n\n    // CMS Editor: loadToolbar\n    // Load the toolbar after saving for update\n    _loadToolbar: function () {\n        if (window.CMS) {\n            CMS.API.StructureBoard._loadToolbar()\n                .done(function (newToolbar) {\n                    CMS.API.Toolbar._refreshMarkup(CMS.$(newToolbar).find('.cms-toolbar'));\n                })\n                .fail(CMS.API.Helpers.reloadBrowser);\n        }\n    },\n\n    _highlightTextplugin: function (pluginId) {\n        const HIGHLIGHT_TIMEOUT = 800;\n\n        if (window.CMS) {\n            var draggable = CMS.$('.cms-draggable-' + pluginId);\n            var doc = CMS.$(document);\n            var currentExpandmode = doc.data('expandmode');\n\n\n            // expand necessary parents\n            doc.data('expandmode', false);\n            draggable\n                .parents('.cms-draggable')\n                .find('> .cms-dragitem-collapsable:not(\".cms-dragitem-expanded\") > .cms-dragitem-text')\n                .each(function (i, el) {\n                    CMS.$(el).triggerHandler(CMS.Plugin.click);\n                });\n            if (draggable.length > 0) {  // Expanded elements available\n                setTimeout(function () {\n                    doc.data('expandmode', currentExpandmode);\n                });\n                setTimeout(function () {\n                    CMS.Plugin._highlightPluginStructure(draggable.find('.cms-dragitem:first'),\n                        {successTimeout: 200, delay: 2000, seeThrough: true});\n                }, HIGHLIGHT_TIMEOUT);\n            }\n        }\n    },\n\n    // Wrap wrapper around nodes\n    // Just pass a collection of nodes, and a wrapper element\n    _wrapAll: function (nodes, wrapper) {\n        // Cache the current parent and previous sibling of the first node.\n        const parent = nodes[0].parentNode;\n        const previousSibling = nodes[0].previousSibling;\n\n        // Place each node in wrapper.\n        //  - If nodes is an array, we must increment the index we grab from\n        //    after each loop.\n        //  - If nodes is a NodeList, each node is automatically removed from\n        //    the NodeList when it is removed from its parent with appendChild.\n        for (var i = 0; nodes.length - i; wrapper.firstChild === nodes[0] && i++) {\n            wrapper.appendChild(nodes[i]);\n        }\n\n        // Place the wrapper just after the cached previousSibling,\n        // or if that is null, just before the first child.\n        const nextSibling = previousSibling ? previousSibling.nextSibling : parent.firstChild;\n        parent.insertBefore(wrapper, nextSibling);\n\n        return wrapper;\n    }\n};\n\ndocument.addEventListener('DOMContentLoaded', window.CMS_Editor.init_all);\nif (window.CMS) {\n    CMS.$(window).on('cms-content-refresh', CMS_Editor._resetInlineEditors);\n}\n"], "names": [], "sourceRoot": ""}