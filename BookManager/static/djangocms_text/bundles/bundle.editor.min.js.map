{"version": 3, "file": "static/djangocms_text/bundles/bundle.editor.min.js", "mappings": ";;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEoC;;;ACtGpC;AACA;AACA;;AAEgD;;AAEhD;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C,kCAAkC;AAClC,gDAAgD;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;;AAErB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,2CAA2C,aAAa;AACxD;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA,SAAS;;AAET;AACA;AACA,uFAAuF;AACvF;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,kEAAkE;AAClE,2DAA2D;AAC3D;AACA;AACA;AACA,8CAA8C,iBAAiB,GAAG,iBAAiB,GAAG,YAAY;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,OAAO;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,wBAAwB;AAC9D;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,sCAAsC,wBAAwB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,gBAAgB;AAC9E;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,iCAAiC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,4FAA4F;AAC5F,uGAAuG;AACvG;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,wBAAwB,GAAG,qCAAqC;AACvF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,IAAI,GAAG,qCAAqC;AAC7D;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,SAAS;AACT;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB,2BAA2B,GAAG,qCAAqC;;AAE1F,2BAA2B,cAAc;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,yCAAyC;AACzC;AACA;AACA,iBAAiB;AACjB;AACA;AACA,yBAAyB,mDAAmD;AAC5E,iBAAiB;AACjB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA", "sources": ["webpack://djangocms-text/./private/js/cms.texteditor.js", "webpack://djangocms-text/./private/js/cms.editor.js"], "sourcesContent": ["/* eslint-env es6 */\n/* jshint esversion: 6 */\n/* global document, window, console */\n\nclass CmsTextEditor {\n    constructor (el, options, save_callback) {\n        this.el = el;\n        this.plugin_identifier = this.find_plugin_identifier();\n        const id_split = this.plugin_identifier.split('-');\n        this.plugin_id = parseInt(id_split[id_split.length-1]);\n        this.options = options;\n        this.events = {};\n        this.save = save_callback;\n        this.init();\n    }\n\n    destroy () {\n        this.el.removeEventListener('focus', this._focus.bind(this));\n        this.el.removeEventListener('blur', this._blur.bind(this));\n        this.el.removeEventListener('input', this._change);\n        this.el.removeEventListener('keydown', this._key_down);\n        this.el.removeEventListener('paste', this._paste);\n        this.el.setAttribute('contenteditable', 'false');\n    }\n\n    init () {\n        this.el.setAttribute('contenteditable', 'plaintext-only');\n        if (!this.el.isContentEditable) {\n            this.el.setAttribute('contenteditable', 'true');\n            this.options.enforcePlaintext = true;\n\n        }\n        this.el.setAttribute('spellcheck', this.options.spellcheck || 'false');\n        this.el.addEventListener('input', this._change);\n        this.el.addEventListener('focus', this._focus.bind(this));\n        this.el.addEventListener('blur', this._blur.bind(this));\n        this.el.addEventListener('keydown', this._key_down);\n        if (this.options.enforcePlaintext) {\n            this.el.addEventListener('paste', this._paste);\n        }\n    }\n\n    _key_down (e) {\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            e.target.blur();\n        }\n        if (e.key === 'Escape') {\n            e.preventDefault();\n            if (e.target.dataset.undo) {\n                e.target.innerText = e.target.dataset.undo;\n                e.target.dataset.changed = false;\n            }\n            e.target.blur();\n        }\n    }\n\n    _focus (e) {\n        this.options.undo = this.el.innerText;\n    }\n\n    _blur (e) {\n        this.save(e.target, (el, response) => {\n            setTimeout(() => {\n                if (e.target.dataset.changed === 'true') {\n                    e.target.innerText = this.options.undo;\n                    e.target.dataset.changed = 'false';\n                    e.target.focus();\n                }\n            }, 100);\n        });\n    }\n\n    _paste (e) {\n        // Upon past only take the plain text\n        e.preventDefault();\n        let text = e.clipboardData.getData('text/plain');\n        if (text) {\n            const [start, end] = [e.target.selectionStart, this.el.selectionEnd];\n            e.target.setRangeText(text, start, end, 'select');\n        }\n    }\n\n    _change (e) {\n        e.target.dataset.changed = 'true';\n    }\n\n    find_plugin_identifier () {\n        const header = 'cms-plugin-';\n\n        for (let cls of this.el.classList) {\n            if (cls.startsWith(header)) {\n                let items = cls.substring(header.length).split('-');\n                if (items.length === 4 && items[items.length-1] == parseInt(items[items.length-1])) {\n                    return items.join('-');\n                }\n            }\n        }\n        return null;\n    }\n}\n\nexport { CmsTextEditor as default };\n", "/* eslint-env es6 */\n/* jshint esversion: 6 */\n/* global window, document, fetch, IntersectionObserver, URLSearchParams, console */\n\nimport CmsTextEditor from './cms.texteditor.js';\n\n// #############################################################################\n// CMS Editor\n// #############################################################################\n\nclass CMSEditor {\n\n    // CMS Editor: constructor\n    // Initialize the editor object\n    constructor() {\n        this._editors = [];\n        this._generic_editors = [];\n        this._global_settings = {};\n        this._editor_settings = {};\n\n        document.addEventListener('DOMContentLoaded', () => {\n            // Get the CMS object from the parent window\n            if (window.CMS !== undefined && window.CMS.config !== undefined) {\n                this.mainWindow = window;\n                this.CMS = window.CMS;\n            } else {\n                this.mainWindow = window.parent;\n                this.CMS = window.parent.CMS;\n            }\n\n            if (this.CMS) {\n                // Only needs to happen on the main window.\n                this.CMS.$(window).on('cms-content-refresh', () => {\n                    if (document.querySelector('template.cms-plugin')) {\n                        // django CMS core does not wrap newly inserted inline editable fields\n                        this.CMS.API.Helpers.reloadBrowser();\n                    } else {\n                        this._resetInlineEditors();\n                    }\n                });\n            }\n            this.initAll();\n        });\n    }\n\n    // CMS Editor: init_all\n    // Initialize all editors on the page\n    initAll () {\n        // Get global options from script element\n        try {\n            this._global_settings = JSON.parse(document.getElementById('cms-editor-cfg').textContent);\n        } catch (e) {\n            this._global_settings = {};\n        }\n\n        // All textareas with class CMS_Editor: typically on admin site\n        document.querySelectorAll('textarea.CMS_Editor').forEach(\n            (el) => this.init(el), this\n        );\n        // Register all plugins on the page for inline editing\n        this.initInlineEditors();\n    }\n\n    // CMS Editor: init\n    // Initialize a single editor\n    init (el) {\n        let content;\n\n        // Get content: json > textarea > innerHTML\n        if (el.dataset.json) {\n            content = JSON.parse(el.dataset.json);\n        } else  {\n            content = el.innerHTML;\n        }\n        if (el.tagName === 'TEXTAREA') {\n            el.visible = false;\n            content = el.value;\n            // el = el.insertAdjacentElement('afterend', document.createElement('div'));\n        }\n        if (!el.id) {\n            el.id = \"cms-edit-\" + Math.random().toString(36).slice(2, 9);\n        }\n        const settings = this.getSettings(el);\n        // Element options overwrite\n        settings.options = Object.assign({},\n            settings.options || {},\n            JSON.parse(el.dataset.options || '{}')\n        );\n\n        // Add event listener to delete data on modal cancel\n        if (settings.revert_on_cancel) {\n            const CMS = this.CMS;\n            const csrf = CMS.config.csrf;\n            CMS.API.Helpers.addEventListener(\n                'modal-close.text-plugin.text-plugin-' + settings.plugin_id,\n                function(e, opts) {\n                    if (!settings.revert_on_cancel || !settings.cancel_plugin_url) {\n                        return;\n                    }\n                    CMS.$.ajax({\n                        method: 'POST',\n                        url: settings.cancel_plugin_url,\n                        data: {\n                            token: settings.action_token,\n                            csrfmiddlewaretoken: csrf\n                        },\n                    }).done(function () {\n                        CMS.API.Helpers.removeEventListener(\n                            'modal-close.text-plugin.text-plugin-' + settings.plugin_id\n                        );\n                        opts.instance.close();\n                    }).fail(function (res) {\n                        CMS.API.Messages.open({\n                            message: res.responseText + ' | ' + res.status + ' ' + res.statusText,\n                            delay: 0,\n                            error: true\n                        });\n                    });\n\n                }\n            );\n        }\n        const inModal = !!document.querySelector(\n            '.app-djangocms_text.model-text.change-form #' + el.id\n        );\n\n        // Create editor\n        if (!el.dataset.cmsType ||el.dataset.cmsType === 'TextPlugin' || el.dataset.cmsType === 'HTMLField') {\n            window.cms_editor_plugin.create(\n                el,\n                inModal,\n                content, settings,\n                el.tagName !== 'TEXTAREA' ? () => this.saveData(el) : () => {\n                }\n            );\n        } else if (el.dataset.cmsType === 'CharField') {\n            this._generic_editors.push(new CmsTextEditor(el, {\n                    spellcheck: el.dataset.spellcheck || 'false',\n                },\n                (el) => this.saveData(el)\n            ));\n        }\n        this._editors.push(el);\n    }\n\n    // CMS Editor: initInlineEditors\n    // Register all plugins on the page for inline editing\n    // This is called from init_all\n    initInlineEditors() {\n        if (this.CMS === undefined || this.CMS._plugins === undefined) {\n            // Check the CMS frontend for plugins\n            // no plugins -> no inline editors\n            return;\n        }\n\n        this.observer = this.observer || new IntersectionObserver( (entries) => {\n            entries.forEach((entry) => {\n                if (entry.isIntersecting) {\n                    this.init(entry.target);\n                }\n            }, this);\n        }, {\n            root: null,\n            threshold: 0.05\n        });\n\n        let generic_inline_fields = document.getElementById('cms-generic-inline-fields') || {};\n        if (generic_inline_fields) {\n            generic_inline_fields = JSON.parse(generic_inline_fields.textContent || '{}');\n        }\n\n        this.CMS._plugins.forEach(function (plugin) {\n            if (plugin[1].type === 'plugin' || plugin[1].type === 'generic') {\n                // Either plugin or frontend editable element\n                const url = plugin[1].urls.edit_plugin;\n                const id = plugin[1].plugin_id;\n                let wrapper;\n\n                if (plugin[1].type === 'plugin' && plugin[1].plugin_type === 'TextPlugin') {\n                    // Text plugin\n                    const elements = document.querySelectorAll('.cms-plugin.cms-plugin-' + id);\n                    wrapper = this._initInlineRichText(elements, url, id);\n                    if (wrapper) {\n                        wrapper.dataset.cmsPluginId = id;\n                        wrapper.dataset.cmsType = 'TextPlugin';\n                    }\n                } else if (plugin[1].type === 'generic') {\n                    // Frontend editable element\n                    const edit_fields = new URL(url.replace('&amp;', '&'), 'https://random-base.org')\n                        .searchParams.get('edit_fields');  // Get the edit_fields parameter from the URL\n                    if (edit_fields && edit_fields.indexOf(',') === -1 && edit_fields !== 'changelist') {\n                        // Single field\n                        const generic_class = plugin[0].split('-');\n                        const search_key = `${generic_class[2]}-${generic_class[3]}-${edit_fields}`;\n                        if (generic_inline_fields[search_key]) {\n                            // Inline editable?\n                            wrapper = this._initInlineRichText(document.getElementsByClassName(plugin[0]), url, id);\n                            if (wrapper) {\n                                wrapper.dataset.cmsCsrfToken = this.CMS.config.csrf;\n                                wrapper.dataset.cmsField = edit_fields;\n                                wrapper.dataset.cmsType = (\n                                    generic_inline_fields[search_key] === 'HTMLFormField' ?\n                                        'HTMLField' : generic_inline_fields[search_key]\n                                );\n                                wrapper.dataset.settings = 'cms-cfg-htmlfield-inline';\n                            }\n                        }\n                    }\n                }\n\n                if (wrapper) {\n                    // Catch CMS single click event to highlight the plugin\n                    // Catch CMS double click event if present, since double click is needed by Editor\n                    this.observer.observe(wrapper);\n                    if (this.CMS) {\n                        // Remove django CMS core's double click event handler which opens an edit dialog\n                        this.CMS.$(wrapper).off('dblclick.cms.plugin')\n                            .on('dblclick.cms-editor', function (event) {\n                            event.stopPropagation();\n                        });\n                        wrapper.addEventListener('focusin.cms-editor', () => {\n                            this._highlightTextplugin(id);\n                        }, true);\n                        // Prevent tooltip on hover\n                        this.CMS.$(wrapper).off('pointerover.cms.plugin pointerout.cms.plugin')\n                            .on('pointerover.cms-editor', function (event) {\n                                window.CMS.API.Tooltip.displayToggle(false, event.target, '', id);\n                                event.stopPropagation();\n                            });\n                    }\n                }\n            }\n        }, this);\n\n        window.addEventListener('beforeunload', (event) =>  {\n            if (document.querySelector('.cms-editor-inline-wrapper[data-changed=\"true\"]')) {\n                event.preventDefault();\n                event.returnValue = true;\n                return 'Do you really want to leave this page?';\n            }\n        });\n    }\n\n    _initInlineRichText(elements, url, id) {\n        let wrapper;\n\n        if (elements.length > 0) {\n            if (elements.length === 1 && elements[0].tagName === 'DIV' || elements[0].tagName === 'CMS-PLUGIN') {\n                // already wrapped?\n                wrapper = elements[0];\n                wrapper.classList.add('cms-editor-inline-wrapper');\n            } else {  // no, wrap now!\n                wrapper = document.createElement('div');\n                wrapper.classList.add('cms-editor-inline-wrapper', 'wrapped');\n                wrapper = this._wrapAll(elements, wrapper);\n            }\n            wrapper.dataset.cmsEditUrl = url;\n            return wrapper;\n        }\n        // No elements found\n        return undefined;\n    }\n\n    /**\n     * Retrieves the settings for the given editor.\n     * If the element is a string, it will be treated as an element's ID.\n     * Reads settings from a json script element.\n     *\n     * @param {string|HTMLElement} el - The element or element's ID to retrieve the settings for.\n     *\n     * @return {Object} - The settings object for the element.\n     */\n    getSettings(el) {\n        if (typeof el === \"string\") {\n            if (this._editor_settings[el]) {\n                return this._editor_settings[el];\n            }\n            el = document.getElementById(el);\n        }\n        const settings_el = (\n            document.getElementById(el.dataset.settings) ||\n            document.getElementById('cms-cfg-' + el.dataset.cmsPluginId)\n        );\n        if (settings_el) {\n            this._editor_settings[el.id] = Object.assign(\n                {},\n                this._global_settings,\n                JSON.parse(settings_el.textContent) || {}\n            );\n            return this._editor_settings[el.id];\n        }\n        return {};\n    }\n\n    /**\n     * Retrieves the list of installed plugins. (Returns empty list of no editor has been initialized.)\n     *\n     * @returns {Array} - The list of installed plugins.\n     */\n    getInstalledPlugins() {\n        if (this._editor_settings) {\n            return this.getSettings(Object.keys(this._editor_settings)[0]).installed_plugins || [];\n        }\n        return [];\n    }\n\n    // CMS Editor: destroy\n    destroyAll() {\n        while (this._editors.length) {\n            const el = this._editors.pop();\n            this.destroyGenericEditor(el);\n            window.cms_editor_plugin.destroyEditor(el);\n        }\n    }\n\n    // CMS Editor: destroyGenericEditor\n    destroyGenericEditor (el) {\n        if (el in this._generic_editors) {\n            this._generic_editors[el].destroy();\n            delete this._generic_editors[el];\n            this._generic_editors.pop(el);\n        }\n    }\n\n    saveData(el, action) {\n        if (el && el.dataset.changed === \"true\") {\n            const html = window.cms_editor_plugin.getHTML(el),\n                json = window.cms_editor_plugin.getJSON(el);\n\n            let url = el.dataset.cmsEditUrl;\n            let csrf = el.dataset.cmsCsrfToken;\n            let field = el.dataset.cmsField;\n            if (this.CMS) {\n                this.CMS.API.Toolbar.showLoader();\n                url = this.CMS.API.Helpers.updateUrlWithPath(url);\n                csrf = this.CMS.config.csrf;\n            }\n\n            let data = {\n                csrfmiddlewaretoken: csrf,\n                _save: 'Save'\n            };\n            if (field) {\n                // FormField data\n                data[field] = el.dataset.cmsType === 'HTMLField' ? html : el.textContent ;\n            } else {\n                // Plugin data\n                data.body = html;\n                data.json = JSON.stringify(json) || '';\n            }\n\n            fetch(url, {\n                method: 'POST',\n                body: new URLSearchParams(data),\n            })\n                .then(response => {\n                        if (action !== undefined) {\n                            action(el, response);\n                        }\n                        if (this.CMS) {\n                            this.CMS.API.Toolbar.hideLoader();\n                        }\n                        return response.text();\n                }).then(body => {\n                    // If the edited field does not force a reload, read the CMS databridge values from the response,\n                    // either directly or from a script tag or from the response using regex.\n                    // This depends on the exact format django CMS core returns it. This will need to be adjusted\n                    // if the format changes.\n                    // Fallback solution is to reload the page as djagocms-text-ckeditor used to do.\n                    const dom = document.createElement('html');\n                    dom.innerHTML = body;\n                    const success_element = dom.querySelectorAll('div.messagelist > div.success').length > 0;\n                    if (!success_element) {\n                        el.dataset.changed = 'true';\n                        // Collect messages\n                        const domMessages = dom.querySelectorAll(\n                            `.field-${field ? field : 'body'} ul.errorlist > li`\n                        );\n                        let messages = [];\n                        domMessages.forEach((message) => {\n                            messages.push(message.textContent);\n                        });\n                        const domField = dom.querySelectorAll(\n                            `.field-${field ? field : 'body'} label`\n                        );\n                        if (messages.length === 0) {\n                            // Maybe CMS message from error.html?\n                            const errorDescription = dom.querySelector('form fieldset .description');\n                            if (errorDescription) {\n                                messages.push(errorDescription.textContent);\n                            }\n                        }\n                        if (messages.length > 0 && this.CMS) {\n                            this.CMS.API.Toolbar.hideLoader();\n                            this.CMS.API.Messages.open({\n                                message: (domField.length > 0 ? domField[0].textContent : '') + messages.join(', '),\n                                error: true,\n                                delay: -1,\n                            });\n                        }\n                        return;  // No databridge to evaluate\n                    }\n                    if (this.CMS) {\n                        // Success:\n                        // Remove an error message from a previous save attempt\n                        this.CMS.API.Messages.close();\n                        // Show messages if any\n                        const settings = this.getSettings(el);\n                        if (settings.messages_url) {\n                            fetch(settings.messages_url)\n                                .then(response => response.json())\n                                .then(messages => {\n                                    let error = \"success\", message_text = \"\";\n                                    for (let message of messages.messages) {\n                                        if (message.level_tag === \"error\") {\n                                            error = \"error\";\n                                        }\n                                        message_text += `<p>${message.message}</p>`;\n                                    }\n                                    if (message_text.length > 0) {\n                                        this.CMS.API.Messages.open({\n                                            message: message_text,\n                                            error: error === \"error\",\n                                        });\n                                    }\n                                });\n                        }\n\n                    }\n                    const script = dom.querySelector('script#data-bridge');\n                    el.dataset.changed = 'false';\n                    if (script && script.textContent.length > 2) {\n                        this.CMS.API.Helpers.dataBridge = JSON.parse(script.textContent);\n                    } else {\n                        const regex1 = /^\\s*Window\\.CMS\\.API\\.Helpers\\.dataBridge\\s=\\s(.*?);$/gmu.exec(body);\n                        const regex2 = /^\\s*Window\\.CMS\\.API\\.Helpers\\.dataBridge\\.structure\\s=\\s(.*?);$/gmu.exec(body);\n                        if (regex1 && regex2 && this.CMS) {\n                            this.CMS.API.Helpers.dataBridge = JSON.parse(regex1[1]);\n                            this.CMS.API.Helpers.dataBridge.structure = JSON.parse(regex2[1]);\n                        } else {\n                            // No databridge found: reload\n                            this.CMS.API.Helpers.reloadBrowser('REFRESH_PAGE');\n                            return;\n                        }\n                    }\n                    if (this.CMS.settings.version < \"4\") {\n                        /* Reflect dirty flag in django CMS < 4 */\n                        try {\n                            /* For some reason, in v3 this fails if the structure board is not open */\n                            this.CMS.API.StructureBoard.handleEditPlugin(this.CMS.API.Helpers.dataBridge);\n                        } catch (e) {\n                            console.error(e);\n                        }\n                        this._loadToolbar();\n                    } else {\n                        this.CMS.API.StructureBoard.handleEditPlugin(this.CMS.API.Helpers.dataBridge);\n                    }\n                })\n                .catch(error => {\n                    el.dataset.changed = 'true';\n                    if (this.CMS) {\n                        this.CMS.API.Toolbar.hideLoader();\n                        this.CMS.API.Messages.open({\n                            message: error.message,\n                            error: true,\n                            delay: -1,\n                        });\n                    }\n                    window.console.error(error.message);\n                    window.console.log(error.stack);\n                });\n        }\n    }\n\n    // CMS Editor: addPluginForm\n    // Get form for a new child plugin\n    addPluginForm (plugin_type, iframe, el , onLoad, onSave) {\n        const settings = this.getSettings(el);\n        const data = {\n            placeholder_id: settings.placeholder_id,\n            plugin_type: plugin_type,\n            plugin_parent: settings.plugin_id,\n            plugin_language: settings.plugin_language,\n            plugin_position: settings.plugin_position + 1,\n            cms_path: window.parent.location.pathname,\n            cms_history: 0,\n        };\n        const url = `${settings.add_plugin_url}?${new URLSearchParams(data).toString()}`;\n        return this.loadForm(url, iframe, el, onLoad, onSave);\n    }\n\n    // CMS Editor: addPluginForm\n    // Get form for a new child plugin\n    editPluginForm (plugin_id, iframe, el, onLoad, onSave) {\n        let url = el.dataset.cmsEditUrl || window.location.href;\n        url = url.replace(/\\/edit-plugin\\/\\d+/, '/edit-plugin/' + plugin_id);\n        const data = {\n            '_popup': 1,\n            cms_path: window.parent.location.pathname,\n            cms_history: 0,\n        };\n        url = `${url}?${new URLSearchParams(data).toString()}`;\n        return this.loadForm(url, iframe, el, onLoad, onSave);\n    }\n\n    loadForm (url, iframe, el, onLoad, onSave) {\n        iframe.addEventListener('load', () => {\n            const form = iframe.contentDocument;\n            const heading = form.querySelector('#content h1');\n            const submitrow = form.querySelector('.submit-row');\n\n            // Remove submit button and heading\n            if (submitrow) {\n                submitrow.style.display = 'none';\n            }\n            if (heading) {\n                heading.style.display = 'none';\n            }\n\n            //\n            let saveSuccess = !!form.querySelector('div.messagelist div.success');\n            if (!saveSuccess) {\n                saveSuccess =\n                    !!form.querySelector('.dashboard #content-main') &&\n                    !form.querySelector('.messagelist .error');\n            }\n            if (saveSuccess) {\n                // Mark document and child as changed\n                el.dataset.changed = 'true';\n                // Hook into the django CMS dataBridge to get the details of the newly created or saved\n                // plugin. For new plugins we need their id to get the content.\n                if (!this.CMS.API.Helpers.dataBridge) {\n                    // The dataBridge sets a timer, so typically it will not yet be present\n                    setTimeout(() => {\n                        // Needed to update StructureBoard\n                        if (onSave) {\n                            onSave(el, form, this.CMS.API.Helpers.dataBridge);\n                        }\n                    }, 100);\n                } else {\n                    // Needed to update StructureBoard\n                    if (onSave) {\n                        onSave(el, form, this.CMS.API.Helpers.dataBridge);\n                    }\n                }\n                //  Do callback\n            } else if (onLoad) {\n                onLoad(el, form, heading, submitrow);\n            }\n            // Editor-specific dialog setup goes into the callback\n        });\n        iframe.setAttribute('src', url);\n\n    }\n\n    // CMS Editor: requestPluginMarkup\n    // Get HTML markup for a child plugin\n    requestPluginMarkup (plugin_id, el) {\n        const settings = this.getSettings(el);\n        const data = {\n            plugin: plugin_id,\n            token: settings.action_token,\n        };\n\n        const url = `${settings.render_plugin_url}?${new URLSearchParams(data).toString()}`;\n\n        return fetch(url, {method: 'GET'})\n            .then(response => {\n                if (response.status === 200) {\n                    return response.text();\n                }\n                else if (response.status === 204)\n                {\n                    return null;\n                }\n            });\n     }\n\n    // CMS Editor: resetInlineEditors\n    _resetInlineEditors () {\n        this.destroyAll();\n        this.initAll();\n    }\n\n    // CMS Editor: loadToolbar\n    // Load the toolbar after saving for update\n    _loadToolbar () {\n        const $ = this.CMS.$;\n        this.CMS.API.StructureBoard._loadToolbar()\n            .done((newToolbar) => {\n                this.CMS.API.Toolbar._refreshMarkup($(newToolbar).find('.cms-toolbar'));\n            })\n            .fail(() => this.CMS.API.Helpers.reloadBrowser());\n    }\n\n    _highlightTextplugin (pluginId) {\n        const HIGHLIGHT_TIMEOUT = 800;\n\n        if (this.CMS) {\n            const $ = this.CMS.$;\n            const draggable = $('.cms-draggable-' + pluginId);\n            const doc = $(document);\n            const currentExpandmode = doc.data('expandmode');\n\n\n            // expand necessary parents\n            doc.data('expandmode', false);\n            draggable\n                .parents('.cms-draggable')\n                .find('> .cms-dragitem-collapsable:not(\".cms-dragitem-expanded\") > .cms-dragitem-text')\n                .each((i, el) => {\n                    $(el).triggerHandler(this.CMS.Plugin.click);\n                });\n            if (draggable.length > 0) {  // Expanded elements available\n                setTimeout(function () {\n                    doc.data('expandmode', currentExpandmode);\n                });\n                setTimeout( () => {\n                    this.CMS.Plugin._highlightPluginStructure(draggable.find('.cms-dragitem:first'),\n                        {successTimeout: 200, delay: 2000, seeThrough: true});\n                }, HIGHLIGHT_TIMEOUT);\n            }\n        }\n    }\n\n    // Wrap wrapper around nodes\n    // Just pass a collection of nodes, and a wrapper element\n    _wrapAll (nodes, wrapper) {\n        // Cache the current parent and previous sibling of the first node.\n        const parent = nodes[0].parentNode;\n        const previousSibling = nodes[0].previousSibling;\n\n        // Place each node in wrapper.\n        //  - If nodes is an array, we must increment the index we grab from\n        //    after each loop.\n        //  - If nodes is a NodeList, each node is automatically removed from\n        //    the NodeList when it is removed from its parent with appendChild.\n        for (let i = 0; nodes.length - i; wrapper.firstChild === nodes[0] && i++) {\n            wrapper.appendChild(nodes[i]);\n        }\n\n        // Place the wrapper just after the cached previousSibling,\n        // or if that is null, just before the first child.\n        const nextSibling = previousSibling ? previousSibling.nextSibling : parent.firstChild;\n        parent.insertBefore(wrapper, nextSibling);\n\n        return wrapper;\n    }\n}\n\n\n// Create global editor object\nwindow.CMS_Editor = new CMSEditor();\n\n"], "names": [], "sourceRoot": ""}