cmsWebpackJsonp([0],{0:function(e,t,n){var i,r;!function(t,n){"object"==typeof e&&"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,function(o,s){function a(e){var t="length"in e&&e.length,n=le.type(e);return"function"!==n&&!le.isWindow(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e))}function c(e,t,n){if(le.isFunction(t))return le.grep(e,function(e,i){return!!t.call(e,i,e)!==n});if(t.nodeType)return le.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(ve.test(t))return le.filter(t,e,n);t=le.filter(t,e)}return le.grep(e,function(e){return le.inArray(e,t)>=0!==n})}function l(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}function u(e){var t=Te[e]={};return le.each(e.match(Se)||[],function(e,n){t[n]=!0}),t}function h(){be.addEventListener?(be.removeEventListener("DOMContentLoaded",d,!1),o.removeEventListener("load",d,!1)):(be.detachEvent("onreadystatechange",d),o.detachEvent("onload",d))}function d(){(be.addEventListener||"load"===event.type||"complete"===be.readyState)&&(h(),le.ready())}function f(e,t,n){if(void 0===n&&1===e.nodeType){var i="data-"+t.replace(Ae,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(i))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:Oe.test(n)?le.parseJSON(n):n)}catch(e){}le.data(e,t,n)}else n=void 0}return n}function p(e){var t;for(t in e)if(("data"!==t||!le.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function g(e,t,n,i){if(le.acceptData(e)){var r,o,s=le.expando,a=e.nodeType,c=a?le.cache:e,l=a?e[s]:e[s]&&s;if(l&&c[l]&&(i||c[l].data)||void 0!==n||"string"!=typeof t)return l||(l=a?e[s]=Q.pop()||le.guid++:s),c[l]||(c[l]=a?{}:{toJSON:le.noop}),("object"==typeof t||"function"==typeof t)&&(i?c[l]=le.extend(c[l],t):c[l].data=le.extend(c[l].data,t)),o=c[l],i||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[le.camelCase(t)]=n),"string"==typeof t?null==(r=o[t])&&(r=o[le.camelCase(t)]):r=o,r}}function m(e,t,n){if(le.acceptData(e)){var i,r,o=e.nodeType,s=o?le.cache:e,a=o?e[le.expando]:le.expando;if(s[a]){if(t&&(i=n?s[a]:s[a].data)){le.isArray(t)?t=t.concat(le.map(t,le.camelCase)):t in i?t=[t]:(t=le.camelCase(t),t=t in i?[t]:t.split(" ")),r=t.length;for(;r--;)delete i[t[r]];if(n?!p(i):!le.isEmptyObject(i))return}(n||(delete s[a].data,p(s[a])))&&(o?le.cleanData([e],!0):ae.deleteExpando||s!=s.window?delete s[a]:s[a]=null)}}}function v(){return!0}function y(){return!1}function b(){try{return be.activeElement}catch(e){}}function x(e){var t=qe.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}function w(e,t){var n,i,r=0,o=typeof e.getElementsByTagName!==Ne?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!==Ne?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(i=n[r]);r++)!t||le.nodeName(i,t)?o.push(i):le.merge(o,w(i,t));return void 0===t||t&&le.nodeName(e,t)?le.merge([e],o):o}function C(e){He.test(e.type)&&(e.defaultChecked=e.checked)}function S(e,t){return le.nodeName(e,"table")&&le.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function T(e){return e.type=(null!==le.find.attr(e,"type"))+"/"+e.type,e}function E(e){var t=Je.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function k(e,t){for(var n,i=0;null!=(n=e[i]);i++)le._data(n,"globalEval",!t||le._data(t[i],"globalEval"))}function N(e,t){if(1===t.nodeType&&le.hasData(e)){var n,i,r,o=le._data(e),s=le._data(t,o),a=o.events;if(a){delete s.handle,s.events={};for(n in a)for(i=0,r=a[n].length;r>i;i++)le.event.add(t,n,a[n][i])}s.data&&(s.data=le.extend({},s.data))}}function O(e,t){var n,i,r;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!ae.noCloneEvent&&t[le.expando]){r=le._data(t);for(i in r.events)le.removeEvent(t,i,r.handle);t.removeAttribute(le.expando)}"script"===n&&t.text!==e.text?(T(t).text=e.text,E(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),ae.html5Clone&&e.innerHTML&&!le.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&He.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}}function A(e,t){var n,i=le(t.createElement(e)).appendTo(t.body),r=o.getDefaultComputedStyle&&(n=o.getDefaultComputedStyle(i[0]))?n.display:le.css(i[0],"display");return i.detach(),r}function D(e){var t=be,n=it[e];return n||(n=A(e,t),"none"!==n&&n||(nt=(nt||le("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=(nt[0].contentWindow||nt[0].contentDocument).document,t.write(),t.close(),n=A(e,t),nt.detach()),it[e]=n),n}function L(e,t){return{get:function(){var n=e();if(null!=n)return n?void delete this.get:(this.get=t).apply(this,arguments)}}}function j(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),i=t,r=mt.length;r--;)if((t=mt[r]+n)in e)return t;return i}function P(e,t){for(var n,i,r,o=[],s=0,a=e.length;a>s;s++)i=e[s],i.style&&(o[s]=le._data(i,"olddisplay"),n=i.style.display,t?(o[s]||"none"!==n||(i.style.display=""),""===i.style.display&&je(i)&&(o[s]=le._data(i,"olddisplay",D(i.nodeName)))):(r=je(i),(n&&"none"!==n||!r)&&le._data(i,"olddisplay",r?n:le.css(i,"display"))));for(s=0;a>s;s++)i=e[s],i.style&&(t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[s]||"":"none"));return e}function H(e,t,n){var i=dt.exec(t);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):t}function _(e,t,n,i,r){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,s=0;4>o;o+=2)"margin"===n&&(s+=le.css(e,n+Le[o],!0,r)),i?("content"===n&&(s-=le.css(e,"padding"+Le[o],!0,r)),"margin"!==n&&(s-=le.css(e,"border"+Le[o]+"Width",!0,r))):(s+=le.css(e,"padding"+Le[o],!0,r),"padding"!==n&&(s+=le.css(e,"border"+Le[o]+"Width",!0,r)));return s}function M(e,t,n){var i=!0,r="width"===t?e.offsetWidth:e.offsetHeight,o=rt(e),s=ae.boxSizing&&"border-box"===le.css(e,"boxSizing",!1,o);if(0>=r||null==r){if(r=ot(e,t,o),(0>r||null==r)&&(r=e.style[t]),at.test(r))return r;i=s&&(ae.boxSizingReliable()||r===e.style[t]),r=parseFloat(r)||0}return r+_(e,t,n||(s?"border":"content"),i,o)+"px"}function R(e,t,n,i,r){return new R.prototype.init(e,t,n,i,r)}function I(){return setTimeout(function(){vt=void 0}),vt=le.now()}function F(e,t){var n,i={height:e},r=0;for(t=t?1:0;4>r;r+=2-t)n=Le[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function q(e,t,n){for(var i,r=(St[t]||[]).concat(St["*"]),o=0,s=r.length;s>o;o++)if(i=r[o].call(n,t,e))return i}function W(e,t,n){var i,r,o,s,a,c,l,u=this,h={},d=e.style,f=e.nodeType&&je(e),p=le._data(e,"fxshow");n.queue||(a=le._queueHooks(e,"fx"),null==a.unqueued&&(a.unqueued=0,c=a.empty.fire,a.empty.fire=function(){a.unqueued||c()}),a.unqueued++,u.always(function(){u.always(function(){a.unqueued--,le.queue(e,"fx").length||a.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[d.overflow,d.overflowX,d.overflowY],l=le.css(e,"display"),"inline"===("none"===l?le._data(e,"olddisplay")||D(e.nodeName):l)&&"none"===le.css(e,"float")&&(ae.inlineBlockNeedsLayout&&"inline"!==D(e.nodeName)?d.zoom=1:d.display="inline-block")),n.overflow&&(d.overflow="hidden",ae.shrinkWrapBlocks()||u.always(function(){d.overflow=n.overflow[0],d.overflowX=n.overflow[1],d.overflowY=n.overflow[2]}));for(i in t)if(r=t[i],bt.exec(r)){if(delete t[i],o=o||"toggle"===r,r===(f?"hide":"show")){if("show"!==r||!p||void 0===p[i])continue;f=!0}h[i]=p&&p[i]||le.style(e,i)}else l=void 0;if(le.isEmptyObject(h))"inline"===("none"===l?D(e.nodeName):l)&&(d.display=l);else{p?"hidden"in p&&(f=p.hidden):p=le._data(e,"fxshow",{}),o&&(p.hidden=!f),f?le(e).show():u.done(function(){le(e).hide()}),u.done(function(){var t;le._removeData(e,"fxshow");for(t in h)le.style(e,t,h[t])});for(i in h)s=q(f?p[i]:0,i,u),i in p||(p[i]=s.start,f&&(s.end=s.start,s.start="width"===i||"height"===i?1:0))}}function B(e,t){var n,i,r,o,s;for(n in e)if(i=le.camelCase(n),r=t[i],o=e[n],le.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(s=le.cssHooks[i])&&"expand"in s){o=s.expand(o),delete e[i];for(n in o)n in e||(e[n]=o[n],t[n]=r)}else t[i]=r}function z(e,t,n){var i,r,o=0,s=Ct.length,a=le.Deferred().always(function(){delete c.elem}),c=function(){if(r)return!1;for(var t=vt||I(),n=Math.max(0,l.startTime+l.duration-t),i=n/l.duration||0,o=1-i,s=0,c=l.tweens.length;c>s;s++)l.tweens[s].run(o);return a.notifyWith(e,[l,o,n]),1>o&&c?n:(a.resolveWith(e,[l]),!1)},l=a.promise({elem:e,props:le.extend({},t),opts:le.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:vt||I(),duration:n.duration,tweens:[],createTween:function(t,n){var i=le.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(i),i},stop:function(t){var n=0,i=t?l.tweens.length:0;if(r)return this;for(r=!0;i>n;n++)l.tweens[n].run(1);return t?a.resolveWith(e,[l,t]):a.rejectWith(e,[l,t]),this}}),u=l.props;for(B(u,l.opts.specialEasing);s>o;o++)if(i=Ct[o].call(l,e,u,l.opts))return i;return le.map(u,q,l),le.isFunction(l.opts.start)&&l.opts.start.call(e,l),le.fx.timer(le.extend(c,{elem:e,anim:l,queue:l.opts.queue})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}function U(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(Se)||[];if(le.isFunction(n))for(;i=o[r++];)"+"===i.charAt(0)?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function $(e,t,n,i){function r(a){var c;return o[a]=!0,le.each(e[a]||[],function(e,a){var l=a(t,n,i);return"string"!=typeof l||s||o[l]?s?!(c=l):void 0:(t.dataTypes.unshift(l),r(l),!1)}),c}var o={},s=e===Gt;return r(t.dataTypes[0])||!o["*"]&&r("*")}function V(e,t){var n,i,r=le.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((r[i]?e:n||(n={}))[i]=t[i]);return n&&le.extend(!0,e,n),e}function G(e,t,n){for(var i,r,o,s,a=e.contents,c=e.dataTypes;"*"===c[0];)c.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(s in a)if(a[s]&&a[s].test(r)){c.unshift(s);break}if(c[0]in n)o=c[0];else{for(s in n){if(!c[0]||e.converters[s+" "+c[0]]){o=s;break}i||(i=s)}o=o||i}return o?(o!==c[0]&&c.unshift(o),n[o]):void 0}function X(e,t,n,i){var r,o,s,a,c,l={},u=e.dataTypes.slice();if(u[1])for(s in e.converters)l[s.toLowerCase()]=e.converters[s];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!c&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),c=o,o=u.shift())if("*"===o)o=c;else if("*"!==c&&c!==o){if(!(s=l[c+" "+o]||l["* "+o]))for(r in l)if(a=r.split(" "),a[1]===o&&(s=l[c+" "+a[0]]||l["* "+a[0]])){!0===s?s=l[r]:!0!==l[r]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+c+" to "+o}}}return{state:"success",data:t}}function K(e,t,n,i){var r;if(le.isArray(t))le.each(t,function(t,r){n||Yt.test(e)?i(e,r):K(e+"["+("object"==typeof r?t:"")+"]",r,n,i)});else if(n||"object"!==le.type(t))i(e,t);else for(r in t)K(e+"["+r+"]",t[r],n,i)}function Y(){try{return new o.XMLHttpRequest}catch(e){}}function J(){try{return new o.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}function Z(e){return le.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}var Q=[],ee=Q.slice,te=Q.concat,ne=Q.push,ie=Q.indexOf,re={},oe=re.toString,se=re.hasOwnProperty,ae={},ce="1.11.3",le=function(e,t){return new le.fn.init(e,t)},ue=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,he=/^-ms-/,de=/-([\da-z])/gi,fe=function(e,t){return t.toUpperCase()};le.fn=le.prototype={jquery:ce,constructor:le,selector:"",length:0,toArray:function(){return ee.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:ee.call(this)},pushStack:function(e){var t=le.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return le.each(this,e,t)},map:function(e){return this.pushStack(le.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(ee.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:ne,sort:Q.sort,splice:Q.splice},le.extend=le.fn.extend=function(){var e,t,n,i,r,o,s=arguments[0]||{},a=1,c=arguments.length,l=!1;for("boolean"==typeof s&&(l=s,s=arguments[a]||{},a++),"object"==typeof s||le.isFunction(s)||(s={}),a===c&&(s=this,a--);c>a;a++)if(null!=(r=arguments[a]))for(i in r)e=s[i],n=r[i],s!==n&&(l&&n&&(le.isPlainObject(n)||(t=le.isArray(n)))?(t?(t=!1,o=e&&le.isArray(e)?e:[]):o=e&&le.isPlainObject(e)?e:{},s[i]=le.extend(l,o,n)):void 0!==n&&(s[i]=n));return s},le.extend({expando:"jQuery"+(ce+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===le.type(e)},isArray:Array.isArray||function(e){return"array"===le.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!le.isArray(e)&&e-parseFloat(e)+1>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==le.type(e)||e.nodeType||le.isWindow(e))return!1;try{if(e.constructor&&!se.call(e,"constructor")&&!se.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(ae.ownLast)for(t in e)return se.call(e,t);for(t in e);return void 0===t||se.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?re[oe.call(e)]||"object":typeof e},globalEval:function(e){e&&le.trim(e)&&(o.execScript||function(e){o.eval.call(o,e)})(e)},camelCase:function(e){return e.replace(he,"ms-").replace(de,fe)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var i=0,r=e.length,o=a(e);if(n){if(o)for(;r>i&&!1!==t.apply(e[i],n);i++);else for(i in e)if(!1===t.apply(e[i],n))break}else if(o)for(;r>i&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},trim:function(e){return null==e?"":(e+"").replace(ue,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(a(Object(e))?le.merge(n,"string"==typeof e?[e]:e):ne.call(n,e)),n},inArray:function(e,t,n){var i;if(t){if(ie)return ie.call(t,e,n);for(i=t.length,n=n?0>n?Math.max(0,i+n):n:0;i>n;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;n>i;)e[r++]=t[i++];if(n!==n)for(;void 0!==t[i];)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,s=!n;o>r;r++)!t(e[r],r)!==s&&i.push(e[r]);return i},map:function(e,t,n){var i,r=0,o=e.length,s=a(e),c=[];if(s)for(;o>r;r++)null!=(i=t(e[r],r,n))&&c.push(i);else for(r in e)null!=(i=t(e[r],r,n))&&c.push(i);return te.apply([],c)},guid:1,proxy:function(e,t){var n,i,r;return"string"==typeof t&&(r=e[t],t=e,e=r),le.isFunction(e)?(n=ee.call(arguments,2),i=function(){return e.apply(t||this,n.concat(ee.call(arguments)))},i.guid=e.guid=e.guid||le.guid++,i):void 0},now:function(){return+new Date},support:ae}),le.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){re["[object "+t+"]"]=t.toLowerCase()});var pe=function(e){function t(e,t,n,i){var r,o,s,a,l,h,d,f,p,g;if((t?t.ownerDocument||t:I)!==D&&A(t),t=t||D,n=n||[],a=t.nodeType,"string"!=typeof e||!e||1!==a&&9!==a&&11!==a)return n;if(!i&&j){if(11!==a&&(r=me.exec(e)))if(s=r[1]){if(9===a){if(!(o=t.getElementById(s))||!o.parentNode)return n;if(o.id===s)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(s))&&M(t,o)&&o.id===s)return n.push(o),n}else{if(r[2])return Y.apply(n,t.getElementsByTagName(e)),n;if((s=r[3])&&b.getElementsByClassName)return Y.apply(n,t.getElementsByClassName(s)),n}if(b.qsa&&(!P||!P.test(e))){if(f=d=R,p=t,g=1!==a&&e,1===a&&"object"!==t.nodeName.toLowerCase()){for(h=S(e),(d=t.getAttribute("id"))?f=d.replace(ye,"\\$&"):t.setAttribute("id",f),f="[id='"+f+"'] ",l=h.length;l--;)h[l]=f+u(h[l]);p=ve.test(e)&&c(t.parentNode)||t,g=h.join(",")}if(g)try{return Y.apply(n,p.querySelectorAll(g)),n}catch(e){}finally{d||t.removeAttribute("id")}}}return E(e.replace(se,"$1"),t,n,i)}function n(){function e(n,i){return t.push(n+" ")>x.cacheLength&&delete e[t.shift()],e[n+" "]=i}var t=[];return e}function i(e){return e[R]=!0,e}function r(e){var t=D.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),i=e.length;i--;)x.attrHandle[n[i]]=t}function s(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||$)-(~e.sourceIndex||$);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function a(e){return i(function(t){return t=+t,i(function(n,i){for(var r,o=e([],n.length,t),s=o.length;s--;)n[r=o[s]]&&(n[r]=!(i[r]=n[r]))})})}function c(e){return e&&void 0!==e.getElementsByTagName&&e}function l(){}function u(e){for(var t=0,n=e.length,i="";n>t;t++)i+=e[t].value;return i}function h(e,t,n){var i=t.dir,r=n&&"parentNode"===i,o=q++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||r)return e(t,n,o)}:function(t,n,s){var a,c,l=[F,o];if(s){for(;t=t[i];)if((1===t.nodeType||r)&&e(t,n,s))return!0}else for(;t=t[i];)if(1===t.nodeType||r){if(c=t[R]||(t[R]={}),(a=c[i])&&a[0]===F&&a[1]===o)return l[2]=a[2];if(c[i]=l,l[2]=e(t,n,s))return!0}}}function d(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function f(e,n,i){for(var r=0,o=n.length;o>r;r++)t(e,n[r],i);return i}function p(e,t,n,i,r){for(var o,s=[],a=0,c=e.length,l=null!=t;c>a;a++)(o=e[a])&&(!n||n(o,i,r))&&(s.push(o),l&&t.push(a));return s}function g(e,t,n,r,o,s){return r&&!r[R]&&(r=g(r)),o&&!o[R]&&(o=g(o,s)),i(function(i,s,a,c){var l,u,h,d=[],g=[],m=s.length,v=i||f(t||"*",a.nodeType?[a]:a,[]),y=!e||!i&&t?v:p(v,d,e,a,c),b=n?o||(i?e:m||r)?[]:s:y;if(n&&n(y,b,a,c),r)for(l=p(b,g),r(l,[],a,c),u=l.length;u--;)(h=l[u])&&(b[g[u]]=!(y[g[u]]=h));if(i){if(o||e){if(o){for(l=[],u=b.length;u--;)(h=b[u])&&l.push(y[u]=h);o(null,b=[],l,c)}for(u=b.length;u--;)(h=b[u])&&(l=o?Z(i,h):d[u])>-1&&(i[l]=!(s[l]=h))}}else b=p(b===s?b.splice(m,b.length):b),o?o(null,s,b,c):Y.apply(s,b)})}function m(e){for(var t,n,i,r=e.length,o=x.relative[e[0].type],s=o||x.relative[" "],a=o?1:0,c=h(function(e){return e===t},s,!0),l=h(function(e){return Z(t,e)>-1},s,!0),f=[function(e,n,i){var r=!o&&(i||n!==k)||((t=n).nodeType?c(e,n,i):l(e,n,i));return t=null,r}];r>a;a++)if(n=x.relative[e[a].type])f=[h(d(f),n)];else{if(n=x.filter[e[a].type].apply(null,e[a].matches),n[R]){for(i=++a;r>i&&!x.relative[e[i].type];i++);return g(a>1&&d(f),a>1&&u(e.slice(0,a-1).concat({value:" "===e[a-2].type?"*":""})).replace(se,"$1"),n,i>a&&m(e.slice(a,i)),r>i&&m(e=e.slice(i)),r>i&&u(e))}f.push(n)}return d(f)}function v(e,n){var r=n.length>0,o=e.length>0,s=function(i,s,a,c,l){var u,h,d,f=0,g="0",m=i&&[],v=[],y=k,b=i||o&&x.find.TAG("*",l),w=F+=null==y?1:Math.random()||.1,C=b.length;for(l&&(k=s!==D&&s);g!==C&&null!=(u=b[g]);g++){if(o&&u){for(h=0;d=e[h++];)if(d(u,s,a)){c.push(u);break}l&&(F=w)}r&&((u=!d&&u)&&f--,i&&m.push(u))}if(f+=g,r&&g!==f){for(h=0;d=n[h++];)d(m,v,s,a);if(i){if(f>0)for(;g--;)m[g]||v[g]||(v[g]=X.call(c));v=p(v)}Y.apply(c,v),l&&!i&&v.length>0&&f+n.length>1&&t.uniqueSort(c)}return l&&(F=w,k=y),m};return r?i(s):s}var y,b,x,w,C,S,T,E,k,N,O,A,D,L,j,P,H,_,M,R="sizzle"+1*new Date,I=e.document,F=0,q=0,W=n(),B=n(),z=n(),U=function(e,t){return e===t&&(O=!0),0},$=1<<31,V={}.hasOwnProperty,G=[],X=G.pop,K=G.push,Y=G.push,J=G.slice,Z=function(e,t){for(var n=0,i=e.length;i>n;n++)if(e[n]===t)return n;return-1},Q="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ee="[\\x20\\t\\r\\n\\f]",te="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ne=te.replace("w","w#"),ie="\\["+ee+"*("+te+")(?:"+ee+"*([*^$|!~]?=)"+ee+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+ne+"))|)"+ee+"*\\]",re=":("+te+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ie+")*)|.*)\\)|)",oe=new RegExp(ee+"+","g"),se=new RegExp("^"+ee+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ee+"+$","g"),ae=new RegExp("^"+ee+"*,"+ee+"*"),ce=new RegExp("^"+ee+"*([>+~]|"+ee+")"+ee+"*"),le=new RegExp("="+ee+"*([^\\]'\"]*?)"+ee+"*\\]","g"),ue=new RegExp(re),he=new RegExp("^"+ne+"$"),de={ID:new RegExp("^#("+te+")"),CLASS:new RegExp("^\\.("+te+")"),TAG:new RegExp("^("+te.replace("w","w*")+")"),ATTR:new RegExp("^"+ie),PSEUDO:new RegExp("^"+re),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ee+"*(even|odd|(([+-]|)(\\d*)n|)"+ee+"*(?:([+-]|)"+ee+"*(\\d+)|))"+ee+"*\\)|)","i"),bool:new RegExp("^(?:"+Q+")$","i"),needsContext:new RegExp("^"+ee+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ee+"*((?:-\\d)?\\d*)"+ee+"*\\)|)(?=[^-]|$)","i")},fe=/^(?:input|select|textarea|button)$/i,pe=/^h\d$/i,ge=/^[^{]+\{\s*\[native \w/,me=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ve=/[+~]/,ye=/'|\\/g,be=new RegExp("\\\\([\\da-f]{1,6}"+ee+"?|("+ee+")|.)","ig"),xe=function(e,t,n){var i="0x"+t-65536;return i!==i||n?t:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},we=function(){A()};try{Y.apply(G=J.call(I.childNodes),I.childNodes),G[I.childNodes.length].nodeType}catch(e){Y={apply:G.length?function(e,t){K.apply(e,J.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}b=t.support={},C=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},A=t.setDocument=function(e){var t,n,i=e?e.ownerDocument||e:I;return i!==D&&9===i.nodeType&&i.documentElement?(D=i,L=i.documentElement,n=i.defaultView,n&&n!==n.top&&(n.addEventListener?n.addEventListener("unload",we,!1):n.attachEvent&&n.attachEvent("onunload",we)),j=!C(i),b.attributes=r(function(e){return e.className="i",!e.getAttribute("className")}),b.getElementsByTagName=r(function(e){return e.appendChild(i.createComment("")),!e.getElementsByTagName("*").length}),b.getElementsByClassName=ge.test(i.getElementsByClassName),b.getById=r(function(e){return L.appendChild(e).id=R,!i.getElementsByName||!i.getElementsByName(R).length}),b.getById?(x.find.ID=function(e,t){if(void 0!==t.getElementById&&j){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},x.filter.ID=function(e){var t=e.replace(be,xe);return function(e){return e.getAttribute("id")===t}}):(delete x.find.ID,x.filter.ID=function(e){var t=e.replace(be,xe);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),x.find.TAG=b.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):b.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},x.find.CLASS=b.getElementsByClassName&&function(e,t){return j?t.getElementsByClassName(e):void 0},H=[],P=[],(b.qsa=ge.test(i.querySelectorAll))&&(r(function(e){L.appendChild(e).innerHTML="<a id='"+R+"'></a><select id='"+R+"-\f]' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&P.push("[*^$]="+ee+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||P.push("\\["+ee+"*(?:value|"+Q+")"),e.querySelectorAll("[id~="+R+"-]").length||P.push("~="),e.querySelectorAll(":checked").length||P.push(":checked"),e.querySelectorAll("a#"+R+"+*").length||P.push(".#.+[+~]")}),r(function(e){var t=i.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&P.push("name"+ee+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||P.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),P.push(",.*:")})),(b.matchesSelector=ge.test(_=L.matches||L.webkitMatchesSelector||L.mozMatchesSelector||L.oMatchesSelector||L.msMatchesSelector))&&r(function(e){b.disconnectedMatch=_.call(e,"div"),_.call(e,"[s!='']:x"),H.push("!=",re)}),P=P.length&&new RegExp(P.join("|")),H=H.length&&new RegExp(H.join("|")),t=ge.test(L.compareDocumentPosition),M=t||ge.test(L.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},U=t?function(e,t){if(e===t)return O=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!b.sortDetached&&t.compareDocumentPosition(e)===n?e===i||e.ownerDocument===I&&M(I,e)?-1:t===i||t.ownerDocument===I&&M(I,t)?1:N?Z(N,e)-Z(N,t):0:4&n?-1:1)}:function(e,t){if(e===t)return O=!0,0;var n,r=0,o=e.parentNode,a=t.parentNode,c=[e],l=[t];if(!o||!a)return e===i?-1:t===i?1:o?-1:a?1:N?Z(N,e)-Z(N,t):0;if(o===a)return s(e,t);for(n=e;n=n.parentNode;)c.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);for(;c[r]===l[r];)r++;return r?s(c[r],l[r]):c[r]===I?-1:l[r]===I?1:0},i):D},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==D&&A(e),n=n.replace(le,"='$1']"),!(!b.matchesSelector||!j||H&&H.test(n)||P&&P.test(n)))try{var i=_.call(e,n);if(i||b.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(e){}return t(n,D,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==D&&A(e),M(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==D&&A(e);var n=x.attrHandle[t.toLowerCase()],i=n&&V.call(x.attrHandle,t.toLowerCase())?n(e,t,!j):void 0;return void 0!==i?i:b.attributes||!j?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],i=0,r=0;if(O=!b.detectDuplicates,N=!b.sortStable&&e.slice(0),e.sort(U),O){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return N=null,e},w=t.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=w(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=w(t);return n},x=t.selectors={cacheLength:50,createPseudo:i,match:de,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(be,xe),e[3]=(e[3]||e[4]||e[5]||"").replace(be,xe),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return de.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&ue.test(n)&&(t=S(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(be,xe).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=W[e+" "];return t||(t=new RegExp("(^|"+ee+")"+e+"("+ee+"|$)"))&&W(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,n,i){return function(r){var o=t.attr(r,e);return null==o?"!="===n:!n||(o+="","="===n?o===i:"!="===n?o!==i:"^="===n?i&&0===o.indexOf(i):"*="===n?i&&o.indexOf(i)>-1:"$="===n?i&&o.slice(-i.length)===i:"~="===n?(" "+o.replace(oe," ")+" ").indexOf(i)>-1:"|="===n&&(o===i||o.slice(0,i.length+1)===i+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,c){var l,u,h,d,f,p,g=o!==s?"nextSibling":"previousSibling",m=t.parentNode,v=a&&t.nodeName.toLowerCase(),y=!c&&!a;if(m){if(o){for(;g;){for(h=t;h=h[g];)if(a?h.nodeName.toLowerCase()===v:1===h.nodeType)return!1;p=g="only"===e&&!p&&"nextSibling"}return!0}if(p=[s?m.firstChild:m.lastChild],s&&y){for(u=m[R]||(m[R]={}),l=u[e]||[],f=l[0]===F&&l[1],d=l[0]===F&&l[2],h=f&&m.childNodes[f];h=++f&&h&&h[g]||(d=f=0)||p.pop();)if(1===h.nodeType&&++d&&h===t){u[e]=[F,f,d];break}}else if(y&&(l=(t[R]||(t[R]={}))[e])&&l[0]===F)d=l[1];else for(;(h=++f&&h&&h[g]||(d=f=0)||p.pop())&&((a?h.nodeName.toLowerCase()!==v:1!==h.nodeType)||!++d||(y&&((h[R]||(h[R]={}))[e]=[F,d]),h!==t)););return(d-=r)===i||d%i==0&&d/i>=0}}},PSEUDO:function(e,n){var r,o=x.pseudos[e]||x.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[R]?o(n):o.length>1?(r=[e,e,"",n],x.setFilters.hasOwnProperty(e.toLowerCase())?i(function(e,t){for(var i,r=o(e,n),s=r.length;s--;)i=Z(e,r[s]),e[i]=!(t[i]=r[s])}):function(e){return o(e,0,r)}):o}},pseudos:{not:i(function(e){var t=[],n=[],r=T(e.replace(se,"$1"));return r[R]?i(function(e,t,n,i){for(var o,s=r(e,null,i,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:i(function(e){return function(n){return t(e,n).length>0}}),contains:i(function(e){return e=e.replace(be,xe),function(t){return(t.textContent||t.innerText||w(t)).indexOf(e)>-1}}),lang:i(function(e){return he.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(be,xe).toLowerCase(),function(t){var n;do{if(n=j?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===L},focus:function(e){return e===D.activeElement&&(!D.hasFocus||D.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!x.pseudos.empty(e)},header:function(e){return pe.test(e.nodeName)},input:function(e){return fe.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:a(function(){return[0]}),last:a(function(e,t){return[t-1]}),eq:a(function(e,t,n){return[0>n?n+t:n]}),even:a(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:a(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:a(function(e,t,n){for(var i=0>n?n+t:n;--i>=0;)e.push(i);return e}),gt:a(function(e,t,n){for(var i=0>n?n+t:n;++i<t;)e.push(i);return e})}},x.pseudos.nth=x.pseudos.eq;for(y in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[y]=function(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}(y);for(y in{submit:!0,reset:!0})x.pseudos[y]=function(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}(y);return l.prototype=x.filters=x.pseudos,x.setFilters=new l,S=t.tokenize=function(e,n){var i,r,o,s,a,c,l,u=B[e+" "];if(u)return n?0:u.slice(0);for(a=e,c=[],l=x.preFilter;a;){(!i||(r=ae.exec(a)))&&(r&&(a=a.slice(r[0].length)||a),c.push(o=[])),i=!1,(r=ce.exec(a))&&(i=r.shift(),o.push({value:i,type:r[0].replace(se," ")}),a=a.slice(i.length));for(s in x.filter)!(r=de[s].exec(a))||l[s]&&!(r=l[s](r))||(i=r.shift(),o.push({value:i,type:s,matches:r}),a=a.slice(i.length));if(!i)break}return n?a.length:a?t.error(e):B(e,c).slice(0)},T=t.compile=function(e,t){var n,i=[],r=[],o=z[e+" "];if(!o){for(t||(t=S(e)),n=t.length;n--;)o=m(t[n]),o[R]?i.push(o):r.push(o);o=z(e,v(r,i)),o.selector=e}return o},E=t.select=function(e,t,n,i){var r,o,s,a,l,h="function"==typeof e&&e,d=!i&&S(e=h.selector||e);if(n=n||[],1===d.length){if(o=d[0]=d[0].slice(0),o.length>2&&"ID"===(s=o[0]).type&&b.getById&&9===t.nodeType&&j&&x.relative[o[1].type]){if(!(t=(x.find.ID(s.matches[0].replace(be,xe),t)||[])[0]))return n;h&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=de.needsContext.test(e)?0:o.length;r--&&(s=o[r],!x.relative[a=s.type]);)if((l=x.find[a])&&(i=l(s.matches[0].replace(be,xe),ve.test(o[0].type)&&c(t.parentNode)||t))){if(o.splice(r,1),!(e=i.length&&u(o)))return Y.apply(n,i),n;break}}return(h||T(e,d))(i,t,!j,n,ve.test(e)&&c(t.parentNode)||t),n},b.sortStable=R.split("").sort(U).join("")===R,b.detectDuplicates=!!O,A(),b.sortDetached=r(function(e){return 1&e.compareDocumentPosition(D.createElement("div"))}),r(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),b.attributes&&r(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),r(function(e){return null==e.getAttribute("disabled")})||o(Q,function(e,t,n){var i;return n?void 0:!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),t}(o);le.find=pe,le.expr=pe.selectors,le.expr[":"]=le.expr.pseudos,le.unique=pe.uniqueSort,le.text=pe.getText,le.isXMLDoc=pe.isXML,le.contains=pe.contains;var ge=le.expr.match.needsContext,me=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,ve=/^.[^:#\[\.,]*$/;le.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?le.find.matchesSelector(i,e)?[i]:[]:le.find.matches(e,le.grep(t,function(e){return 1===e.nodeType}))},le.fn.extend({find:function(e){var t,n=[],i=this,r=i.length;if("string"!=typeof e)return this.pushStack(le(e).filter(function(){for(t=0;r>t;t++)if(le.contains(i[t],this))return!0}));for(t=0;r>t;t++)le.find(e,i[t],n);return n=this.pushStack(r>1?le.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(c(this,e||[],!1))},not:function(e){return this.pushStack(c(this,e||[],!0))},is:function(e){return!!c(this,"string"==typeof e&&ge.test(e)?le(e):e||[],!1).length}});var ye,be=o.document,xe=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(le.fn.init=function(e,t){var n,i;if(!e)return this;if("string"==typeof e){if(!(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:xe.exec(e))||!n[1]&&t)return!t||t.jquery?(t||ye).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof le?t[0]:t,le.merge(this,le.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:be,!0)),me.test(n[1])&&le.isPlainObject(t))for(n in t)le.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if((i=be.getElementById(n[2]))&&i.parentNode){if(i.id!==n[2])return ye.find(e);this.length=1,this[0]=i}return this.context=be,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):le.isFunction(e)?void 0!==ye.ready?ye.ready(e):e(le):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),le.makeArray(e,this))}).prototype=le.fn,ye=le(be);var we=/^(?:parents|prev(?:Until|All))/,Ce={children:!0,contents:!0,next:!0,prev:!0};le.extend({dir:function(e,t,n){for(var i=[],r=e[t];r&&9!==r.nodeType&&(void 0===n||1!==r.nodeType||!le(r).is(n));)1===r.nodeType&&i.push(r),r=r[t];return i},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),le.fn.extend({has:function(e){var t,n=le(e,this),i=n.length;return this.filter(function(){for(t=0;i>t;t++)if(le.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,i=0,r=this.length,o=[],s=ge.test(e)||"string"!=typeof e?le(e,t||this.context):0;r>i;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&le.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?le.unique(o):o)},index:function(e){return e?"string"==typeof e?le.inArray(this[0],le(e)):le.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(le.unique(le.merge(this.get(),le(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),le.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return le.dir(e,"parentNode")},parentsUntil:function(e,t,n){return le.dir(e,"parentNode",n)},next:function(e){return l(e,"nextSibling")},prev:function(e){return l(e,"previousSibling")},nextAll:function(e){return le.dir(e,"nextSibling")},prevAll:function(e){return le.dir(e,"previousSibling")},nextUntil:function(e,t,n){return le.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return le.dir(e,"previousSibling",n)},siblings:function(e){return le.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return le.sibling(e.firstChild)},contents:function(e){return le.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:le.merge([],e.childNodes)}},function(e,t){le.fn[e]=function(n,i){var r=le.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=le.filter(i,r)),this.length>1&&(Ce[e]||(r=le.unique(r)),we.test(e)&&(r=r.reverse())),this.pushStack(r)}});var Se=/\S+/g,Te={};le.Callbacks=function(e){e="string"==typeof e?Te[e]||u(e):le.extend({},e);var t,n,i,r,o,s,a=[],c=!e.once&&[],l=function(u){for(n=e.memory&&u,i=!0,o=s||0,s=0,r=a.length,t=!0;a&&r>o;o++)if(!1===a[o].apply(u[0],u[1])&&e.stopOnFalse){n=!1;break}t=!1,a&&(c?c.length&&l(c.shift()):n?a=[]:h.disable())},h={add:function(){if(a){var i=a.length;!function t(n){le.each(n,function(n,i){var r=le.type(i);"function"===r?e.unique&&h.has(i)||a.push(i):i&&i.length&&"string"!==r&&t(i)})}(arguments),t?r=a.length:n&&(s=i,l(n))}return this},remove:function(){return a&&le.each(arguments,function(e,n){for(var i;(i=le.inArray(n,a,i))>-1;)a.splice(i,1),t&&(r>=i&&r--,o>=i&&o--)}),this},has:function(e){return e?le.inArray(e,a)>-1:!(!a||!a.length)},empty:function(){return a=[],r=0,this},disable:function(){return a=c=n=void 0,this},disabled:function(){return!a},lock:function(){return c=void 0,n||h.disable(),this},locked:function(){return!c},fireWith:function(e,n){return!a||i&&!c||(n=n||[],n=[e,n.slice?n.slice():n],t?c.push(n):l(n)),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!i}};return h},le.extend({Deferred:function(e){var t=[["resolve","done",le.Callbacks("once memory"),"resolved"],["reject","fail",le.Callbacks("once memory"),"rejected"],["notify","progress",le.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return r.done(arguments).fail(arguments),this},then:function(){var e=arguments;return le.Deferred(function(n){le.each(t,function(t,o){var s=le.isFunction(e[t])&&e[t];r[o[1]](function(){var e=s&&s.apply(this,arguments);e&&le.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o[0]+"With"](this===i?n.promise():this,s?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?le.extend(e,i):i}},r={};return i.pipe=i.then,le.each(t,function(e,o){var s=o[2],a=o[3];i[o[1]]=s.add,a&&s.add(function(){n=a},t[1^e][2].disable,t[2][2].lock),r[o[0]]=function(){return r[o[0]+"With"](this===r?i:this,arguments),this},r[o[0]+"With"]=s.fireWith}),i.promise(r),e&&e.call(r,r),r},when:function(e){var t,n,i,r=0,o=ee.call(arguments),s=o.length,a=1!==s||e&&le.isFunction(e.promise)?s:0,c=1===a?e:le.Deferred(),l=function(e,n,i){return function(r){n[e]=this,i[e]=arguments.length>1?ee.call(arguments):r,i===t?c.notifyWith(n,i):--a||c.resolveWith(n,i)}};if(s>1)for(t=new Array(s),n=new Array(s),i=new Array(s);s>r;r++)o[r]&&le.isFunction(o[r].promise)?o[r].promise().done(l(r,i,o)).fail(c.reject).progress(l(r,n,t)):--a;return a||c.resolveWith(i,o),c.promise()}});var Ee;le.fn.ready=function(e){return le.ready.promise().done(e),this},le.extend({isReady:!1,readyWait:1,holdReady:function(e){e?le.readyWait++:le.ready(!0)},ready:function(e){if(!0===e?!--le.readyWait:!le.isReady){if(!be.body)return setTimeout(le.ready);le.isReady=!0,!0!==e&&--le.readyWait>0||(Ee.resolveWith(be,[le]),le.fn.triggerHandler&&(le(be).triggerHandler("ready"),le(be).off("ready")))}}}),le.ready.promise=function(e){if(!Ee)if(Ee=le.Deferred(),"complete"===be.readyState)setTimeout(le.ready);else if(be.addEventListener)be.addEventListener("DOMContentLoaded",d,!1),o.addEventListener("load",d,!1);else{be.attachEvent("onreadystatechange",d),o.attachEvent("onload",d);var t=!1;try{t=null==o.frameElement&&be.documentElement}catch(e){}t&&t.doScroll&&function e(){if(!le.isReady){try{t.doScroll("left")}catch(t){return setTimeout(e,50)}h(),le.ready()}}()}return Ee.promise(e)};var ke,Ne="undefined";for(ke in le(ae))break;ae.ownLast="0"!==ke,ae.inlineBlockNeedsLayout=!1,le(function(){var e,t,n,i;(n=be.getElementsByTagName("body")[0])&&n.style&&(t=be.createElement("div"),i=be.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==Ne&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",ae.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(i))}),function(){var e=be.createElement("div");if(null==ae.deleteExpando){ae.deleteExpando=!0;try{delete e.test}catch(e){ae.deleteExpando=!1}}e=null}(),le.acceptData=function(e){var t=le.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)};var Oe=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ae=/([A-Z])/g;le.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return!!(e=e.nodeType?le.cache[e[le.expando]]:e[le.expando])&&!p(e)},data:function(e,t,n){return g(e,t,n)},removeData:function(e,t){return m(e,t)},_data:function(e,t,n){return g(e,t,n,!0)},_removeData:function(e,t){return m(e,t,!0)}}),le.fn.extend({data:function(e,t){var n,i,r,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(r=le.data(o),1===o.nodeType&&!le._data(o,"parsedAttrs"))){for(n=s.length;n--;)s[n]&&(i=s[n].name,0===i.indexOf("data-")&&(i=le.camelCase(i.slice(5)),f(o,i,r[i])));le._data(o,"parsedAttrs",!0)}return r}return"object"==typeof e?this.each(function(){le.data(this,e)}):arguments.length>1?this.each(function(){le.data(this,e,t)}):o?f(o,e,le.data(o,e)):void 0},removeData:function(e){return this.each(function(){le.removeData(this,e)})}}),le.extend({queue:function(e,t,n){var i;return e?(t=(t||"fx")+"queue",i=le._data(e,t),n&&(!i||le.isArray(n)?i=le._data(e,t,le.makeArray(n)):i.push(n)),i||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=le.queue(e,t),i=n.length,r=n.shift(),o=le._queueHooks(e,t),s=function(){le.dequeue(e,t)};"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,s,o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return le._data(e,n)||le._data(e,n,{empty:le.Callbacks("once memory").add(function(){le._removeData(e,t+"queue"),le._removeData(e,n)})})}}),le.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?le.queue(this[0],e):void 0===t?this:this.each(function(){var n=le.queue(this,e,t);le._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&le.dequeue(this,e)})},dequeue:function(e){return this.each(function(){le.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=le.Deferred(),o=this,s=this.length,a=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=le._data(o[s],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),r.promise(t)}});var De=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Le=["Top","Right","Bottom","Left"],je=function(e,t){return e=t||e,"none"===le.css(e,"display")||!le.contains(e.ownerDocument,e)},Pe=le.access=function(e,t,n,i,r,o,s){var a=0,c=e.length,l=null==n;if("object"===le.type(n)){r=!0;for(a in n)le.access(e,t,a,n[a],!0,o,s)}else if(void 0!==i&&(r=!0,le.isFunction(i)||(s=!0),l&&(s?(t.call(e,i),t=null):(l=t,t=function(e,t,n){return l.call(le(e),n)})),t))for(;c>a;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return r?e:l?t.call(e):c?t(e[0],n):o},He=/^(?:checkbox|radio)$/i;!function(){var e=be.createElement("input"),t=be.createElement("div"),n=be.createDocumentFragment();if(t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",ae.leadingWhitespace=3===t.firstChild.nodeType,ae.tbody=!t.getElementsByTagName("tbody").length,ae.htmlSerialize=!!t.getElementsByTagName("link").length,ae.html5Clone="<:nav></:nav>"!==be.createElement("nav").cloneNode(!0).outerHTML,e.type="checkbox",e.checked=!0,n.appendChild(e),ae.appendChecked=e.checked,t.innerHTML="<textarea>x</textarea>",ae.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,n.appendChild(t),t.innerHTML="<input type='radio' checked='checked' name='t'/>",ae.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,ae.noCloneEvent=!0,t.attachEvent&&(t.attachEvent("onclick",function(){ae.noCloneEvent=!1}),t.cloneNode(!0).click()),null==ae.deleteExpando){ae.deleteExpando=!0;try{delete t.test}catch(e){ae.deleteExpando=!1}}}(),function(){var e,t,n=be.createElement("div");for(e in{submit:!0,change:!0,focusin:!0})t="on"+e,(ae[e+"Bubbles"]=t in o)||(n.setAttribute(t,"t"),ae[e+"Bubbles"]=!1===n.attributes[t].expando);n=null}();var _e=/^(?:input|select|textarea)$/i,Me=/^key/,Re=/^(?:mouse|pointer|contextmenu)|click/,Ie=/^(?:focusinfocus|focusoutblur)$/,Fe=/^([^.]*)(?:\.(.+)|)$/;le.event={global:{},add:function(e,t,n,i,r){var o,s,a,c,l,u,h,d,f,p,g,m=le._data(e);if(m){for(n.handler&&(c=n,n=c.handler,r=c.selector),n.guid||(n.guid=le.guid++),(s=m.events)||(s=m.events={}),(u=m.handle)||(u=m.handle=function(e){return typeof le===Ne||e&&le.event.triggered===e.type?void 0:le.event.dispatch.apply(u.elem,arguments)},u.elem=e),t=(t||"").match(Se)||[""],a=t.length;a--;)o=Fe.exec(t[a])||[],f=g=o[1],p=(o[2]||"").split(".").sort(),f&&(l=le.event.special[f]||{},f=(r?l.delegateType:l.bindType)||f,l=le.event.special[f]||{},h=le.extend({type:f,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&le.expr.match.needsContext.test(r),namespace:p.join(".")},c),(d=s[f])||(d=s[f]=[],d.delegateCount=0,l.setup&&!1!==l.setup.call(e,i,p,u)||(e.addEventListener?e.addEventListener(f,u,!1):e.attachEvent&&e.attachEvent("on"+f,u))),l.add&&(l.add.call(e,h),h.handler.guid||(h.handler.guid=n.guid)),r?d.splice(d.delegateCount++,0,h):d.push(h),le.event.global[f]=!0);e=null}},remove:function(e,t,n,i,r){var o,s,a,c,l,u,h,d,f,p,g,m=le.hasData(e)&&le._data(e);if(m&&(u=m.events)){for(t=(t||"").match(Se)||[""],l=t.length;l--;)if(a=Fe.exec(t[l])||[],f=g=a[1],p=(a[2]||"").split(".").sort(),f){for(h=le.event.special[f]||{},f=(i?h.delegateType:h.bindType)||f,d=u[f]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),c=o=d.length;o--;)s=d[o],!r&&g!==s.origType||n&&n.guid!==s.guid||a&&!a.test(s.namespace)||i&&i!==s.selector&&("**"!==i||!s.selector)||(d.splice(o,1),s.selector&&d.delegateCount--,h.remove&&h.remove.call(e,s));c&&!d.length&&(h.teardown&&!1!==h.teardown.call(e,p,m.handle)||le.removeEvent(e,f,m.handle),delete u[f])}else for(f in u)le.event.remove(e,f+t[l],n,i,!0);le.isEmptyObject(u)&&(delete m.handle,le._removeData(e,"events"))}},trigger:function(e,t,n,i){var r,s,a,c,l,u,h,d=[n||be],f=se.call(e,"type")?e.type:e,p=se.call(e,"namespace")?e.namespace.split("."):[];if(a=u=n=n||be,3!==n.nodeType&&8!==n.nodeType&&!Ie.test(f+le.event.triggered)&&(f.indexOf(".")>=0&&(p=f.split("."),f=p.shift(),p.sort()),s=f.indexOf(":")<0&&"on"+f,e=e[le.expando]?e:new le.Event(f,"object"==typeof e&&e),e.isTrigger=i?2:3,e.namespace=p.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:le.makeArray(t,[e]),l=le.event.special[f]||{},i||!l.trigger||!1!==l.trigger.apply(n,t))){if(!i&&!l.noBubble&&!le.isWindow(n)){for(c=l.delegateType||f,Ie.test(c+f)||(a=a.parentNode);a;a=a.parentNode)d.push(a),u=a;u===(n.ownerDocument||be)&&d.push(u.defaultView||u.parentWindow||o)}for(h=0;(a=d[h++])&&!e.isPropagationStopped();)e.type=h>1?c:l.bindType||f,r=(le._data(a,"events")||{})[e.type]&&le._data(a,"handle"),r&&r.apply(a,t),(r=s&&a[s])&&r.apply&&le.acceptData(a)&&(e.result=r.apply(a,t),!1===e.result&&e.preventDefault());if(e.type=f,!i&&!e.isDefaultPrevented()&&(!l._default||!1===l._default.apply(d.pop(),t))&&le.acceptData(n)&&s&&n[f]&&!le.isWindow(n)){u=n[s],u&&(n[s]=null),le.event.triggered=f;try{n[f]()}catch(e){}le.event.triggered=void 0,u&&(n[s]=u)}return e.result}},dispatch:function(e){e=le.event.fix(e);var t,n,i,r,o,s=[],a=ee.call(arguments),c=(le._data(this,"events")||{})[e.type]||[],l=le.event.special[e.type]||{};if(a[0]=e,e.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,e)){for(s=le.event.handlers.call(this,e,c),t=0;(r=s[t++])&&!e.isPropagationStopped();)for(e.currentTarget=r.elem,o=0;(i=r.handlers[o++])&&!e.isImmediatePropagationStopped();)(!e.namespace_re||e.namespace_re.test(i.namespace))&&(e.handleObj=i,e.data=i.data,void 0!==(n=((le.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(e.result=n)&&(e.preventDefault(),e.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,r,o,s=[],a=t.delegateCount,c=e.target;if(a&&c.nodeType&&(!e.button||"click"!==e.type))for(;c!=this;c=c.parentNode||this)if(1===c.nodeType&&(!0!==c.disabled||"click"!==e.type)){for(r=[],o=0;a>o;o++)i=t[o],n=i.selector+" ",void 0===r[n]&&(r[n]=i.needsContext?le(n,this).index(c)>=0:le.find(n,this,null,[c]).length),r[n]&&r.push(i);r.length&&s.push({elem:c,handlers:r})}return a<t.length&&s.push({elem:this,handlers:t.slice(a)}),s},fix:function(e){if(e[le.expando])return e;var t,n,i,r=e.type,o=e,s=this.fixHooks[r];for(s||(this.fixHooks[r]=s=Re.test(r)?this.mouseHooks:Me.test(r)?this.keyHooks:{}),i=s.props?this.props.concat(s.props):this.props,e=new le.Event(o),t=i.length;t--;)n=i[t],e[n]=o[n];return e.target||(e.target=o.srcElement||be),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,s.filter?s.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,i,r,o=t.button,s=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=e.target.ownerDocument||be,r=i.documentElement,n=i.body,e.pageX=t.clientX+(r&&r.scrollLeft||n&&n.scrollLeft||0)-(r&&r.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||n&&n.scrollTop||0)-(r&&r.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&s&&(e.relatedTarget=s===e.target?t.toElement:s),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==b()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){return this===b()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return le.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(e){return le.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,i){var r=le.extend(new le.Event,n,{type:e,isSimulated:!0,originalEvent:{}});i?le.event.trigger(r,null,t):le.event.dispatch.call(t,r),r.isDefaultPrevented()&&n.preventDefault()}},le.removeEvent=be.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var i="on"+t;e.detachEvent&&(typeof e[i]===Ne&&(e[i]=null),e.detachEvent(i,n))},le.Event=function(e,t){return this instanceof le.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?v:y):this.type=e,t&&le.extend(this,t),this.timeStamp=e&&e.timeStamp||le.now(),void(this[le.expando]=!0)):new le.Event(e,t)},le.Event.prototype={isDefaultPrevented:y,isPropagationStopped:y,isImmediatePropagationStopped:y,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=v,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=v,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=v,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},le.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){le.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,r=e.relatedTarget,o=e.handleObj;return(!r||r!==i&&!le.contains(i,r))&&(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),ae.submitBubbles||(le.event.special.submit={setup:function(){return!le.nodeName(this,"form")&&void le.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=le.nodeName(t,"input")||le.nodeName(t,"button")?t.form:void 0;n&&!le._data(n,"submitBubbles")&&(le.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),le._data(n,"submitBubbles",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&le.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){return!le.nodeName(this,"form")&&void le.event.remove(this,"._submit")}}),ae.changeBubbles||(le.event.special.change={setup:function(){return _e.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(le.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),le.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),le.event.simulate("change",this,e,!0)})),!1):void le.event.add(this,"beforeactivate._change",function(e){var t=e.target;_e.test(t.nodeName)&&!le._data(t,"changeBubbles")&&(le.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||le.event.simulate("change",this.parentNode,e,!0)}),le._data(t,"changeBubbles",!0))})},handle:function(e){var t=e.target;return this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type?e.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return le.event.remove(this,"._change"),!_e.test(this.nodeName)}}),ae.focusinBubbles||le.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){le.event.simulate(t,e.target,le.event.fix(e),!0)};le.event.special[t]={setup:function(){var i=this.ownerDocument||this,r=le._data(i,t);r||i.addEventListener(e,n,!0),le._data(i,t,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=le._data(i,t)-1;r?le._data(i,t,r):(i.removeEventListener(e,n,!0),le._removeData(i,t))}}}),le.fn.extend({on:function(e,t,n,i,r){var o,s;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=void 0);for(o in e)this.on(o,t,n,e[o],r);return this}if(null==n&&null==i?(i=t,n=t=void 0):null==i&&("string"==typeof t?(i=n,n=void 0):(i=n,n=t,t=void 0)),!1===i)i=y;else if(!i)return this;return 1===r&&(s=i,i=function(e){return le().off(e),s.apply(this,arguments)},i.guid=s.guid||(s.guid=le.guid++)),this.each(function(){le.event.add(this,e,i,n,t)})},one:function(e,t,n,i){return this.on(e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,le(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return(!1===t||"function"==typeof t)&&(n=t,t=void 0),!1===n&&(n=y),this.each(function(){le.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){le.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?le.event.trigger(e,t,n,!0):void 0}});var qe="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",We=/ jQuery\d+="(?:null|\d+)"/g,Be=new RegExp("<(?:"+qe+")[\\s/>]","i"),ze=/^\s+/,Ue=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,$e=/<([\w:]+)/,Ve=/<tbody/i,Ge=/<|&#?\w+;/,Xe=/<(?:script|style|link)/i,Ke=/checked\s*(?:[^=]|=\s*.checked.)/i,Ye=/^$|\/(?:java|ecma)script/i,Je=/^true\/(.*)/,Ze=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Qe={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:ae.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},et=x(be),tt=et.appendChild(be.createElement("div"));Qe.optgroup=Qe.option,Qe.tbody=Qe.tfoot=Qe.colgroup=Qe.caption=Qe.thead,Qe.th=Qe.td,le.extend({clone:function(e,t,n){var i,r,o,s,a,c=le.contains(e.ownerDocument,e);if(ae.html5Clone||le.isXMLDoc(e)||!Be.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(tt.innerHTML=e.outerHTML,tt.removeChild(o=tt.firstChild)),!(ae.noCloneEvent&&ae.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||le.isXMLDoc(e)))for(i=w(o),a=w(e),s=0;null!=(r=a[s]);++s)i[s]&&O(r,i[s]);if(t)if(n)for(a=a||w(e),i=i||w(o),s=0;null!=(r=a[s]);s++)N(r,i[s]);else N(e,o);return i=w(o,"script"),i.length>0&&k(i,!c&&w(e,"script")),i=a=r=null,o},buildFragment:function(e,t,n,i){for(var r,o,s,a,c,l,u,h=e.length,d=x(t),f=[],p=0;h>p;p++)if((o=e[p])||0===o)if("object"===le.type(o))le.merge(f,o.nodeType?[o]:o);else if(Ge.test(o)){for(a=a||d.appendChild(t.createElement("div")),c=($e.exec(o)||["",""])[1].toLowerCase(),u=Qe[c]||Qe._default,a.innerHTML=u[1]+o.replace(Ue,"<$1></$2>")+u[2],r=u[0];r--;)a=a.lastChild;if(!ae.leadingWhitespace&&ze.test(o)&&f.push(t.createTextNode(ze.exec(o)[0])),!ae.tbody)for(o="table"!==c||Ve.test(o)?"<table>"!==u[1]||Ve.test(o)?0:a:a.firstChild,r=o&&o.childNodes.length;r--;)le.nodeName(l=o.childNodes[r],"tbody")&&!l.childNodes.length&&o.removeChild(l);for(le.merge(f,a.childNodes),a.textContent="";a.firstChild;)a.removeChild(a.firstChild);a=d.lastChild}else f.push(t.createTextNode(o));for(a&&d.removeChild(a),ae.appendChecked||le.grep(w(f,"input"),C),p=0;o=f[p++];)if((!i||-1===le.inArray(o,i))&&(s=le.contains(o.ownerDocument,o),a=w(d.appendChild(o),"script"),s&&k(a),n))for(r=0;o=a[r++];)Ye.test(o.type||"")&&n.push(o);return a=null,d},cleanData:function(e,t){for(var n,i,r,o,s=0,a=le.expando,c=le.cache,l=ae.deleteExpando,u=le.event.special;null!=(n=e[s]);s++)if((t||le.acceptData(n))&&(r=n[a],o=r&&c[r])){if(o.events)for(i in o.events)u[i]?le.event.remove(n,i):le.removeEvent(n,i,o.handle);c[r]&&(delete c[r],l?delete n[a]:typeof n.removeAttribute!==Ne?n.removeAttribute(a):n[a]=null,Q.push(r))}}}),le.fn.extend({text:function(e){return Pe(this,function(e){return void 0===e?le.text(this):this.empty().append((this[0]&&this[0].ownerDocument||be).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){S(this,e).appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=S(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var n,i=e?le.filter(e,this):this,r=0;null!=(n=i[r]);r++)t||1!==n.nodeType||le.cleanData(w(n)),n.parentNode&&(t&&le.contains(n.ownerDocument,n)&&k(w(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&le.cleanData(w(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&le.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return le.clone(this,e,t)})},html:function(e){return Pe(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(We,""):void 0;if(!("string"!=typeof e||Xe.test(e)||!ae.htmlSerialize&&Be.test(e)||!ae.leadingWhitespace&&ze.test(e)||Qe[($e.exec(e)||["",""])[1].toLowerCase()])){e=e.replace(Ue,"<$1></$2>");try{for(;i>n;n++)t=this[n]||{},1===t.nodeType&&(le.cleanData(w(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=arguments[0];return this.domManip(arguments,function(t){e=this.parentNode,le.cleanData(w(this)),e&&e.replaceChild(t,this)}),e&&(e.length||e.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t){e=te.apply([],e);var n,i,r,o,s,a,c=0,l=this.length,u=this,h=l-1,d=e[0],f=le.isFunction(d);if(f||l>1&&"string"==typeof d&&!ae.checkClone&&Ke.test(d))return this.each(function(n){var i=u.eq(n);f&&(e[0]=d.call(this,n,i.html())),i.domManip(e,t)});if(l&&(a=le.buildFragment(e,this[0].ownerDocument,!1,this),n=a.firstChild,1===a.childNodes.length&&(a=n),n)){for(o=le.map(w(a,"script"),T),r=o.length;l>c;c++)i=a,c!==h&&(i=le.clone(i,!0,!0),r&&le.merge(o,w(i,"script"))),t.call(this[c],i,c);if(r)for(s=o[o.length-1].ownerDocument,le.map(o,E),c=0;r>c;c++)i=o[c],Ye.test(i.type||"")&&!le._data(i,"globalEval")&&le.contains(s,i)&&(i.src?le._evalUrl&&le._evalUrl(i.src):le.globalEval((i.text||i.textContent||i.innerHTML||"").replace(Ze,"")));a=n=null}return this}}),le.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){le.fn[e]=function(e){for(var n,i=0,r=[],o=le(e),s=o.length-1;s>=i;i++)n=i===s?this:this.clone(!0),le(o[i])[t](n),ne.apply(r,n.get());return this.pushStack(r)}});var nt,it={};!function(){var e;ae.shrinkWrapBlocks=function(){if(null!=e)return e;e=!1;var t,n,i;return n=be.getElementsByTagName("body")[0],n&&n.style?(t=be.createElement("div"),i=be.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==Ne&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(be.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(i),e):void 0}}();var rt,ot,st=/^margin/,at=new RegExp("^("+De+")(?!px)[a-z%]+$","i"),ct=/^(top|right|bottom|left)$/;o.getComputedStyle?(rt=function(e){return e.ownerDocument.defaultView.opener?e.ownerDocument.defaultView.getComputedStyle(e,null):o.getComputedStyle(e,null)},ot=function(e,t,n){var i,r,o,s,a=e.style;return n=n||rt(e),s=n?n.getPropertyValue(t)||n[t]:void 0,n&&(""!==s||le.contains(e.ownerDocument,e)||(s=le.style(e,t)),at.test(s)&&st.test(t)&&(i=a.width,r=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=i,a.minWidth=r,a.maxWidth=o)),void 0===s?s:s+""}):be.documentElement.currentStyle&&(rt=function(e){return e.currentStyle},ot=function(e,t,n){var i,r,o,s,a=e.style;return n=n||rt(e),s=n?n[t]:void 0,null==s&&a&&a[t]&&(s=a[t]),at.test(s)&&!ct.test(t)&&(i=a.left,r=e.runtimeStyle,o=r&&r.left,o&&(r.left=e.currentStyle.left),a.left="fontSize"===t?"1em":s,s=a.pixelLeft+"px",a.left=i,o&&(r.left=o)),void 0===s?s:s+""||"auto"}),!function(){function e(){var e,t,n,i;(t=be.getElementsByTagName("body")[0])&&t.style&&(e=be.createElement("div"),n=be.createElement("div"),n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",t.appendChild(n).appendChild(e),e.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",r=s=!1,c=!0,o.getComputedStyle&&(r="1%"!==(o.getComputedStyle(e,null)||{}).top,s="4px"===(o.getComputedStyle(e,null)||{width:"4px"}).width,i=e.appendChild(be.createElement("div")),i.style.cssText=e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",i.style.marginRight=i.style.width="0",e.style.width="1px",c=!parseFloat((o.getComputedStyle(i,null)||{}).marginRight),e.removeChild(i)),e.innerHTML="<table><tr><td></td><td>t</td></tr></table>",i=e.getElementsByTagName("td"),i[0].style.cssText="margin:0;border:0;padding:0;display:none",a=0===i[0].offsetHeight,a&&(i[0].style.display="",i[1].style.display="none",a=0===i[0].offsetHeight),t.removeChild(n))}var t,n,i,r,s,a,c;t=be.createElement("div"),t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",i=t.getElementsByTagName("a")[0],(n=i&&i.style)&&(n.cssText="float:left;opacity:.5",ae.opacity="0.5"===n.opacity,ae.cssFloat=!!n.cssFloat,t.style.backgroundClip="content-box",t.cloneNode(!0).style.backgroundClip="",ae.clearCloneStyle="content-box"===t.style.backgroundClip,ae.boxSizing=""===n.boxSizing||""===n.MozBoxSizing||""===n.WebkitBoxSizing,le.extend(ae,{reliableHiddenOffsets:function(){return null==a&&e(),a},boxSizingReliable:function(){return null==s&&e(),s},pixelPosition:function(){return null==r&&e(),r},reliableMarginRight:function(){return null==c&&e(),c}}))}(),le.swap=function(e,t,n,i){var r,o,s={};for(o in t)s[o]=e.style[o],e.style[o]=t[o];r=n.apply(e,i||[]);for(o in t)e.style[o]=s[o];return r};var lt=/alpha\([^)]*\)/i,ut=/opacity\s*=\s*([^)]*)/,ht=/^(none|table(?!-c[ea]).+)/,dt=new RegExp("^("+De+")(.*)$","i"),ft=new RegExp("^([+-])=("+De+")","i"),pt={position:"absolute",visibility:"hidden",display:"block"},gt={letterSpacing:"0",fontWeight:"400"},mt=["Webkit","O","Moz","ms"];le.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=ot(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:ae.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,s,a=le.camelCase(t),c=e.style;if(t=le.cssProps[a]||(le.cssProps[a]=j(c,a)),s=le.cssHooks[t]||le.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(r=s.get(e,!1,i))?r:c[t];if(o=typeof n,"string"===o&&(r=ft.exec(n))&&(n=(r[1]+1)*r[2]+parseFloat(le.css(e,t)),o="number"),null!=n&&n===n&&("number"!==o||le.cssNumber[a]||(n+="px"),ae.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),!(s&&"set"in s&&void 0===(n=s.set(e,n,i)))))try{c[t]=n}catch(e){}}},css:function(e,t,n,i){var r,o,s,a=le.camelCase(t);return t=le.cssProps[a]||(le.cssProps[a]=j(e.style,a)),s=le.cssHooks[t]||le.cssHooks[a],s&&"get"in s&&(o=s.get(e,!0,n)),void 0===o&&(o=ot(e,t,i)),"normal"===o&&t in gt&&(o=gt[t]),""===n||n?(r=parseFloat(o),!0===n||le.isNumeric(r)?r||0:o):o}}),le.each(["height","width"],function(e,t){le.cssHooks[t]={get:function(e,n,i){return n?ht.test(le.css(e,"display"))&&0===e.offsetWidth?le.swap(e,pt,function(){return M(e,t,i)}):M(e,t,i):void 0},set:function(e,n,i){var r=i&&rt(e);return H(e,n,i?_(e,t,i,ae.boxSizing&&"border-box"===le.css(e,"boxSizing",!1,r),r):0)}}}),ae.opacity||(le.cssHooks.opacity={get:function(e,t){return ut.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,i=e.currentStyle,r=le.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=i&&i.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===le.trim(o.replace(lt,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||i&&!i.filter)||(n.filter=lt.test(o)?o.replace(lt,r):o+" "+r)}}),le.cssHooks.marginRight=L(ae.reliableMarginRight,function(e,t){return t?le.swap(e,{display:"inline-block"},ot,[e,"marginRight"]):void 0}),le.each({margin:"",padding:"",border:"Width"},function(e,t){le.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];4>i;i++)r[e+Le[i]+t]=o[i]||o[i-2]||o[0];return r}},st.test(e)||(le.cssHooks[e+t].set=H)}),le.fn.extend({css:function(e,t){return Pe(this,function(e,t,n){var i,r,o={},s=0;if(le.isArray(t)){for(i=rt(e),r=t.length;r>s;s++)o[t[s]]=le.css(e,t[s],!1,i);return o}return void 0!==n?le.style(e,t,n):le.css(e,t)},e,t,arguments.length>1)},show:function(){return P(this,!0)},hide:function(){return P(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){je(this)?le(this).show():le(this).hide()})}}),le.Tween=R,R.prototype={constructor:R,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||"swing",this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(le.cssNumber[n]?"":"px")},cur:function(){var e=R.propHooks[this.prop];return e&&e.get?e.get(this):R.propHooks._default.get(this)},run:function(e){var t,n=R.propHooks[this.prop];return this.options.duration?this.pos=t=le.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):R.propHooks._default.set(this),this}},R.prototype.init.prototype=R.prototype,R.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=le.css(e.elem,e.prop,""),t&&"auto"!==t?t:0):e.elem[e.prop]},set:function(e){le.fx.step[e.prop]?le.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[le.cssProps[e.prop]]||le.cssHooks[e.prop])?le.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},R.propHooks.scrollTop=R.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},le.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},le.fx=R.prototype.init,le.fx.step={};var vt,yt,bt=/^(?:toggle|show|hide)$/,xt=new RegExp("^(?:([+-])=|)("+De+")([a-z%]*)$","i"),wt=/queueHooks$/,Ct=[W],St={"*":[function(e,t){var n=this.createTween(e,t),i=n.cur(),r=xt.exec(t),o=r&&r[3]||(le.cssNumber[e]?"":"px"),s=(le.cssNumber[e]||"px"!==o&&+i)&&xt.exec(le.css(n.elem,e)),a=1,c=20;if(s&&s[3]!==o){o=o||s[3],r=r||[],s=+i||1;do{a=a||".5",s/=a,le.style(n.elem,e,s+o)}while(a!==(a=n.cur()/i)&&1!==a&&--c)}return r&&(s=n.start=+s||+i||0,n.unit=o,n.end=r[1]?s+(r[1]+1)*r[2]:+r[2]),n}]};le.Animation=le.extend(z,{tweener:function(e,t){le.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,i=0,r=e.length;r>i;i++)n=e[i],St[n]=St[n]||[],St[n].unshift(t)},prefilter:function(e,t){t?Ct.unshift(e):Ct.push(e)}}),le.speed=function(e,t,n){var i=e&&"object"==typeof e?le.extend({},e):{complete:n||!n&&t||le.isFunction(e)&&e,duration:e,easing:n&&t||t&&!le.isFunction(t)&&t};return i.duration=le.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in le.fx.speeds?le.fx.speeds[i.duration]:le.fx.speeds._default,(null==i.queue||!0===i.queue)&&(i.queue="fx"),i.old=i.complete,i.complete=function(){le.isFunction(i.old)&&i.old.call(this),i.queue&&le.dequeue(this,i.queue)},i},le.fn.extend({fadeTo:function(e,t,n,i){return this.filter(je).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=le.isEmptyObject(e),o=le.speed(t,n,i),s=function(){var t=z(this,le.extend({},e),o);(r||le._data(this,"finish"))&&t.stop(!0)};return s.finish=s,r||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=le.timers,s=le._data(this);if(r)s[r]&&s[r].stop&&i(s[r]);else for(r in s)s[r]&&s[r].stop&&wt.test(r)&&i(s[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));(t||!n)&&le.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=le._data(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=le.timers,s=i?i.length:0;for(n.finish=!0,le.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;s>t;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),le.each(["toggle","show","hide"],function(e,t){var n=le.fn[t];le.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(F(t,!0),e,i,r)}}),le.each({slideDown:F("show"),slideUp:F("hide"),slideToggle:F("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){le.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),le.timers=[],le.fx.tick=function(){var e,t=le.timers,n=0;for(vt=le.now();n<t.length;n++)(e=t[n])()||t[n]!==e||t.splice(n--,1);t.length||le.fx.stop(),vt=void 0},le.fx.timer=function(e){le.timers.push(e),e()?le.fx.start():le.timers.pop()},le.fx.interval=13,le.fx.start=function(){yt||(yt=setInterval(le.fx.tick,le.fx.interval))},le.fx.stop=function(){clearInterval(yt),yt=null},le.fx.speeds={slow:600,fast:200,_default:400},le.fn.delay=function(e,t){return e=le.fx?le.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var i=setTimeout(t,e);n.stop=function(){clearTimeout(i)}})},function(){var e,t,n,i,r;t=be.createElement("div"),t.setAttribute("className","t"),t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",i=t.getElementsByTagName("a")[0],n=be.createElement("select"),r=n.appendChild(be.createElement("option")),e=t.getElementsByTagName("input")[0],i.style.cssText="top:1px",ae.getSetAttribute="t"!==t.className,ae.style=/top/.test(i.getAttribute("style")),ae.hrefNormalized="/a"===i.getAttribute("href"),ae.checkOn=!!e.value,ae.optSelected=r.selected,ae.enctype=!!be.createElement("form").enctype,n.disabled=!0,ae.optDisabled=!r.disabled,e=be.createElement("input"),e.setAttribute("value",""),ae.input=""===e.getAttribute("value"),e.value="t",e.setAttribute("type","radio"),ae.radioValue="t"===e.value}();var Tt=/\r/g;le.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=le.isFunction(e),this.each(function(n){var r;1===this.nodeType&&(r=i?e.call(this,n,le(this).val()):e,null==r?r="":"number"==typeof r?r+="":le.isArray(r)&&(r=le.map(r,function(e){return null==e?"":e+""})),(t=le.valHooks[this.type]||le.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))})):r?(t=le.valHooks[r.type]||le.valHooks[r.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:(n=r.value,"string"==typeof n?n.replace(Tt,""):null==n?"":n)):void 0}}),le.extend({valHooks:{option:{get:function(e){var t=le.find.attr(e,"value");return null!=t?t:le.trim(le.text(e))}},select:{get:function(e){for(var t,n,i=e.options,r=e.selectedIndex,o="select-one"===e.type||0>r,s=o?null:[],a=o?r+1:i.length,c=0>r?a:o?r:0;a>c;c++)if(n=i[c],!(!n.selected&&c!==r||(ae.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&le.nodeName(n.parentNode,"optgroup"))){if(t=le(n).val(),o)return t;s.push(t)}return s},set:function(e,t){for(var n,i,r=e.options,o=le.makeArray(t),s=r.length;s--;)if(i=r[s],le.inArray(le.valHooks.option.get(i),o)>=0)try{i.selected=n=!0}catch(e){i.scrollHeight}else i.selected=!1;return n||(e.selectedIndex=-1),r}}}}),le.each(["radio","checkbox"],function(){le.valHooks[this]={set:function(e,t){return le.isArray(t)?e.checked=le.inArray(le(e).val(),t)>=0:void 0}},ae.checkOn||(le.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var Et,kt,Nt=le.expr.attrHandle,Ot=/^(?:checked|selected)$/i,At=ae.getSetAttribute,Dt=ae.input;le.fn.extend({attr:function(e,t){return Pe(this,le.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){le.removeAttr(this,e)})}}),le.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===Ne?le.prop(e,t,n):(1===o&&le.isXMLDoc(e)||(t=t.toLowerCase(),i=le.attrHooks[t]||(le.expr.match.bool.test(t)?kt:Et)),void 0===n?i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=le.find.attr(e,t),null==r?void 0:r):null!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):void le.removeAttr(e,t))},removeAttr:function(e,t){var n,i,r=0,o=t&&t.match(Se);if(o&&1===e.nodeType)for(;n=o[r++];)i=le.propFix[n]||n,le.expr.match.bool.test(n)?Dt&&At||!Ot.test(n)?e[i]=!1:e[le.camelCase("default-"+n)]=e[i]=!1:le.attr(e,n,""),e.removeAttribute(At?n:i)},attrHooks:{type:{set:function(e,t){if(!ae.radioValue&&"radio"===t&&le.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}}}),kt={set:function(e,t,n){return!1===t?le.removeAttr(e,n):Dt&&At||!Ot.test(n)?e.setAttribute(!At&&le.propFix[n]||n,n):e[le.camelCase("default-"+n)]=e[n]=!0,n}},le.each(le.expr.match.bool.source.match(/\w+/g),function(e,t){var n=Nt[t]||le.find.attr;Nt[t]=Dt&&At||!Ot.test(t)?function(e,t,i){var r,o;return i||(o=Nt[t],Nt[t]=r,r=null!=n(e,t,i)?t.toLowerCase():null,Nt[t]=o),r}:function(e,t,n){return n?void 0:e[le.camelCase("default-"+t)]?t.toLowerCase():null}}),Dt&&At||(le.attrHooks.value={set:function(e,t,n){return le.nodeName(e,"input")?void(e.defaultValue=t):Et&&Et.set(e,t,n)}}),At||(Et={set:function(e,t,n){var i=e.getAttributeNode(n);return i||e.setAttributeNode(i=e.ownerDocument.createAttribute(n)),i.value=t+="","value"===n||t===e.getAttribute(n)?t:void 0}},Nt.id=Nt.name=Nt.coords=function(e,t,n){var i;return n?void 0:(i=e.getAttributeNode(t))&&""!==i.value?i.value:null},le.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&n.specified?n.value:void 0},set:Et.set},le.attrHooks.contenteditable={set:function(e,t,n){Et.set(e,""!==t&&t,n)}},le.each(["width","height"],function(e,t){le.attrHooks[t]={set:function(e,n){return""===n?(e.setAttribute(t,"auto"),n):void 0}}})),ae.style||(le.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var Lt=/^(?:input|select|textarea|button|object)$/i,jt=/^(?:a|area)$/i;le.fn.extend({prop:function(e,t){return Pe(this,le.prop,e,t,arguments.length>1)},removeProp:function(e){return e=le.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(e){}})}}),le.extend({propFix:{for:"htmlFor",class:"className"},prop:function(e,t,n){var i,r,o,s=e.nodeType;if(e&&3!==s&&8!==s&&2!==s)return o=1!==s||!le.isXMLDoc(e),o&&(t=le.propFix[t]||t,r=le.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=le.find.attr(e,"tabindex");return t?parseInt(t,10):Lt.test(e.nodeName)||jt.test(e.nodeName)&&e.href?0:-1}}}}),ae.hrefNormalized||le.each(["href","src"],function(e,t){le.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),ae.optSelected||(le.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}}),le.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){le.propFix[this.toLowerCase()]=this}),ae.enctype||(le.propFix.enctype="encoding");var Pt=/[\t\r\n\f]/g;le.fn.extend({addClass:function(e){var t,n,i,r,o,s,a=0,c=this.length,l="string"==typeof e&&e;if(le.isFunction(e))return this.each(function(t){le(this).addClass(e.call(this,t,this.className))});if(l)for(t=(e||"").match(Se)||[];c>a;a++)if(n=this[a],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(Pt," "):" ")){for(o=0;r=t[o++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");s=le.trim(i),n.className!==s&&(n.className=s)}return this},removeClass:function(e){var t,n,i,r,o,s,a=0,c=this.length,l=0===arguments.length||"string"==typeof e&&e;if(le.isFunction(e))return this.each(function(t){le(this).removeClass(e.call(this,t,this.className))});if(l)for(t=(e||"").match(Se)||[];c>a;a++)if(n=this[a],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(Pt," "):"")){for(o=0;r=t[o++];)for(;i.indexOf(" "+r+" ")>=0;)i=i.replace(" "+r+" "," ");s=e?le.trim(i):"",n.className!==s&&(n.className=s)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):this.each(le.isFunction(e)?function(n){le(this).toggleClass(e.call(this,n,this.className,t),t)}:function(){if("string"===n)for(var t,i=0,r=le(this),o=e.match(Se)||[];t=o[i++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else(n===Ne||"boolean"===n)&&(this.className&&le._data(this,"__className__",this.className),this.className=this.className||!1===e?"":le._data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,i=this.length;i>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(Pt," ").indexOf(t)>=0)return!0;return!1}}),le.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){le.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),le.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var Ht=le.now(),_t=/\?/,Mt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;le.parseJSON=function(e){if(o.JSON&&o.JSON.parse)return o.JSON.parse(e+"");var t,n=null,i=le.trim(e+"");return i&&!le.trim(i.replace(Mt,function(e,i,r,o){return t&&i&&(n=0),0===n?e:(t=r||i,n+=!o-!r,"")}))?Function("return "+i)():le.error("Invalid JSON: "+e)},le.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{o.DOMParser?(n=new DOMParser,t=n.parseFromString(e,"text/xml")):(t=new ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(e))}catch(e){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||le.error("Invalid XML: "+e),t};var Rt,It,Ft=/#.*$/,qt=/([?&])_=[^&]*/,Wt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Bt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,zt=/^(?:GET|HEAD)$/,Ut=/^\/\//,$t=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Vt={},Gt={},Xt="*/".concat("*");try{It=location.href}catch(e){It=be.createElement("a"),It.href="",It=It.href}Rt=$t.exec(It.toLowerCase())||[],le.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:It,type:"GET",isLocal:Bt.test(Rt[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Xt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":le.parseJSON,"text xml":le.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?V(V(e,le.ajaxSettings),t):V(le.ajaxSettings,e)},ajaxPrefilter:U(Vt),ajaxTransport:U(Gt),ajax:function(e,t){function n(e,t,n,i){var r,u,v,y,x,C=t;2!==b&&(b=2,a&&clearTimeout(a),l=void 0,s=i||"",w.readyState=e>0?4:0,r=e>=200&&300>e||304===e,n&&(y=G(h,w,n)),y=X(h,y,w,r),r?(h.ifModified&&(x=w.getResponseHeader("Last-Modified"),x&&(le.lastModified[o]=x),(x=w.getResponseHeader("etag"))&&(le.etag[o]=x)),204===e||"HEAD"===h.type?C="nocontent":304===e?C="notmodified":(C=y.state,u=y.data,v=y.error,r=!v)):(v=C,(e||!C)&&(C="error",0>e&&(e=0))),w.status=e,w.statusText=(t||C)+"",r?p.resolveWith(d,[u,C,w]):p.rejectWith(d,[w,C,v]),w.statusCode(m),m=void 0,c&&f.trigger(r?"ajaxSuccess":"ajaxError",[w,h,r?u:v]),g.fireWith(d,[w,C]),c&&(f.trigger("ajaxComplete",[w,h]),--le.active||le.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var i,r,o,s,a,c,l,u,h=le.ajaxSetup({},t),d=h.context||h,f=h.context&&(d.nodeType||d.jquery)?le(d):le.event,p=le.Deferred(),g=le.Callbacks("once memory"),m=h.statusCode||{},v={},y={},b=0,x="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===b){if(!u)for(u={};t=Wt.exec(s);)u[t[1].toLowerCase()]=t[2];t=u[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===b?s:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return b||(e=y[n]=y[n]||e,v[e]=t),this},overrideMimeType:function(e){return b||(h.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>b)for(t in e)m[t]=[m[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||x;return l&&l.abort(t),n(0,t),this}};if(p.promise(w).complete=g.add,w.success=w.done,w.error=w.fail,h.url=((e||h.url||It)+"").replace(Ft,"").replace(Ut,Rt[1]+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=le.trim(h.dataType||"*").toLowerCase().match(Se)||[""],null==h.crossDomain&&(i=$t.exec(h.url.toLowerCase()),h.crossDomain=!(!i||i[1]===Rt[1]&&i[2]===Rt[2]&&(i[3]||("http:"===i[1]?"80":"443"))===(Rt[3]||("http:"===Rt[1]?"80":"443")))),h.data&&h.processData&&"string"!=typeof h.data&&(h.data=le.param(h.data,h.traditional)),$(Vt,h,t,w),2===b)return w;c=le.event&&h.global,c&&0==le.active++&&le.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!zt.test(h.type),o=h.url,h.hasContent||(h.data&&(o=h.url+=(_t.test(o)?"&":"?")+h.data,delete h.data),!1===h.cache&&(h.url=qt.test(o)?o.replace(qt,"$1_="+Ht++):o+(_t.test(o)?"&":"?")+"_="+Ht++)),h.ifModified&&(le.lastModified[o]&&w.setRequestHeader("If-Modified-Since",le.lastModified[o]),le.etag[o]&&w.setRequestHeader("If-None-Match",le.etag[o])),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&w.setRequestHeader("Content-Type",h.contentType),w.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Xt+"; q=0.01":""):h.accepts["*"]);for(r in h.headers)w.setRequestHeader(r,h.headers[r]);if(h.beforeSend&&(!1===h.beforeSend.call(d,w,h)||2===b))return w.abort();x="abort";for(r in{success:1,error:1,complete:1})w[r](h[r]);if(l=$(Gt,h,t,w)){w.readyState=1,c&&f.trigger("ajaxSend",[w,h]),h.async&&h.timeout>0&&(a=setTimeout(function(){w.abort("timeout")},h.timeout));try{b=1,l.send(v,n)}catch(e){if(!(2>b))throw e;n(-1,e)}}else n(-1,"No Transport");return w},getJSON:function(e,t,n){return le.get(e,t,n,"json")},getScript:function(e,t){return le.get(e,void 0,t,"script")}}),le.each(["get","post"],function(e,t){le[t]=function(e,n,i,r){return le.isFunction(n)&&(r=r||i,i=n,n=void 0),le.ajax({url:e,type:t,dataType:r,data:n,success:i})}}),le._evalUrl=function(e){return le.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},le.fn.extend({wrapAll:function(e){if(le.isFunction(e))return this.each(function(t){le(this).wrapAll(e.call(this,t))});if(this[0]){var t=le(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return this.each(le.isFunction(e)?function(t){le(this).wrapInner(e.call(this,t))}:function(){var t=le(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=le.isFunction(e);return this.each(function(n){le(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){le.nodeName(this,"body")||le(this).replaceWith(this.childNodes)}).end()}}),le.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0||!ae.reliableHiddenOffsets()&&"none"===(e.style&&e.style.display||le.css(e,"display"))},le.expr.filters.visible=function(e){return!le.expr.filters.hidden(e)};var Kt=/%20/g,Yt=/\[\]$/,Jt=/\r?\n/g,Zt=/^(?:submit|button|image|reset|file)$/i,Qt=/^(?:input|select|textarea|keygen)/i;le.param=function(e,t){var n,i=[],r=function(e,t){t=le.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=le.ajaxSettings&&le.ajaxSettings.traditional),le.isArray(e)||e.jquery&&!le.isPlainObject(e))le.each(e,function(){r(this.name,this.value)});else for(n in e)K(n,e[n],t,r);return i.join("&").replace(Kt,"+")},le.fn.extend({serialize:function(){return le.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=le.prop(this,"elements");return e?le.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!le(this).is(":disabled")&&Qt.test(this.nodeName)&&!Zt.test(e)&&(this.checked||!He.test(e))}).map(function(e,t){var n=le(this).val();return null==n?null:le.isArray(n)?le.map(n,function(e){return{name:t.name,value:e.replace(Jt,"\r\n")}}):{name:t.name,value:n.replace(Jt,"\r\n")}}).get()}}),le.ajaxSettings.xhr=void 0!==o.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&Y()||J()}:Y;var en=0,tn={},nn=le.ajaxSettings.xhr();o.attachEvent&&o.attachEvent("onunload",function(){for(var e in tn)tn[e](void 0,!0)}),ae.cors=!!nn&&"withCredentials"in nn,(nn=ae.ajax=!!nn)&&le.ajaxTransport(function(e){if(!e.crossDomain||ae.cors){var t;return{send:function(n,i){var r,o=e.xhr(),s=++en;if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)o[r]=e.xhrFields[r];e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(r in n)void 0!==n[r]&&o.setRequestHeader(r,n[r]+"");o.send(e.hasContent&&e.data||null),t=function(n,r){var a,c,l;if(t&&(r||4===o.readyState))if(delete tn[s],t=void 0,o.onreadystatechange=le.noop,r)4!==o.readyState&&o.abort();else{l={},a=o.status,"string"==typeof o.responseText&&(l.text=o.responseText);try{c=o.statusText}catch(e){c=""}a||!e.isLocal||e.crossDomain?1223===a&&(a=204):a=l.text?200:404}l&&i(a,c,l,o.getAllResponseHeaders())},e.async?4===o.readyState?setTimeout(t):o.onreadystatechange=tn[s]=t:t()},abort:function(){t&&t(void 0,!0)}}}}),le.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return le.globalEval(e),e}}}),le.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),le.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=be.head||le("head")[0]||be.documentElement;return{send:function(i,r){t=be.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||r(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var rn=[],on=/(=)\?(?=&|$)|\?\?/;le.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=rn.pop()||le.expando+"_"+Ht++;return this[e]=!0,e}}),le.ajaxPrefilter("json jsonp",function(e,t,n){var i,r,s,a=!1!==e.jsonp&&(on.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&on.test(e.data)&&"data");return a||"jsonp"===e.dataTypes[0]?(i=e.jsonpCallback=le.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(on,"$1"+i):!1!==e.jsonp&&(e.url+=(_t.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||le.error(i+" was not called"),s[0]},e.dataTypes[0]="json",r=o[i],o[i]=function(){s=arguments},n.always(function(){o[i]=r,e[i]&&(e.jsonpCallback=t.jsonpCallback,rn.push(i)),s&&le.isFunction(r)&&r(s[0]),s=r=void 0}),"script"):void 0}),le.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||be;var i=me.exec(e),r=!n&&[];return i?[t.createElement(i[1])]:(i=le.buildFragment([e],t,r),r&&r.length&&le(r).remove(),le.merge([],i.childNodes))};var sn=le.fn.load;le.fn.load=function(e,t,n){if("string"!=typeof e&&sn)return sn.apply(this,arguments);var i,r,o,s=this,a=e.indexOf(" ");return a>=0&&(i=le.trim(e.slice(a,e.length)),e=e.slice(0,a)),le.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),s.length>0&&le.ajax({url:e,type:o,dataType:"html",data:t}).done(function(e){r=arguments,s.html(i?le("<div>").append(le.parseHTML(e)).find(i):e)}).complete(n&&function(e,t){s.each(n,r||[e.responseText,t,e])}),this},le.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){le.fn[t]=function(e){return this.on(t,e)}}),le.expr.filters.animated=function(e){return le.grep(le.timers,function(t){return e===t.elem}).length};var an=o.document.documentElement;le.offset={setOffset:function(e,t,n){var i,r,o,s,a,c,l,u=le.css(e,"position"),h=le(e),d={};"static"===u&&(e.style.position="relative"),a=h.offset(),o=le.css(e,"top"),c=le.css(e,"left"),l=("absolute"===u||"fixed"===u)&&le.inArray("auto",[o,c])>-1,l?(i=h.position(),s=i.top,r=i.left):(s=parseFloat(o)||0,r=parseFloat(c)||0),le.isFunction(t)&&(t=t.call(e,n,a)),null!=t.top&&(d.top=t.top-a.top+s),null!=t.left&&(d.left=t.left-a.left+r),"using"in t?t.using.call(e,d):h.css(d)}},le.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){le.offset.setOffset(this,e,t)});var t,n,i={top:0,left:0},r=this[0],o=r&&r.ownerDocument;return o?(t=o.documentElement,le.contains(t,r)?(typeof r.getBoundingClientRect!==Ne&&(i=r.getBoundingClientRect()),n=Z(o),{top:i.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:i.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):i):void 0},position:function(){if(this[0]){var e,t,n={top:0,left:0},i=this[0];return"fixed"===le.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),le.nodeName(e[0],"html")||(n=e.offset()),n.top+=le.css(e[0],"borderTopWidth",!0),n.left+=le.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-le.css(i,"marginTop",!0),left:t.left-n.left-le.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||an;e&&!le.nodeName(e,"html")&&"static"===le.css(e,"position");)e=e.offsetParent;return e||an})}}),le.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n=/Y/.test(t);le.fn[e]=function(i){return Pe(this,function(e,i,r){var o=Z(e);return void 0===r?o?t in o?o[t]:o.document.documentElement[i]:e[i]:void(o?o.scrollTo(n?le(o).scrollLeft():r,n?r:le(o).scrollTop()):e[i]=r)},e,i,arguments.length,null)}}),le.each(["top","left"],function(e,t){le.cssHooks[t]=L(ae.pixelPosition,function(e,n){return n?(n=ot(e,t),at.test(n)?le(e).position()[t]+"px":n):void 0})}),le.each({Height:"height",Width:"width"},function(e,t){le.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){le.fn[i]=function(i,r){var o=arguments.length&&(n||"boolean"!=typeof i),s=n||(!0===i||!0===r?"margin":"border");return Pe(this,function(t,n,i){var r;return le.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])):void 0===i?le.css(t,n,s):le.style(t,n,i,s)},t,o?i:void 0,o,null)}})}),le.fn.size=function(){return this.length},le.fn.andSelf=le.fn.addBack,n(94)&&(i=[],void 0!==(r=function(){return le}.apply(t,i))&&(e.exports=r));var cn=o.jQuery,ln=o.$;return le.noConflict=function(e){return o.$===le&&(o.$=ln),e&&o.jQuery===le&&(o.jQuery=cn),le},typeof s===Ne&&(o.jQuery=o.$=le),le})},1:function(e,t,n){var i=n(43)("wks"),r=n(25),o=n(3).Symbol,s="function"==typeof o;(e.exports=function(e){return i[e]||(i[e]=s&&o[e]||(s?o:r)("Symbol."+e))}).store=i},10:function(e,t,n){var i=n(4),r=n(18);e.exports=n(5)?function(e,t,n){return i.f(e,t,r(1,n))}:function(e,t,n){return e[t]=n,e}},11:function(e,t){!function(){function e(e){var t=function(){};return t.prototype=e.prototype||e,new t}function t(e,t,n){if(n){var i={};for(var r in e)r!==t&&(i[r]=e[r])}else delete e[t];return i||e}function n(t,i,r){if(!t||!i)return t||i||{};t=e(t),i=e(i);for(var o in i)"[object Object]"===Object.prototype.toString.call(i[o])?n(t[o],i[o]):t[o]=r&&t[o]?t[o]:i[o];return t}function i(e){for(var r={},o=0;o<e.length;o++){"function"==typeof e[o]&&(e[o]=e[o].prototype);var s=t(e[o],"initialize",!0);r=s.implement?i(s.implement):n(r,s)}return r}var r=window.Class,o=window.Class=function(r){r=r||{};var s=function(){return this.initialize?this.initialize.apply(this,arguments):a};if(r.implement){var a=window===this?e(s.prototype):this,c=r.implement;t(r,"implement"),r=n(r,i(c))}s.prototype=e(r),s.constructor=s,s._parent=e(r);for(var l=0,u=["extend","implement","getOptions","setOptions"];l<u.length;l++)s[u[l]]=o[u[l]];return s};o.extend=function(e){var r=this;e.implement&&(this.prototype=n(this.prototype,i(e.implement)),t(e,"implement"));for(var o in e)e[o]="function"==typeof e[o]&&/parent/.test(e[o].toString())?function(e,t){return function(){return this.parent=r._parent[t],e.apply(this,arguments)}}(e[o],o):e[o];return this._parent=n(this._parent,e,!0),this.prototype=n(this.prototype,e),this},o.implement=function(e){return this.prototype=n(this.prototype,i(e))},o.getOptions=function(){return this.prototype.options||{}},o.setOptions=function(e){return this.prototype.options=n(this.prototype.options,e)},o.noConflict=function(){return window.Class=r,o},o.version="1.0"}(),e.exports=Class},112:function(e,t,n){var i=n(13),r=n(49),o=n(113);e.exports=function(e){return function(t,n,s){var a,c=i(t),l=r(c.length),u=o(s,l);if(e&&n!=n){for(;l>u;)if((a=c[u++])!=a)return!0}else for(;l>u;u++)if((e||u in c)&&c[u]===n)return e||u||0;return!e&&-1}}},113:function(e,t,n){var i=n(41),r=Math.max,o=Math.min;e.exports=function(e,t){return e=i(e),e<0?r(e+t,0):o(e,t)}},114:function(e,t,n){var i=n(41),r=n(40);e.exports=function(e){return function(t,n){var o,s,a=String(r(t)),c=i(n),l=a.length;return c<0||c>=l?e?"":void 0:(o=a.charCodeAt(c),o<55296||o>56319||c+1===l||(s=a.charCodeAt(c+1))<56320||s>57343?e?a.charAt(c):o:e?a.slice(c,c+2):s-56320+(o-55296<<10)+65536)}}},115:function(e,t,n){"use strict";var i=n(56),r=n(18),o=n(26),s={};n(10)(s,n(1)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=i(s,{next:r(1,n)}),o(e,t+" Iterator")}},116:function(e,t,n){var i=n(4),r=n(7),o=n(35);e.exports=n(5)?Object.defineProperties:function(e,t){r(e);for(var n,s=o(t),a=s.length,c=0;a>c;)i.f(e,n=s[c++],t[n]);return e}},117:function(e,t,n){var i=n(9),r=n(27),o=n(42)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=r(e),i(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},118:function(e,t,n){"use strict";var i=n(119),r=n(92),o=n(14),s=n(13);e.exports=n(55)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,r(1)):"keys"==t?r(0,n):"values"==t?r(0,e[n]):r(0,[n,e[n]])},"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},119:function(e,t){e.exports=function(){}},120:function(e,t,n){e.exports={default:n(121),__esModule:!0}},121:function(e,t,n){n(20),n(29),e.exports=n(45).f("iterator")},122:function(e,t,n){e.exports={default:n(123),__esModule:!0}},123:function(e,t,n){n(124),n(69),n(128),n(129),e.exports=n(2).Symbol},124:function(e,t,n){"use strict";var i=n(3),r=n(9),o=n(5),s=n(8),a=n(66),c=n(70).KEY,l=n(17),u=n(43),h=n(26),d=n(25),f=n(1),p=n(45),g=n(46),m=n(125),v=n(93),y=n(7),b=n(6),x=n(27),w=n(13),C=n(39),S=n(18),T=n(56),E=n(126),k=n(127),N=n(67),O=n(4),A=n(35),D=k.f,L=O.f,j=E.f,P=i.Symbol,H=i.JSON,_=H&&H.stringify,M=f("_hidden"),R=f("toPrimitive"),I={}.propertyIsEnumerable,F=u("symbol-registry"),q=u("symbols"),W=u("op-symbols"),B=Object.prototype,z="function"==typeof P&&!!N.f,U=i.QObject,$=!U||!U.prototype||!U.prototype.findChild,V=o&&l(function(){return 7!=T(L({},"a",{get:function(){return L(this,"a",{value:7}).a}})).a})?function(e,t,n){var i=D(B,t);i&&delete B[t],L(e,t,n),i&&e!==B&&L(B,t,i)}:L,G=function(e){var t=q[e]=T(P.prototype);return t._k=e,t},X=z&&"symbol"==typeof P.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof P},K=function(e,t,n){return e===B&&K(W,t,n),y(e),t=C(t,!0),y(n),r(q,t)?(n.enumerable?(r(e,M)&&e[M][t]&&(e[M][t]=!1),n=T(n,{enumerable:S(0,!1)})):(r(e,M)||L(e,M,S(1,{})),e[M][t]=!0),V(e,t,n)):L(e,t,n)},Y=function(e,t){y(e);for(var n,i=m(t=w(t)),r=0,o=i.length;o>r;)K(e,n=i[r++],t[n]);return e},J=function(e,t){return void 0===t?T(e):Y(T(e),t)},Z=function(e){var t=I.call(this,e=C(e,!0));return!(this===B&&r(q,e)&&!r(W,e))&&(!(t||!r(this,e)||!r(q,e)||r(this,M)&&this[M][e])||t)},Q=function(e,t){if(e=w(e),t=C(t,!0),e!==B||!r(q,t)||r(W,t)){var n=D(e,t);return!n||!r(q,t)||r(e,M)&&e[M][t]||(n.enumerable=!0),n}},ee=function(e){for(var t,n=j(w(e)),i=[],o=0;n.length>o;)r(q,t=n[o++])||t==M||t==c||i.push(t);return i},te=function(e){for(var t,n=e===B,i=j(n?W:w(e)),o=[],s=0;i.length>s;)!r(q,t=i[s++])||n&&!r(B,t)||o.push(q[t]);return o};z||(P=function(){if(this instanceof P)throw TypeError("Symbol is not a constructor!");var e=d(arguments.length>0?arguments[0]:void 0),t=function(n){this===B&&t.call(W,n),r(this,M)&&r(this[M],e)&&(this[M][e]=!1),V(this,e,S(1,n))};return o&&$&&V(B,e,{configurable:!0,set:t}),G(e)},a(P.prototype,"toString",function(){return this._k}),k.f=Q,O.f=K,n(68).f=E.f=ee,n(47).f=Z,N.f=te,o&&!n(23)&&a(B,"propertyIsEnumerable",Z,!0),p.f=function(e){return G(f(e))}),s(s.G+s.W+s.F*!z,{Symbol:P});for(var ne="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ie=0;ne.length>ie;)f(ne[ie++]);for(var re=A(f.store),oe=0;re.length>oe;)g(re[oe++]);s(s.S+s.F*!z,"Symbol",{for:function(e){return r(F,e+="")?F[e]:F[e]=P(e)},keyFor:function(e){if(!X(e))throw TypeError(e+" is not a symbol!");for(var t in F)if(F[t]===e)return t},useSetter:function(){$=!0},useSimple:function(){$=!1}}),s(s.S+s.F*!z,"Object",{create:J,defineProperty:K,defineProperties:Y,getOwnPropertyDescriptor:Q,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var se=l(function(){N.f(1)});s(s.S+s.F*se,"Object",{getOwnPropertySymbols:function(e){return N.f(x(e))}}),H&&s(s.S+s.F*(!z||l(function(){var e=P();return"[null]"!=_([e])||"{}"!=_({a:e})||"{}"!=_(Object(e))})),"JSON",{stringify:function(e){for(var t,n,i=[e],r=1;arguments.length>r;)i.push(arguments[r++]);if(n=t=i[1],(b(t)||void 0!==e)&&!X(e))return v(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!X(t))return t}),i[1]=t,_.apply(H,i)}}),P.prototype[R]||n(10)(P.prototype,R,P.prototype.valueOf),h(P,"Symbol"),h(Math,"Math",!0),h(i.JSON,"JSON",!0)},125:function(e,t,n){var i=n(35),r=n(67),o=n(47);e.exports=function(e){var t=i(e),n=r.f;if(n)for(var s,a=n(e),c=o.f,l=0;a.length>l;)c.call(e,s=a[l++])&&t.push(s);return t}},126:function(e,t,n){var i=n(13),r=n(68).f,o={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(e){try{return r(e)}catch(e){return s.slice()}};e.exports.f=function(e){return s&&"[object Window]"==o.call(e)?a(e):r(i(e))}},127:function(e,t,n){var i=n(47),r=n(18),o=n(13),s=n(39),a=n(9),c=n(64),l=Object.getOwnPropertyDescriptor;t.f=n(5)?l:function(e,t){if(e=o(e),t=s(t,!0),c)try{return l(e,t)}catch(e){}if(a(e,t))return r(!i.f.call(e,t),e[t])}},128:function(e,t,n){n(46)("asyncIterator")},129:function(e,t,n){n(46)("observable")},13:function(e,t,n){var i=n(90),r=n(40);e.exports=function(e){return i(r(e))}},14:function(e,t){e.exports={}},17:function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},18:function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},19:function(e,t,n){var i=n(48);e.exports=function(e,t,n){if(i(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,i){return e.call(t,n,i)};case 3:return function(n,i,r){return e.call(t,n,i,r)}}return function(){return e.apply(t,arguments)}}},196:function(e,t,n){var i=n(0);e.exports=function(e,t){if(t.length){var n=!1;""===t.val().trim()&&(n=!0),window.unihandecode&&(window.UNIHANDECODER=window.unihandecode.Unihan(t.data("decoder"))),e.on("keyup keypress",function(){var i=e.val();window.UNIHANDECODER&&(i=window.UNIHANDECODER.decode(i)),!1===n&&""===t.val()&&(n=!0);var r=URLify(i,64);n&&t.val(r)}),e.trigger("keyup"),t.add(e).bind("change",function(){i(this).data("changed",!0)})}}},2:function(e,t){var n=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},20:function(e,t,n){"use strict";var i=n(114)(!0);n(55)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=i(t,n),this._i+=e.length,{value:e,done:!1})})},23:function(e,t){e.exports=!0},25:function(e,t){var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},26:function(e,t,n){var i=n(4).f,r=n(9),o=n(1)("toStringTag");e.exports=function(e,t,n){e&&!r(e=n?e:e.prototype,o)&&i(e,o,{configurable:!0,value:t})}},27:function(e,t,n){var i=n(40);e.exports=function(e){return Object(i(e))}},28:function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},29:function(e,t,n){n(118);for(var i=n(3),r=n(10),o=n(14),s=n(1)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),c=0;c<a.length;c++){var l=a[c],u=i[l],h=u&&u.prototype;h&&!h[s]&&r(h,s,l),o[l]=o.Array}},3:function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},35:function(e,t,n){var i=n(65),r=n(44);e.exports=Object.keys||function(e){return i(e,r)}},351:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=n(95),r=n.n(i),o=n(0);!function(e){void 0===e.fn.each2&&e.extend(e.fn,{each2:function(t){for(var n=e([0]),i=-1,r=this.length;++i<r&&(n.context=n[0]=this[i])&&!1!==t.call(n[0],i,n););return this}})}(o),function(e,t){function n(t){var n=e(document.createTextNode(""));t.before(n),n.before(t),n.remove()}function i(e){function t(e){return W[e]||e}return e.replace(/[^\u0000-\u007E]/g,t)}function o(e,t){for(var n=0,i=t.length;n<i;n+=1)if(a(e,t[n]))return n;return-1}function s(){var t=e(q);t.appendTo("body");var n={width:t.width()-t[0].clientWidth,height:t.height()-t[0].clientHeight};return t.remove(),n}function a(e,n){return e===n||e!==t&&n!==t&&(null!==e&&null!==n&&(e.constructor===String?e+""==n+"":n.constructor===String&&n+""==e+""))}function c(t,n){var i,r,o;if(null===t||t.length<1)return[];for(i=t.split(n),r=0,o=i.length;r<o;r+=1)i[r]=e.trim(i[r]);return i}function l(e){return e.outerWidth(!1)-e.width()}function u(n){var i="keyup-change-value";n.on("keydown",function(){e.data(n,i)===t&&e.data(n,i,n.val())}),n.on("keyup",function(){var r=e.data(n,i);r!==t&&n.val()!==r&&(e.removeData(n,i),n.trigger("keyup-change"))})}function h(n){n.on("mousemove",function(n){var i=F;i!==t&&i.x===n.pageX&&i.y===n.pageY||e(n.target).trigger("mousemove-filtered",n)})}function d(e,n,i){i=i||t;var r;return function(){var t=arguments;window.clearTimeout(r),r=window.setTimeout(function(){n.apply(i,t)},e)}}function f(e,t){var n=d(e,function(e){t.trigger("scroll-debounced",e)});t.on("scroll",function(e){o(e.target,t.get())>=0&&n(e)})}function p(e){e[0]!==document.activeElement&&window.setTimeout(function(){var t,n=e[0],i=e.val().length;e.focus(),(n.offsetWidth>0||n.offsetHeight>0)&&n===document.activeElement&&(n.setSelectionRange?n.setSelectionRange(i,i):n.createTextRange&&(t=n.createTextRange(),t.collapse(!1),t.select()))},0)}function g(t){t=e(t)[0];var n=0,i=0;if("selectionStart"in t)n=t.selectionStart,i=t.selectionEnd-n;else if("selection"in document){t.focus();var r=document.selection.createRange();i=document.selection.createRange().text.length,r.moveStart("character",-t.value.length),n=r.text.length-i}return{offset:n,length:i}}function m(e){e.preventDefault(),e.stopPropagation()}function v(e){e.preventDefault(),e.stopImmediatePropagation()}function y(t){if(!M){var n=t[0].currentStyle||window.getComputedStyle(t[0],null);M=e(document.createElement("div")).css({position:"absolute",left:"-10000px",top:"-10000px",display:"none",fontSize:n.fontSize,fontFamily:n.fontFamily,fontStyle:n.fontStyle,fontWeight:n.fontWeight,letterSpacing:n.letterSpacing,textTransform:n.textTransform,whiteSpace:"nowrap"}),M.attr("class","select2-sizer"),e("body").append(M)}return M.text(t.val()),M.width()}function b(t,n,i){var r,o,s=[];r=e.trim(t.attr("class")),r&&(r=""+r,e(r.split(/\s+/)).each2(function(){0===this.indexOf("select2-")&&s.push(this)})),r=e.trim(n.attr("class")),r&&(r=""+r,e(r.split(/\s+/)).each2(function(){0!==this.indexOf("select2-")&&(o=i(this))&&s.push(o)})),t.attr("class",s.join(" "))}function x(e,t,n,r){var o=i(e.toUpperCase()).indexOf(i(t.toUpperCase())),s=t.length;if(o<0)return void n.push(r(e));n.push(r(e.substring(0,o))),n.push("<span class='select2-match'>"),n.push(r(e.substring(o,o+s))),n.push("</span>"),n.push(r(e.substring(o+s,e.length)))}function w(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return String(e).replace(/[&<>"'\/\\]/g,function(e){return t[e]})}function C(n){var i,r=null,o=n.quietMillis||100,s=n.url,a=this;return function(c){window.clearTimeout(i),i=window.setTimeout(function(){var i=n.data,o=s,l=n.transport||e.fn.select2.ajaxDefaults.transport,u={type:n.type||"GET",cache:n.cache||!1,jsonpCallback:n.jsonpCallback||t,dataType:n.dataType||"json"},h=e.extend({},e.fn.select2.ajaxDefaults.params,u);i=i?i.call(a,c.term,c.page,c.context):null,o="function"==typeof o?o.call(a,c.term,c.page,c.context):o,r&&"function"==typeof r.abort&&r.abort(),n.params&&(e.isFunction(n.params)?e.extend(h,n.params.call(a)):e.extend(h,n.params)),e.extend(h,{url:o,dataType:n.dataType,data:i,success:function(e){var t=n.results(e,c.page,c);c.callback(t)},error:function(e,t,n){var i={hasError:!0,jqXHR:e,textStatus:t,errorThrown:n};c.callback(i)}}),r=l.call(a,h)},o)}}function S(t){var n,i,r=t,o=function(e){return""+e.text};e.isArray(r)&&(i=r,r={results:i}),!1===e.isFunction(r)&&(i=r,r=function(){return i});var s=r();return s.text&&(o=s.text,e.isFunction(o)||(n=s.text,o=function(e){return e[n]})),function(t){var n,i=t.term,s={results:[]};if(""===i)return void t.callback(r());n=function(r,s){var a,c;if(r=r[0],r.children){a={};for(c in r)r.hasOwnProperty(c)&&(a[c]=r[c]);a.children=[],e(r.children).each2(function(e,t){n(t,a.children)}),(a.children.length||t.matcher(i,o(a),r))&&s.push(a)}else t.matcher(i,o(r),r)&&s.push(r)},e(r().results).each2(function(e,t){n(t,s.results)}),t.callback(s)}}function T(n){var i=e.isFunction(n);return function(r){var o=r.term,s={results:[]},a=i?n(r):n;e.isArray(a)&&(e(a).each(function(){var e=this.text!==t,n=e?this.text:this;(""===o||r.matcher(o,n))&&s.results.push(e?this:{id:this,text:this})}),r.callback(s))}}function E(t,n){if(e.isFunction(t))return!0;if(!t)return!1;if("string"==typeof t)return!0;throw new Error(n+" must be a string, function, or falsy value")}function k(t,n){if(e.isFunction(t)){var i=Array.prototype.slice.call(arguments,2);return t.apply(n,i)}return t}function N(t){var n=0;return e.each(t,function(e,t){t.children?n+=N(t.children):n++}),n}function O(e,n,i,r){var o,s,c,l,u,h=e,d=!1;if(!r.createSearchChoice||!r.tokenSeparators||r.tokenSeparators.length<1)return t;for(;;){for(s=-1,c=0,l=r.tokenSeparators.length;c<l&&(u=r.tokenSeparators[c],!((s=e.indexOf(u))>=0));c++);if(s<0)break;if(o=e.substring(0,s),e=e.substring(s+u.length),o.length>0&&(o=r.createSearchChoice.call(this,o,n))!==t&&null!==o&&r.id(o)!==t&&null!==r.id(o)){for(d=!1,c=0,l=n.length;c<l;c++)if(a(r.id(o),r.id(n[c]))){d=!0;break}d||i(o)}}return h!==e?e:void 0}function A(){var t=this;e.each(arguments,function(e,n){t[n].remove(),t[n]=null})}function D(t,n){var i=function(){};return i.prototype=new t,i.prototype.constructor=i,i.prototype.parent=t.prototype,i.prototype=e.extend(i.prototype,n),i}if(window.Select2===t){var L,j,P,H,_,M,R,I,F={x:0,y:0},L={TAB:9,ENTER:13,ESC:27,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40,SHIFT:16,CTRL:17,ALT:18,PAGE_UP:33,PAGE_DOWN:34,HOME:36,END:35,BACKSPACE:8,DELETE:46,isArrow:function(e){switch(e=e.which?e.which:e){case L.LEFT:case L.RIGHT:case L.UP:case L.DOWN:return!0}return!1},isControl:function(e){switch(e.which){case L.SHIFT:case L.CTRL:case L.ALT:return!0}return!!e.metaKey},isFunctionKey:function(e){return(e=e.which?e.which:e)>=112&&e<=123}},q="<div class='select2-measure-scrollbar'></div>",W={"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"};R=e(document),_=function(){var e=1;return function(){return e++}}(),j=D(Object,{bind:function(e){var t=this;return function(){e.apply(t,arguments)}},init:function(n){var i,r;this.opts=n=this.prepareOpts(n),this.id=n.id,n.element.data("select2")!==t&&null!==n.element.data("select2")&&n.element.data("select2").destroy(),this.container=this.createContainer(),this.liveRegion=e("<span>",{role:"status","aria-live":"polite"}).addClass("select2-hidden-accessible").appendTo(document.body),this.containerId="s2id_"+(n.element.attr("id")||"autogen"+_()),this.containerEventName=this.containerId.replace(/([.])/g,"_").replace(/([;&,\-\.\+\*\~':"\!\^#$%@\[\]\(\)=>\|])/g,"\\$1"),this.container.attr("id",this.containerId),this.container.attr("title",n.element.attr("title")),this.body=e("body"),b(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.attr("style",n.element.attr("style")),this.container.css(k(n.containerCss,this.opts.element)),this.container.addClass(k(n.containerCssClass,this.opts.element)),this.elementTabIndex=this.opts.element.attr("tabindex"),this.opts.element.data("select2",this).attr("tabindex","-1").before(this.container).on("click.select2",m),this.container.data("select2",this),this.dropdown=this.container.find(".select2-drop"),b(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass),this.dropdown.addClass(k(n.dropdownCssClass,this.opts.element)),this.dropdown.data("select2",this),this.dropdown.on("click",m),this.results=i=this.container.find(".select2-results"),this.search=r=this.container.find("input.select2-input"),this.queryCount=0,this.resultsPage=0,this.context=null,this.initContainer(),this.container.on("click",m),h(this.results),this.dropdown.on("mousemove-filtered",".select2-results",this.bind(this.highlightUnderEvent)),this.dropdown.on("touchstart touchmove touchend",".select2-results",this.bind(function(e){this._touchEvent=!0,this.highlightUnderEvent(e)})),this.dropdown.on("touchmove",".select2-results",this.bind(this.touchMoved)),this.dropdown.on("touchstart touchend",".select2-results",this.bind(this.clearTouchMoved)),this.dropdown.on("click",this.bind(function(e){this._touchEvent&&(this._touchEvent=!1,this.selectHighlighted())})),f(80,this.results),this.dropdown.on("scroll-debounced",".select2-results",this.bind(this.loadMoreIfNeeded)),e(this.container).on("change",".select2-input",function(e){e.stopPropagation()}),e(this.dropdown).on("change",".select2-input",function(e){e.stopPropagation()}),e.fn.mousewheel&&i.mousewheel(function(e,t,n,r){var o=i.scrollTop();r>0&&o-r<=0?(i.scrollTop(0),m(e)):r<0&&i.get(0).scrollHeight-i.scrollTop()+r<=i.height()&&(i.scrollTop(i.get(0).scrollHeight-i.height()),m(e))}),u(r),r.on("keyup-change input paste",this.bind(this.updateResults)),r.on("focus",function(){r.addClass("select2-focused")}),r.on("blur",function(){r.removeClass("select2-focused")}),this.dropdown.on("mouseup",".select2-results",this.bind(function(t){e(t.target).closest(".select2-result-selectable").length>0&&(this.highlightUnderEvent(t),this.selectHighlighted(t))})),this.dropdown.on("click mouseup mousedown touchstart touchend focusin",function(e){e.stopPropagation()}),this.nextSearchTerm=t,e.isFunction(this.opts.initSelection)&&(this.initSelection(),this.monitorSource()),null!==n.maximumInputLength&&this.search.attr("maxlength",n.maximumInputLength);var o=n.element.prop("disabled");o===t&&(o=!1),this.enable(!o);var a=n.element.prop("readonly");a===t&&(a=!1),this.readonly(a),I=I||s(),this.autofocus=n.element.prop("autofocus"),n.element.prop("autofocus",!1),this.autofocus&&this.focus(),this.search.attr("placeholder",n.searchInputPlaceholder)},destroy:function(){var e=this.opts.element,n=e.data("select2"),i=this;this.close(),e.length&&e[0].detachEvent&&e.each(function(){this.detachEvent("onpropertychange",i._sync)}),this.propertyObserver&&(this.propertyObserver.disconnect(),this.propertyObserver=null),this._sync=null,n!==t&&(n.container.remove(),n.liveRegion.remove(),n.dropdown.remove(),e.removeClass("select2-offscreen").removeData("select2").off(".select2").prop("autofocus",this.autofocus||!1),this.elementTabIndex?e.attr({tabindex:this.elementTabIndex}):e.removeAttr("tabindex"),e.show()),A.call(this,"container","liveRegion","dropdown","results","search")},optionToData:function(e){return e.is("option")?{id:e.prop("value"),text:e.text(),element:e.get(),css:e.attr("class"),disabled:e.prop("disabled"),locked:a(e.attr("locked"),"locked")||a(e.data("locked"),!0)}:e.is("optgroup")?{text:e.attr("label"),children:[],element:e.get(),css:e.attr("class")}:void 0},prepareOpts:function(n){var i,r,o,s,l=this;if(i=n.element,"select"===i.get(0).tagName.toLowerCase()&&(this.select=r=n.element),r&&e.each(["id","multiple","ajax","query","createSearchChoice","initSelection","data","tags"],function(){if(this in n)throw new Error("Option '"+this+"' is not allowed for Select2 when attached to a <select> element.")}),n=e.extend({},{populateResults:function(i,r,o){var s,a=this.opts.id,c=this.liveRegion;(s=function(i,r,u){var h,d,f,p,g,m,v,y,b,x;i=n.sortResults(i,r,o);var w=[];for(h=0,d=i.length;h<d;h+=1)f=i[h],g=!0===f.disabled,p=!g&&a(f)!==t,m=f.children&&f.children.length>0,v=e("<li></li>"),v.addClass("select2-results-dept-"+u),v.addClass("select2-result"),v.addClass(p?"select2-result-selectable":"select2-result-unselectable"),g&&v.addClass("select2-disabled"),m&&v.addClass("select2-result-with-children"),v.addClass(l.opts.formatResultCssClass(f)),v.attr("role","presentation"),y=e(document.createElement("div")),y.addClass("select2-result-label"),y.attr("id","select2-result-label-"+_()),y.attr("role","option"),x=n.formatResult(f,y,o,l.opts.escapeMarkup),x!==t&&(y.html(x),v.append(y)),m&&(b=e("<ul></ul>"),b.addClass("select2-result-sub"),s(f.children,b,u+1),v.append(b)),v.data("select2-data",f),w.push(v[0]);r.append(w),c.text(n.formatMatches(i.length))})(r,i,0)}},e.fn.select2.defaults,n),"function"!=typeof n.id&&(o=n.id,n.id=function(e){return e[o]}),e.isArray(n.element.data("select2Tags"))){if("tags"in n)throw"tags specified as both an attribute 'data-select2-tags' and in options of Select2 "+n.element.attr("id");n.tags=n.element.data("select2Tags")}if(r?(n.query=this.bind(function(e){var n,r,o,s={results:[],more:!1},a=e.term;o=function(t,n){var i;t.is("option")?e.matcher(a,t.text(),t)&&n.push(l.optionToData(t)):t.is("optgroup")&&(i=l.optionToData(t),t.children().each2(function(e,t){o(t,i.children)}),i.children.length>0&&n.push(i))},n=i.children(),this.getPlaceholder()!==t&&n.length>0&&(r=this.getPlaceholderOption())&&(n=n.not(r)),n.each2(function(e,t){o(t,s.results)}),e.callback(s)}),n.id=function(e){return e.id}):"query"in n||("ajax"in n?(s=n.element.data("ajax-url"),s&&s.length>0&&(n.ajax.url=s),n.query=C.call(n.element,n.ajax)):"data"in n?n.query=S(n.data):"tags"in n&&(n.query=T(n.tags),n.createSearchChoice===t&&(n.createSearchChoice=function(t){return{id:e.trim(t),text:e.trim(t)}}),n.initSelection===t&&(n.initSelection=function(t,i){var r=[];e(c(t.val(),n.separator)).each(function(){var t={id:this,text:this},i=n.tags;e.isFunction(i)&&(i=i()),e(i).each(function(){if(a(this.id,t.id))return t=this,!1}),r.push(t)}),i(r)}))),"function"!=typeof n.query)throw"query function not defined for Select2 "+n.element.attr("id");if("top"===n.createSearchChoicePosition)n.createSearchChoicePosition=function(e,t){e.unshift(t)};else if("bottom"===n.createSearchChoicePosition)n.createSearchChoicePosition=function(e,t){e.push(t)};else if("function"!=typeof n.createSearchChoicePosition)throw"invalid createSearchChoicePosition option must be 'top', 'bottom' or a custom function";return n},monitorSource:function(){var n,i=this.opts.element,r=this;i.on("change.select2",this.bind(function(e){!0!==this.opts.element.data("select2-change-triggered")&&this.initSelection()})),this._sync=this.bind(function(){var e=i.prop("disabled");e===t&&(e=!1),this.enable(!e);var n=i.prop("readonly");n===t&&(n=!1),this.readonly(n),b(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.addClass(k(this.opts.containerCssClass,this.opts.element)),b(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass),this.dropdown.addClass(k(this.opts.dropdownCssClass,this.opts.element))}),i.length&&i[0].attachEvent&&i.each(function(){this.attachEvent("onpropertychange",r._sync)}),(n=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver)!==t&&(this.propertyObserver&&(delete this.propertyObserver,this.propertyObserver=null),this.propertyObserver=new n(function(t){e.each(t,r._sync)}),this.propertyObserver.observe(i.get(0),{attributes:!0,subtree:!1}))},triggerSelect:function(t){var n=e.Event("select2-selecting",{val:this.id(t),object:t,choice:t});return this.opts.element.trigger(n),!n.isDefaultPrevented()},triggerChange:function(t){t=t||{},t=e.extend({},t,{type:"change",val:this.val()}),this.opts.element.data("select2-change-triggered",!0),this.opts.element.trigger(t),this.opts.element.data("select2-change-triggered",!1),this.opts.element.click(),this.opts.blurOnChange&&this.opts.element.blur()},isInterfaceEnabled:function(){return!0===this.enabledInterface},enableInterface:function(){var e=this._enabled&&!this._readonly,t=!e;return e!==this.enabledInterface&&(this.container.toggleClass("select2-container-disabled",t),this.close(),this.enabledInterface=e,!0)},enable:function(e){e===t&&(e=!0),this._enabled!==e&&(this._enabled=e,this.opts.element.prop("disabled",!e),this.enableInterface())},disable:function(){this.enable(!1)},readonly:function(e){e===t&&(e=!1),this._readonly!==e&&(this._readonly=e,this.opts.element.prop("readonly",e),this.enableInterface())},opened:function(){return!!this.container&&this.container.hasClass("select2-dropdown-open")},positionDropdown:function(){var t,n,i,r,o,s=this.dropdown,a=this.container.offset(),c=this.container.outerHeight(!1),l=this.container.outerWidth(!1),u=s.outerHeight(!1),h=e(window),d=h.width(),f=h.height(),p=h.scrollLeft()+d,g=h.scrollTop()+f,m=a.top+c,v=a.left,y=m+u<=g,b=a.top-u>=h.scrollTop(),x=s.outerWidth(!1),w=v+x<=p,C=s.hasClass("select2-drop-above");C?(n=!0,!b&&y&&(i=!0,n=!1)):(n=!1,!y&&b&&(i=!0,n=!0)),i&&(s.hide(),a=this.container.offset(),c=this.container.outerHeight(!1),l=this.container.outerWidth(!1),u=s.outerHeight(!1),p=h.scrollLeft()+d,g=h.scrollTop()+f,m=a.top+c,v=a.left,x=s.outerWidth(!1),w=v+x<=p,s.show(),this.focusSearch()),this.opts.dropdownAutoWidth?(o=e(".select2-results",s)[0],s.addClass("select2-drop-auto-width"),s.css("width",""),x=s.outerWidth(!1)+(o.scrollHeight===o.clientHeight?0:I.width),x>l?l=x:x=l,u=s.outerHeight(!1),w=v+x<=p):this.container.removeClass("select2-drop-auto-width"),"static"!==this.body.css("position")&&(t=this.body.offset(),m-=t.top,v-=t.left),w||(v=a.left+this.container.outerWidth(!1)-x),r={left:v,width:l},n?(r.top=a.top-u,r.bottom="auto",this.container.addClass("select2-drop-above"),s.addClass("select2-drop-above")):(r.top=m,r.bottom="auto",this.container.removeClass("select2-drop-above"),s.removeClass("select2-drop-above")),r=e.extend(r,k(this.opts.dropdownCss,this.opts.element)),s.css(r)},shouldOpen:function(){var t;return!this.opened()&&(!1!==this._enabled&&!0!==this._readonly&&(t=e.Event("select2-opening"),this.opts.element.trigger(t),!t.isDefaultPrevented()))},clearDropdownAlignmentPreference:function(){this.container.removeClass("select2-drop-above"),this.dropdown.removeClass("select2-drop-above")},open:function(){return!!this.shouldOpen()&&(this.opening(),R.on("mousemove.select2Event",function(e){F.x=e.pageX,F.y=e.pageY}),!0)},opening:function(){var t,i=this.containerEventName,r="scroll."+i,o="resize."+i,s="orientationchange."+i;this.container.addClass("select2-dropdown-open").addClass("select2-container-active"),this.clearDropdownAlignmentPreference(),this.dropdown[0]!==this.body.children().last()[0]&&this.dropdown.detach().appendTo(this.body),t=e("#select2-drop-mask"),0==t.length&&(t=e(document.createElement("div")),t.attr("id","select2-drop-mask").attr("class","select2-drop-mask"),t.hide(),t.appendTo(this.body),t.on("mousedown touchstart click",function(i){n(t);var r,o=e("#select2-drop");o.length>0&&(r=o.data("select2"),r.opts.selectOnBlur&&r.selectHighlighted({noFocus:!0}),r.close(),i.preventDefault(),i.stopPropagation())})),this.dropdown.prev()[0]!==t[0]&&this.dropdown.before(t),e("#select2-drop").removeAttr("id"),this.dropdown.attr("id","select2-drop"),t.show(),this.positionDropdown(),this.dropdown.show(),this.positionDropdown(),this.dropdown.addClass("select2-drop-active");var a=this;this.container.parents().add(window).each(function(){e(this).on(o+" "+r+" "+s,function(e){a.opened()&&a.positionDropdown()})})},close:function(){if(this.opened()){var t=this.containerEventName,n="scroll."+t,i="resize."+t,r="orientationchange."+t;this.container.parents().add(window).each(function(){e(this).off(n).off(i).off(r)}),this.clearDropdownAlignmentPreference(),e("#select2-drop-mask").hide(),this.dropdown.removeAttr("id"),this.dropdown.hide(),this.container.removeClass("select2-dropdown-open").removeClass("select2-container-active"),this.results.empty(),R.off("mousemove.select2Event"),this.clearSearch(),this.search.removeClass("select2-active"),this.opts.element.trigger(e.Event("select2-close"))}},externalSearch:function(e){this.open(),this.search.val(e),this.updateResults(!1)},clearSearch:function(){},getMaximumSelectionSize:function(){return k(this.opts.maximumSelectionSize,this.opts.element)},ensureHighlightVisible:function(){var t,n,i,r,o,s,a,c,l=this.results;if(!((n=this.highlight())<0)){if(0==n)return void l.scrollTop(0);t=this.findHighlightableChoices().find(".select2-result-label"),i=e(t[n]),c=(i.offset()||{}).top||0,r=c+i.outerHeight(!0),n===t.length-1&&(a=l.find("li.select2-more-results"),a.length>0&&(r=a.offset().top+a.outerHeight(!0))),o=l.offset().top+l.outerHeight(!0),r>o&&l.scrollTop(l.scrollTop()+(r-o)),s=c-l.offset().top,s<0&&"none"!=i.css("display")&&l.scrollTop(l.scrollTop()+s)}},findHighlightableChoices:function(){return this.results.find(".select2-result-selectable:not(.select2-disabled):not(.select2-selected)")},moveHighlight:function(t){for(var n=this.findHighlightableChoices(),i=this.highlight();i>-1&&i<n.length;){i+=t;var r=e(n[i]);if(r.hasClass("select2-result-selectable")&&!r.hasClass("select2-disabled")&&!r.hasClass("select2-selected")){this.highlight(i);break}}},highlight:function(t){var n,i,r=this.findHighlightableChoices();if(0===arguments.length)return o(r.filter(".select2-highlighted")[0],r.get());t>=r.length&&(t=r.length-1),t<0&&(t=0),this.removeHighlight(),n=e(r[t]),n.addClass("select2-highlighted"),this.search.attr("aria-activedescendant",n.find(".select2-result-label").attr("id")),this.ensureHighlightVisible(),this.liveRegion.text(n.text()),(i=n.data("select2-data"))&&this.opts.element.trigger({type:"select2-highlight",val:this.id(i),choice:i})},removeHighlight:function(){this.results.find(".select2-highlighted").removeClass("select2-highlighted")},touchMoved:function(){this._touchMoved=!0},clearTouchMoved:function(){this._touchMoved=!1},countSelectableResults:function(){return this.findHighlightableChoices().length},highlightUnderEvent:function(t){var n=e(t.target).closest(".select2-result-selectable");if(n.length>0&&!n.is(".select2-highlighted")){var i=this.findHighlightableChoices();this.highlight(i.index(n))}else 0==n.length&&this.removeHighlight()},loadMoreIfNeeded:function(){var e=this.results,t=e.find("li.select2-more-results"),n=this.resultsPage+1,i=this,r=this.search.val(),o=this.context;0!==t.length&&t.offset().top-e.offset().top-e.height()<=this.opts.loadMorePadding&&(t.addClass("select2-active"),this.opts.query({element:this.opts.element,term:r,page:n,context:o,matcher:this.opts.matcher,callback:this.bind(function(s){i.opened()&&(i.opts.populateResults.call(this,e,s.results,{term:r,page:n,context:o}),i.postprocessResults(s,!1,!1),!0===s.more?(t.detach().appendTo(e).text(k(i.opts.formatLoadMore,i.opts.element,n+1)),window.setTimeout(function(){i.loadMoreIfNeeded()},10)):t.remove(),i.positionDropdown(),i.resultsPage=n,i.context=s.context,this.opts.element.trigger({type:"select2-loaded",items:s}))})}))},tokenize:function(){},updateResults:function(n){function i(){l.removeClass("select2-active"),d.positionDropdown(),u.find(".select2-no-results,.select2-selection-limit,.select2-searching").length?d.liveRegion.text(u.text()):d.liveRegion.text(d.opts.formatMatches(u.find(".select2-result-selectable").length))}function r(e){u.html(e),i()}var o,s,c,l=this.search,u=this.results,h=this.opts,d=this,f=l.val(),p=e.data(this.container,"select2-last-term");if((!0===n||!p||!a(f,p))&&(e.data(this.container,"select2-last-term",f),!0===n||!1!==this.showSearchInput&&this.opened())){c=++this.queryCount;var g=this.getMaximumSelectionSize();if(g>=1&&(o=this.data(),e.isArray(o)&&o.length>=g&&E(h.formatSelectionTooBig,"formatSelectionTooBig")))return void r("<li class='select2-selection-limit'>"+k(h.formatSelectionTooBig,h.element,g)+"</li>");if(l.val().length<h.minimumInputLength)return r(E(h.formatInputTooShort,"formatInputTooShort")?"<li class='select2-no-results'>"+k(h.formatInputTooShort,h.element,l.val(),h.minimumInputLength)+"</li>":""),void(n&&this.showSearch&&this.showSearch(!0));if(h.maximumInputLength&&l.val().length>h.maximumInputLength)return void r(E(h.formatInputTooLong,"formatInputTooLong")?"<li class='select2-no-results'>"+k(h.formatInputTooLong,h.element,l.val(),h.maximumInputLength)+"</li>":"");h.formatSearching&&0===this.findHighlightableChoices().length&&r("<li class='select2-searching'>"+k(h.formatSearching,h.element)+"</li>"),l.addClass("select2-active"),this.removeHighlight(),s=this.tokenize(),s!=t&&null!=s&&l.val(s),this.resultsPage=1,h.query({element:h.element,term:l.val(),page:this.resultsPage,context:null,matcher:h.matcher,callback:this.bind(function(o){var s;if(c==this.queryCount){if(!this.opened())return void this.search.removeClass("select2-active");if(o.hasError!==t&&E(h.formatAjaxError,"formatAjaxError"))return void r("<li class='select2-ajax-error'>"+k(h.formatAjaxError,h.element,o.jqXHR,o.textStatus,o.errorThrown)+"</li>");if(this.context=o.context===t?null:o.context,this.opts.createSearchChoice&&""!==l.val()&&(s=this.opts.createSearchChoice.call(d,l.val(),o.results))!==t&&null!==s&&d.id(s)!==t&&null!==d.id(s)&&0===e(o.results).filter(function(){return a(d.id(this),d.id(s))}).length&&this.opts.createSearchChoicePosition(o.results,s),0===o.results.length&&E(h.formatNoMatches,"formatNoMatches"))return void r("<li class='select2-no-results'>"+k(h.formatNoMatches,h.element,l.val())+"</li>");u.empty(),d.opts.populateResults.call(this,u,o.results,{term:l.val(),page:this.resultsPage,context:null}),!0===o.more&&E(h.formatLoadMore,"formatLoadMore")&&(u.append("<li class='select2-more-results'>"+h.escapeMarkup(k(h.formatLoadMore,h.element,this.resultsPage))+"</li>"),window.setTimeout(function(){d.loadMoreIfNeeded()},10)),this.postprocessResults(o,n),i(),this.opts.element.trigger({type:"select2-loaded",items:o})}})})}},cancel:function(){this.close()},blur:function(){this.opts.selectOnBlur&&this.selectHighlighted({noFocus:!0}),this.close(),this.container.removeClass("select2-container-active"),this.search[0]===document.activeElement&&this.search.blur(),this.clearSearch(),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus")},focusSearch:function(){p(this.search)},selectHighlighted:function(e){if(this._touchMoved)return void this.clearTouchMoved();var t=this.highlight(),n=this.results.find(".select2-highlighted"),i=n.closest(".select2-result").data("select2-data");i?(this.highlight(t),this.onSelect(i,e)):e&&e.noFocus&&this.close()},getPlaceholder:function(){var e;return this.opts.element.attr("placeholder")||this.opts.element.attr("data-placeholder")||this.opts.element.data("placeholder")||this.opts.placeholder||((e=this.getPlaceholderOption())!==t?e.text():t)},getPlaceholderOption:function(){if(this.select){var n=this.select.children("option").first();if(this.opts.placeholderOption!==t)return"first"===this.opts.placeholderOption&&n||"function"==typeof this.opts.placeholderOption&&this.opts.placeholderOption(this.select);if(""===e.trim(n.text())&&""===n.val())return n}},initContainerWidth:function(){function n(){var n,i,r,o,s,a;if("off"===this.opts.width)return null;if("element"===this.opts.width)return 0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px";if("copy"===this.opts.width||"resolve"===this.opts.width){if((n=this.opts.element.attr("style"))!==t)for(i=n.split(";"),o=0,s=i.length;o<s;o+=1)if(a=i[o].replace(/\s/g,""),null!==(r=a.match(/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i))&&r.length>=1)return r[1];return"resolve"===this.opts.width?(n=this.opts.element.css("width"),n.indexOf("%")>0?n:0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px"):null}return e.isFunction(this.opts.width)?this.opts.width():this.opts.width}var i=n.call(this);null!==i&&this.container.css("width",i)}}),P=D(j,{createContainer:function(){return e(document.createElement("div")).attr({class:"select2-container"}).html(["<a href='javascript:void(0)' class='select2-choice' tabindex='-1'>","   <span class='select2-chosen'>&#160;</span><abbr class='select2-search-choice-close'></abbr>","   <span class='select2-arrow' role='presentation'><b role='presentation'></b></span>","</a>","<label for='' class='select2-offscreen'></label>","<input class='select2-focusser select2-offscreen' type='text' aria-haspopup='true' role='button' />","<div class='select2-drop select2-display-none'>","   <div class='select2-search'>","       <label for='' class='select2-offscreen'></label>","       <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input' role='combobox' aria-expanded='true'","       aria-autocomplete='list' />","   </div>","   <ul class='select2-results' role='listbox'>","   </ul>","</div>"].join(""))},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.focusser.prop("disabled",!this.isInterfaceEnabled())},opening:function(){var n,i,r;this.opts.minimumResultsForSearch>=0&&this.showSearch(!0),this.parent.opening.apply(this,arguments),!1!==this.showSearchInput&&this.search.val(this.focusser.val()),this.opts.shouldFocusInput(this)&&(this.search.focus(),n=this.search.get(0),n.createTextRange?(i=n.createTextRange(),i.collapse(!1),i.select()):n.setSelectionRange&&(r=this.search.val().length,n.setSelectionRange(r,r))),""===this.search.val()&&this.nextSearchTerm!=t&&(this.search.val(this.nextSearchTerm),this.search.select()),this.focusser.prop("disabled",!0).val(""),this.updateResults(!0),this.opts.element.trigger(e.Event("select2-open"))},close:function(){this.opened()&&(this.parent.close.apply(this,arguments),this.focusser.prop("disabled",!1),this.opts.shouldFocusInput(this)&&this.focusser.focus())},focus:function(){this.opened()?this.close():(this.focusser.prop("disabled",!1),this.opts.shouldFocusInput(this)&&this.focusser.focus())},isFocused:function(){return this.container.hasClass("select2-container-active")},cancel:function(){this.parent.cancel.apply(this,arguments),this.focusser.prop("disabled",!1),this.opts.shouldFocusInput(this)&&this.focusser.focus()},destroy:function(){e("label[for='"+this.focusser.attr("id")+"']").attr("for",this.opts.element.attr("id")),this.parent.destroy.apply(this,arguments),A.call(this,"selection","focusser")},initContainer:function(){var t,i,r=this.container,o=this.dropdown,s=_();this.opts.minimumResultsForSearch<0?this.showSearch(!1):this.showSearch(!0),this.selection=t=r.find(".select2-choice"),this.focusser=r.find(".select2-focusser"),t.find(".select2-chosen").attr("id","select2-chosen-"+s),this.focusser.attr("aria-labelledby","select2-chosen-"+s),this.results.attr("id","select2-results-"+s),this.search.attr("aria-owns","select2-results-"+s),this.focusser.attr("id","s2id_autogen"+s),i=e("label[for='"+this.opts.element.attr("id")+"']"),this.focusser.prev().text(i.text()).attr("for",this.focusser.attr("id"));var a=this.opts.element.attr("title");this.opts.element.attr("title",a||i.text()),this.focusser.attr("tabindex",this.elementTabIndex),this.search.attr("id",this.focusser.attr("id")+"_search"),this.search.prev().text(e("label[for='"+this.focusser.attr("id")+"']").text()).attr("for",this.search.attr("id")),this.search.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()&&229!=e.keyCode){if(e.which===L.PAGE_UP||e.which===L.PAGE_DOWN)return void m(e);switch(e.which){case L.UP:case L.DOWN:return this.moveHighlight(e.which===L.UP?-1:1),void m(e);case L.ENTER:return this.selectHighlighted(),void m(e);case L.TAB:return void this.selectHighlighted({noFocus:!0});case L.ESC:return this.cancel(e),void m(e)}}})),this.search.on("blur",this.bind(function(e){document.activeElement===this.body.get(0)&&window.setTimeout(this.bind(function(){this.opened()&&this.search.focus()}),0)})),this.focusser.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()&&e.which!==L.TAB&&!L.isControl(e)&&!L.isFunctionKey(e)&&e.which!==L.ESC){if(!1===this.opts.openOnEnter&&e.which===L.ENTER)return void m(e);if(e.which==L.DOWN||e.which==L.UP||e.which==L.ENTER&&this.opts.openOnEnter){if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey)return;return this.open(),void m(e)}return e.which==L.DELETE||e.which==L.BACKSPACE?(this.opts.allowClear&&this.clear(),void m(e)):void 0}})),u(this.focusser),this.focusser.on("keyup-change input",this.bind(function(e){if(this.opts.minimumResultsForSearch>=0){if(e.stopPropagation(),this.opened())return;this.open()}})),t.on("mousedown touchstart","abbr",this.bind(function(e){this.isInterfaceEnabled()&&(this.clear(),v(e),this.close(),this.selection.focus())})),t.on("mousedown touchstart",this.bind(function(i){n(t),this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.opened()?this.close():this.isInterfaceEnabled()&&this.open(),m(i)})),o.on("mousedown touchstart",this.bind(function(){this.opts.shouldFocusInput(this)&&this.search.focus()})),t.on("focus",this.bind(function(e){m(e)})),this.focusser.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active")})).on("blur",this.bind(function(){this.opened()||(this.container.removeClass("select2-container-active"),this.opts.element.trigger(e.Event("select2-blur")))})),this.search.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active")})),this.initContainerWidth(),this.opts.element.addClass("select2-offscreen"),this.setPlaceholder()},clear:function(t){var n=this.selection.data("select2-data");if(n){var i=e.Event("select2-clearing");if(this.opts.element.trigger(i),i.isDefaultPrevented())return;var r=this.getPlaceholderOption();this.opts.element.val(r?r.val():""),this.selection.find(".select2-chosen").empty(),this.selection.removeData("select2-data"),this.setPlaceholder(),!1!==t&&(this.opts.element.trigger({type:"select2-removed",val:this.id(n),choice:n}),this.triggerChange({removed:n}))}},initSelection:function(){if(this.isPlaceholderOptionSelected())this.updateSelection(null),this.close(),this.setPlaceholder();else{var e=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==t&&null!==n&&(e.updateSelection(n),e.close(),e.setPlaceholder(),e.nextSearchTerm=e.opts.nextSearchTerm(n,e.search.val()))})}},isPlaceholderOptionSelected:function(){var e;return this.getPlaceholder()!==t&&((e=this.getPlaceholderOption())!==t&&e.prop("selected")||""===this.opts.element.val()||this.opts.element.val()===t||null===this.opts.element.val())},prepareOpts:function(){var t=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===t.element.get(0).tagName.toLowerCase()?t.initSelection=function(e,t){var i=e.find("option").filter(function(){return this.selected&&!this.disabled});t(n.optionToData(i))}:"data"in t&&(t.initSelection=t.initSelection||function(n,i){var r=n.val(),o=null;t.query({matcher:function(e,n,i){var s=a(r,t.id(i));return s&&(o=i),s},callback:e.isFunction(i)?function(){i(o)}:e.noop})}),t},getPlaceholder:function(){return this.select&&this.getPlaceholderOption()===t?t:this.parent.getPlaceholder.apply(this,arguments)},setPlaceholder:function(){var e=this.getPlaceholder();if(this.isPlaceholderOptionSelected()&&e!==t){if(this.select&&this.getPlaceholderOption()===t)return;this.selection.find(".select2-chosen").html(this.opts.escapeMarkup(e)),this.selection.addClass("select2-default"),this.container.removeClass("select2-allowclear")}},postprocessResults:function(e,t,n){var i=0,r=this;if(this.findHighlightableChoices().each2(function(e,t){if(a(r.id(t.data("select2-data")),r.opts.element.val()))return i=e,!1}),!1!==n&&(!0===t&&i>=0?this.highlight(i):this.highlight(0)),!0===t){var o=this.opts.minimumResultsForSearch;o>=0&&this.showSearch(N(e.results)>=o)}},showSearch:function(t){this.showSearchInput!==t&&(this.showSearchInput=t,this.dropdown.find(".select2-search").toggleClass("select2-search-hidden",!t),this.dropdown.find(".select2-search").toggleClass("select2-offscreen",!t),e(this.dropdown,this.container).toggleClass("select2-with-searchbox",t))},onSelect:function(e,t){if(this.triggerSelect(e)){var n=this.opts.element.val(),i=this.data();this.opts.element.val(this.id(e)),this.updateSelection(e),this.opts.element.trigger({type:"select2-selected",val:this.id(e),choice:e}),this.nextSearchTerm=this.opts.nextSearchTerm(e,this.search.val()),this.close(),t&&t.noFocus||!this.opts.shouldFocusInput(this)||this.focusser.focus(),a(n,this.id(e))||this.triggerChange({added:e,removed:i})}},updateSelection:function(e){var n,i,r=this.selection.find(".select2-chosen");this.selection.data("select2-data",e),r.empty(),null!==e&&(n=this.opts.formatSelection(e,r,this.opts.escapeMarkup)),n!==t&&r.append(n),i=this.opts.formatSelectionCssClass(e,r),i!==t&&r.addClass(i),this.selection.removeClass("select2-default"),this.opts.allowClear&&this.getPlaceholder()!==t&&this.container.addClass("select2-allowclear")},val:function(){var e,n=!1,i=null,r=this,o=this.data();if(0===arguments.length)return this.opts.element.val();if(e=arguments[0],arguments.length>1&&(n=arguments[1]),this.select)this.select.val(e).find("option").filter(function(){return this.selected}).each2(function(e,t){return i=r.optionToData(t),!1}),this.updateSelection(i),this.setPlaceholder(),n&&this.triggerChange({added:i,removed:o});else{if(!e&&0!==e)return void this.clear(n);if(this.opts.initSelection===t)throw new Error("cannot call val() if initSelection() is not defined");this.opts.element.val(e),this.opts.initSelection(this.opts.element,function(e){r.opts.element.val(e?r.id(e):""),r.updateSelection(e),r.setPlaceholder(),n&&r.triggerChange({added:e,removed:o})})}},clearSearch:function(){this.search.val(""),this.focusser.val("")},data:function(e){var n,i=!1;if(0===arguments.length)return n=this.selection.data("select2-data"),n==t&&(n=null),n;arguments.length>1&&(i=arguments[1]),e?(n=this.data(),this.opts.element.val(e?this.id(e):""),this.updateSelection(e),i&&this.triggerChange({added:e,removed:n})):this.clear(i)}}),H=D(j,{createContainer:function(){return e(document.createElement("div")).attr({class:"select2-container select2-container-multi"}).html(["<ul class='select2-choices'>","  <li class='select2-search-field'>","    <label for='' class='select2-offscreen'></label>","    <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input'>","  </li>","</ul>","<div class='select2-drop select2-drop-multi select2-display-none'>","   <ul class='select2-results'>","   </ul>","</div>"].join(""))},prepareOpts:function(){var t=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===t.element.get(0).tagName.toLowerCase()?t.initSelection=function(e,t){var i=[];e.find("option").filter(function(){return this.selected&&!this.disabled}).each2(function(e,t){i.push(n.optionToData(t))}),t(i)}:"data"in t&&(t.initSelection=t.initSelection||function(n,i){var r=c(n.val(),t.separator),o=[];t.query({matcher:function(n,i,s){var c=e.grep(r,function(e){return a(e,t.id(s))}).length;return c&&o.push(s),c},callback:e.isFunction(i)?function(){for(var e=[],n=0;n<r.length;n++)for(var s=r[n],c=0;c<o.length;c++){var l=o[c];if(a(s,t.id(l))){e.push(l),o.splice(c,1);break}}i(e)}:e.noop})}),t},selectChoice:function(e){var t=this.container.find(".select2-search-choice-focus");t.length&&e&&e[0]==t[0]||(t.length&&this.opts.element.trigger("choice-deselected",t),t.removeClass("select2-search-choice-focus"),e&&e.length&&(this.close(),e.addClass("select2-search-choice-focus"),this.opts.element.trigger("choice-selected",e)))},destroy:function(){e("label[for='"+this.search.attr("id")+"']").attr("for",this.opts.element.attr("id")),this.parent.destroy.apply(this,arguments),A.call(this,"searchContainer","selection")},initContainer:function(){var t,n=".select2-choices";this.searchContainer=this.container.find(".select2-search-field"),this.selection=t=this.container.find(n);var i=this;this.selection.on("click",".select2-search-choice:not(.select2-locked)",function(t){i.search[0].focus(),i.selectChoice(e(this))}),this.search.attr("id","s2id_autogen"+_()),this.search.prev().text(e("label[for='"+this.opts.element.attr("id")+"']").text()).attr("for",this.search.attr("id")),this.search.on("input paste",this.bind(function(){this.search.attr("placeholder")&&0==this.search.val().length||this.isInterfaceEnabled()&&(this.opened()||this.open())})),this.search.attr("tabindex",this.elementTabIndex),this.keydowns=0,this.search.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()){++this.keydowns;var n=t.find(".select2-search-choice-focus"),i=n.prev(".select2-search-choice:not(.select2-locked)"),r=n.next(".select2-search-choice:not(.select2-locked)"),o=g(this.search);if(n.length&&(e.which==L.LEFT||e.which==L.RIGHT||e.which==L.BACKSPACE||e.which==L.DELETE||e.which==L.ENTER)){var s=n;return e.which==L.LEFT&&i.length?s=i:e.which==L.RIGHT?s=r.length?r:null:e.which===L.BACKSPACE?this.unselect(n.first())&&(this.search.width(10),s=i.length?i:r):e.which==L.DELETE?this.unselect(n.first())&&(this.search.width(10),s=r.length?r:null):e.which==L.ENTER&&(s=null),this.selectChoice(s),m(e),void(s&&s.length||this.open())}if((e.which===L.BACKSPACE&&1==this.keydowns||e.which==L.LEFT)&&0==o.offset&&!o.length)return this.selectChoice(t.find(".select2-search-choice:not(.select2-locked)").last()),void m(e);if(this.selectChoice(null),this.opened())switch(e.which){case L.UP:case L.DOWN:return this.moveHighlight(e.which===L.UP?-1:1),void m(e);case L.ENTER:return this.selectHighlighted(),void m(e);case L.TAB:return this.selectHighlighted({noFocus:!0}),void this.close();case L.ESC:return this.cancel(e),void m(e)}if(e.which!==L.TAB&&!L.isControl(e)&&!L.isFunctionKey(e)&&e.which!==L.BACKSPACE&&e.which!==L.ESC){if(e.which===L.ENTER){if(!1===this.opts.openOnEnter)return;if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey)return}this.open(),e.which!==L.PAGE_UP&&e.which!==L.PAGE_DOWN||m(e),e.which===L.ENTER&&m(e)}}})),this.search.on("keyup",this.bind(function(e){this.keydowns=0,this.resizeSearch()})),this.search.on("blur",this.bind(function(t){this.container.removeClass("select2-container-active"),this.search.removeClass("select2-focused"),this.selectChoice(null),this.opened()||this.clearSearch(),t.stopImmediatePropagation(),this.opts.element.trigger(e.Event("select2-blur"))})),this.container.on("click",n,this.bind(function(t){this.isInterfaceEnabled()&&(e(t.target).closest(".select2-search-choice").length>0||(this.selectChoice(null),this.clearPlaceholder(),this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.open(),this.focusSearch(),t.preventDefault()))})),this.container.on("focus",n,this.bind(function(){this.isInterfaceEnabled()&&(this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"),this.clearPlaceholder())})),this.initContainerWidth(),this.opts.element.addClass("select2-offscreen"),this.clearSearch()},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.search.prop("disabled",!this.isInterfaceEnabled())},initSelection:function(){if(""===this.opts.element.val()&&""===this.opts.element.text()&&(this.updateSelection([]),this.close(),this.clearSearch()),this.select||""!==this.opts.element.val()){var e=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==t&&null!==n&&(e.updateSelection(n),e.close(),e.clearSearch())})}},clearSearch:function(){var e=this.getPlaceholder(),n=this.getMaxSearchWidth();e!==t&&0===this.getVal().length&&!1===this.search.hasClass("select2-focused")?(this.search.val(e).addClass("select2-default"),this.search.width(n>0?n:this.container.css("width"))):this.search.val("").width(10)},clearPlaceholder:function(){this.search.hasClass("select2-default")&&this.search.val("").removeClass("select2-default")},opening:function(){this.clearPlaceholder(),this.resizeSearch(),this.parent.opening.apply(this,arguments),this.focusSearch(),""===this.search.val()&&this.nextSearchTerm!=t&&(this.search.val(this.nextSearchTerm),this.search.select()),this.updateResults(!0),this.opts.shouldFocusInput(this)&&this.search.focus(),this.opts.element.trigger(e.Event("select2-open"))},close:function(){this.opened()&&this.parent.close.apply(this,arguments)},focus:function(){this.close(),this.search.focus()},isFocused:function(){return this.search.hasClass("select2-focused")},updateSelection:function(t){var n=[],i=[],r=this;e(t).each(function(){o(r.id(this),n)<0&&(n.push(r.id(this)),i.push(this))}),t=i,this.selection.find(".select2-search-choice").remove(),e(t).each(function(){r.addSelectedChoice(this)}),r.postprocessResults()},tokenize:function(){var e=this.search.val();null!=(e=this.opts.tokenizer.call(this,e,this.data(),this.bind(this.onSelect),this.opts))&&e!=t&&(this.search.val(e),e.length>0&&this.open())},onSelect:function(e,n){this.triggerSelect(e)&&""!==e.text&&(this.addSelectedChoice(e),this.opts.element.trigger({type:"selected",val:this.id(e),choice:e}),this.nextSearchTerm=this.opts.nextSearchTerm(e,this.search.val()),this.clearSearch(),this.updateResults(),!this.select&&this.opts.closeOnSelect||this.postprocessResults(e,!1,!0===this.opts.closeOnSelect),this.opts.closeOnSelect?(this.close(),this.search.width(10)):this.countSelectableResults()>0?(this.search.width(10),this.resizeSearch(),this.getMaximumSelectionSize()>0&&this.val().length>=this.getMaximumSelectionSize()?this.updateResults(!0):this.nextSearchTerm!=t&&(this.search.val(this.nextSearchTerm),this.updateResults(),this.search.select()),this.positionDropdown()):(this.close(),this.search.width(10)),this.triggerChange({added:e}),n&&n.noFocus||this.focusSearch())},cancel:function(){this.close(),this.focusSearch()},addSelectedChoice:function(n){var i,r,o=!n.locked,s=e("<li class='select2-search-choice'>    <div></div>    <a href='#' class='select2-search-choice-close' tabindex='-1'></a></li>"),a=e("<li class='select2-search-choice select2-locked'><div></div></li>"),c=o?s:a,l=this.id(n),u=this.getVal();i=this.opts.formatSelection(n,c.find("div"),this.opts.escapeMarkup),i!=t&&c.find("div").replaceWith("<div>"+i+"</div>"),r=this.opts.formatSelectionCssClass(n,c.find("div")),r!=t&&c.addClass(r),o&&c.find(".select2-search-choice-close").on("mousedown",m).on("click dblclick",this.bind(function(t){this.isInterfaceEnabled()&&(this.unselect(e(t.target)),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus"),m(t),this.close(),this.focusSearch())})).on("focus",this.bind(function(){this.isInterfaceEnabled()&&(this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"))})),c.data("select2-data",n),c.insertBefore(this.searchContainer),u.push(l),this.setVal(u)},unselect:function(t){var n,i,r=this.getVal();if(t=t.closest(".select2-search-choice"),0===t.length)throw"Invalid argument: "+t+". Must be .select2-search-choice";if(n=t.data("select2-data")){var s=e.Event("select2-removing");if(s.val=this.id(n),s.choice=n,this.opts.element.trigger(s),s.isDefaultPrevented())return!1;for(;(i=o(this.id(n),r))>=0;)r.splice(i,1),this.setVal(r),this.select&&this.postprocessResults();return t.remove(),this.opts.element.trigger({type:"select2-removed",val:this.id(n),choice:n}),this.triggerChange({removed:n}),!0}},postprocessResults:function(e,t,n){var i=this.getVal(),r=this.results.find(".select2-result"),s=this.results.find(".select2-result-with-children"),a=this;r.each2(function(e,t){o(a.id(t.data("select2-data")),i)>=0&&(t.addClass("select2-selected"),t.find(".select2-result-selectable").addClass("select2-selected"))}),s.each2(function(e,t){t.is(".select2-result-selectable")||0!==t.find(".select2-result-selectable:not(.select2-selected)").length||t.addClass("select2-selected")}),-1==this.highlight()&&!1!==n&&a.highlight(0),!this.opts.createSearchChoice&&!r.filter(".select2-result:not(.select2-selected)").length>0&&(!e||e&&!e.more&&0===this.results.find(".select2-no-results").length)&&E(a.opts.formatNoMatches,"formatNoMatches")&&this.results.append("<li class='select2-no-results'>"+k(a.opts.formatNoMatches,a.opts.element,a.search.val())+"</li>")},getMaxSearchWidth:function(){return this.selection.width()-l(this.search)},resizeSearch:function(){var e,t,n,i,r,o=l(this.search);e=y(this.search)+10,t=this.search.offset().left,n=this.selection.width(),i=this.selection.offset().left,r=n-(t-i)-o,r<e&&(r=n-o),r<40&&(r=n-o),r<=0&&(r=e),this.search.width(Math.floor(r))},getVal:function(){var e;return this.select?(e=this.select.val(),null===e?[]:e):(e=this.opts.element.val(),c(e,this.opts.separator))},setVal:function(t){var n;this.select?this.select.val(t):(n=[],e(t).each(function(){o(this,n)<0&&n.push(this)}),this.opts.element.val(0===n.length?"":n.join(this.opts.separator)))},buildChangeDetails:function(e,t){for(var t=t.slice(0),e=e.slice(0),n=0;n<t.length;n++)for(var i=0;i<e.length;i++)a(this.opts.id(t[n]),this.opts.id(e[i]))&&(t.splice(n,1),n>0&&n--,e.splice(i,1),i--);return{added:t,removed:e}},val:function(n,i){var r,o=this;if(0===arguments.length)return this.getVal();if(r=this.data(),r.length||(r=[]),!n&&0!==n)return this.opts.element.val(""),this.updateSelection([]),this.clearSearch(),void(i&&this.triggerChange({added:this.data(),removed:r}));if(this.setVal(n),this.select)this.opts.initSelection(this.select,this.bind(this.updateSelection)),i&&this.triggerChange(this.buildChangeDetails(r,this.data()));else{if(this.opts.initSelection===t)throw new Error("val() cannot be called if initSelection() is not defined");this.opts.initSelection(this.opts.element,function(t){var n=e.map(t,o.id);o.setVal(n),o.updateSelection(t),o.clearSearch(),i&&o.triggerChange(o.buildChangeDetails(r,o.data()))})}this.clearSearch()},onSortStart:function(){if(this.select)throw new Error("Sorting of elements is not supported when attached to <select>. Attach to <input type='hidden'/> instead.");this.search.width(0),this.searchContainer.hide()},onSortEnd:function(){var t=[],n=this;this.searchContainer.show(),this.searchContainer.appendTo(this.searchContainer.parent()),this.resizeSearch(),this.selection.find(".select2-search-choice").each(function(){t.push(n.opts.id(e(this).data("select2-data")))}),this.setVal(t),this.triggerChange()},data:function(t,n){var i,r,o=this;if(0===arguments.length)return this.selection.children(".select2-search-choice").map(function(){return e(this).data("select2-data")}).get();r=this.data(),t||(t=[]),i=e.map(t,function(e){return o.opts.id(e)}),this.setVal(i),this.updateSelection(t),this.clearSearch(),n&&this.triggerChange(this.buildChangeDetails(r,this.data()))}}),e.fn.select2=function(){var n,i,s,a,c,l=Array.prototype.slice.call(arguments,0),u=["val","destroy","opened","open","close","focus","isFocused","container","dropdown","onSortStart","onSortEnd","enable","disable","readonly","positionDropdown","data","search"],h=["opened","isFocused","container","dropdown"],d=["val","data"],f={search:"externalSearch"};return this.each(function(){if(0===l.length||"object"===r()(l[0]))n=0===l.length?{}:e.extend({},l[0]),n.element=e(this),"select"===n.element.get(0).tagName.toLowerCase()?c=n.element.prop("multiple"):(c=n.multiple||!1,"tags"in n&&(n.multiple=c=!0)),i=c?new window.Select2.class.multi:new window.Select2.class.single,i.init(n);else{if("string"!=typeof l[0])throw"Invalid arguments to select2 plugin: "+l;if(o(l[0],u)<0)throw"Unknown method: "+l[0];if(a=t,(i=e(this).data("select2"))===t)return;if(s=l[0],"container"===s?a=i.container:"dropdown"===s?a=i.dropdown:(f[s]&&(s=f[s]),a=i[s].apply(i,l.slice(1))),o(l[0],h)>=0||o(l[0],d)>=0&&1==l.length)return!1}}),a===t?this:a},e.fn.select2.defaults={width:"copy",loadMorePadding:0,closeOnSelect:!0,openOnEnter:!0,containerCss:{},dropdownCss:{},containerCssClass:"",dropdownCssClass:"",formatResult:function(e,t,n,i){var r=[];return x(e.text,n.term,r,i),r.join("")},formatSelection:function(e,n,i){return e?i(e.text):t},sortResults:function(e,t,n){return e},formatResultCssClass:function(e){return e.css},formatSelectionCssClass:function(e,n){return t},minimumResultsForSearch:0,minimumInputLength:0,maximumInputLength:null,maximumSelectionSize:0,id:function(e){return e==t?null:e.id},matcher:function(e,t){return i(""+t).toUpperCase().indexOf(i(""+e).toUpperCase())>=0},separator:",",tokenSeparators:[],tokenizer:O,escapeMarkup:w,blurOnChange:!1,selectOnBlur:!1,adaptContainerCssClass:function(e){return e},adaptDropdownCssClass:function(e){return null},nextSearchTerm:function(e,n){return t},searchInputPlaceholder:"",createSearchChoicePosition:"top",shouldFocusInput:function(e){return!(("ontouchstart"in window||navigator.msMaxTouchPoints>0)&&e.opts.minimumResultsForSearch<0)}},e.fn.select2.locales=[],e.fn.select2.locales.en={formatMatches:function(e){return 1===e?"One result is available, press enter to select it.":e+" results are available, use up and down arrow keys to navigate."},formatNoMatches:function(){return"No matches found"},formatAjaxError:function(e,t,n){return"Loading failed"},formatInputTooShort:function(e,t){var n=t-e.length;return"Please enter "+n+" or more character"+(1==n?"":"s")},formatInputTooLong:function(e,t){var n=e.length-t;return"Please delete "+n+" character"+(1==n?"":"s")},formatSelectionTooBig:function(e){return"You can only select "+e+" item"+(1==e?"":"s")},formatLoadMore:function(e){return"Loading more results…"},formatSearching:function(){return"Searching…"}},e.extend(e.fn.select2.defaults,e.fn.select2.locales.en),e.fn.select2.ajaxDefaults={transport:e.ajax,params:{type:"GET",cache:!1,dataType:"json"}},window.Select2={query:{ajax:C,local:S,tags:T},util:{debounce:d,markMatch:x,escapeMarkup:w,stripDiacritics:i},class:{abstract:j,single:P,multi:H}}}}(o)},39:function(e,t,n){var i=n(6);e.exports=function(e,t){if(!i(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!i(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!i(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},4:function(e,t,n){var i=n(7),r=n(64),o=n(39),s=Object.defineProperty;t.f=n(5)?Object.defineProperty:function(e,t,n){if(i(e),t=o(t,!0),i(n),r)try{return s(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},40:function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},41:function(e,t){var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:n)(e)}},42:function(e,t,n){var i=n(43)("keys"),r=n(25);e.exports=function(e){return i[e]||(i[e]=r(e))}},43:function(e,t,n){var i=n(2),r=n(3),o=r["__core-js_shared__"]||(r["__core-js_shared__"]={});(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:i.version,mode:n(23)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},44:function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},45:function(e,t,n){t.f=n(1)},46:function(e,t,n){var i=n(3),r=n(2),o=n(23),s=n(45),a=n(4).f;e.exports=function(e){var t=r.Symbol||(r.Symbol=o?{}:i.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},47:function(e,t){t.f={}.propertyIsEnumerable},48:function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},49:function(e,t,n){var i=n(41),r=Math.min;e.exports=function(e){return e>0?r(i(e),9007199254740991):0}},5:function(e,t,n){e.exports=!n(17)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},54:function(e,t,n){var i=n(6),r=n(3).document,o=i(r)&&i(r.createElement);e.exports=function(e){return o?r.createElement(e):{}}},55:function(e,t,n){"use strict";var i=n(23),r=n(8),o=n(66),s=n(10),a=n(14),c=n(115),l=n(26),u=n(117),h=n(1)("iterator"),d=!([].keys&&"next"in[].keys()),f=function(){return this};e.exports=function(e,t,n,p,g,m,v){c(n,t,p);var y,b,x,w=function(e){if(!d&&e in E)return E[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},C=t+" Iterator",S="values"==g,T=!1,E=e.prototype,k=E[h]||E["@@iterator"]||g&&E[g],N=k||w(g),O=g?S?w("entries"):N:void 0,A="Array"==t?E.entries||k:k;if(A&&(x=u(A.call(new e)))!==Object.prototype&&x.next&&(l(x,C,!0),i||"function"==typeof x[h]||s(x,h,f)),S&&k&&"values"!==k.name&&(T=!0,N=function(){return k.call(this)}),i&&!v||!d&&!T&&E[h]||s(E,h,N),a[t]=N,a[C]=f,g)if(y={values:S?N:w("values"),keys:m?N:w("keys"),entries:O},v)for(b in y)b in E||o(E,b,y[b]);else r(r.P+r.F*(d||T),t,y);return y}},56:function(e,t,n){var i=n(7),r=n(116),o=n(44),s=n(42)("IE_PROTO"),a=function(){},c=function(){var e,t=n(54)("iframe"),i=o.length;for(t.style.display="none",n(91).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),c=e.F;i--;)delete c.prototype[o[i]];return c()};e.exports=Object.create||function(e,t){var n;return null!==e?(a.prototype=i(e),n=new a,a.prototype=null,n[s]=e):n=c(),void 0===t?n:r(n,t)}},6:function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},64:function(e,t,n){e.exports=!n(5)&&!n(17)(function(){return 7!=Object.defineProperty(n(54)("div"),"a",{get:function(){return 7}}).a})},65:function(e,t,n){var i=n(9),r=n(13),o=n(112)(!1),s=n(42)("IE_PROTO");e.exports=function(e,t){var n,a=r(e),c=0,l=[];for(n in a)n!=s&&i(a,n)&&l.push(n);for(;t.length>c;)i(a,n=t[c++])&&(~o(l,n)||l.push(n));return l}},66:function(e,t,n){e.exports=n(10)},67:function(e,t){t.f=Object.getOwnPropertySymbols},68:function(e,t,n){var i=n(65),r=n(44).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return i(e,r)}},69:function(e,t){},7:function(e,t,n){var i=n(6);e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},70:function(e,t,n){var i=n(25)("meta"),r=n(6),o=n(9),s=n(4).f,a=0,c=Object.isExtensible||function(){return!0},l=!n(17)(function(){return c(Object.preventExtensions({}))}),u=function(e){s(e,i,{value:{i:"O"+ ++a,w:{}}})},h=function(e,t){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,i)){if(!c(e))return"F";if(!t)return"E";u(e)}return e[i].i},d=function(e,t){if(!o(e,i)){if(!c(e))return!0;if(!t)return!1;u(e)}return e[i].w},f=function(e){return l&&p.NEED&&c(e)&&!o(e,i)&&u(e),e},p=e.exports={KEY:i,NEED:!1,fastKey:h,getWeak:d,onFreeze:f}},8:function(e,t,n){var i=n(3),r=n(2),o=n(19),s=n(10),a=n(9),c=function(e,t,n){var l,u,h,d=e&c.F,f=e&c.G,p=e&c.S,g=e&c.P,m=e&c.B,v=e&c.W,y=f?r:r[t]||(r[t]={}),b=y.prototype,x=f?i:p?i[t]:(i[t]||{}).prototype;f&&(n=t);for(l in n)(u=!d&&x&&void 0!==x[l])&&a(y,l)||(h=u?x[l]:n[l],y[l]=f&&"function"!=typeof x[l]?n[l]:m&&u?o(h,i):v&&x[l]==h?function(e){var t=function(t,n,i){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,i)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(h):g&&"function"==typeof h?o(Function.call,h):h,g&&((y.virtual||(y.virtual={}))[l]=h,e&c.R&&b&&!b[l]&&s(b,l,h)))};c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},9:function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},90:function(e,t,n){var i=n(28);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==i(e)?e.split(""):Object(e)}},91:function(e,t,n){var i=n(3).document;e.exports=i&&i.documentElement},92:function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},93:function(e,t,n){var i=n(28);e.exports=Array.isArray||function(e){return"Array"==i(e)}},94:function(e,t){(function(t){e.exports=t}).call(t,{})},95:function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var r=n(120),o=i(r),s=n(122),a=i(s),c="function"==typeof a.default&&"symbol"==typeof o.default?function(e){return typeof e}:function(e){return e&&"function"==typeof a.default&&e.constructor===a.default&&e!==a.default.prototype?"symbol":typeof e};t.default="function"==typeof a.default&&"symbol"===c(o.default)?function(e){return void 0===e?"undefined":c(e)}:function(e){return e&&"function"==typeof a.default&&e.constructor===a.default&&e!==a.default.prototype?"symbol":void 0===e?"undefined":c(e)}}});