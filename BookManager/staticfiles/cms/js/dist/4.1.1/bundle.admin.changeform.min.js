cmsWebpackJsonp([2],{196:function(n,a,e){var i=e(0);n.exports=function(n,a){if(a.length){var e=!1;""===a.val().trim()&&(e=!0),window.unihandecode&&(window.UNIHANDECODER=window.unihandecode.Unihan(a.data("decoder"))),n.on("keyup keypress",function(){var i=n.val();window.UNIHANDECODER&&(i=window.UNIHANDECODER.decode(i)),!1===e&&""===a.val()&&(e=!0);var t=URLify(i,64);e&&a.val(t)}),n.trigger("keyup"),a.add(n).bind("change",function(){i(this).data("changed",!0)})}}},346:function(n,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var i=(e(71),e(79),e(16)),t=e(0),d=e.n(t),o=e(11),r=e.n(o),c=e(196),u=e.n(c);d()(function(){var n=d()("#id_title"),a=d()("#id_slug");u()(n,a),d()("div.loading").each(function(){d()(this).load(d()(this).attr("rel"))}),d()('input[type="hidden"]').each(function(){d()(this).parent(".form-row").hide()}),window.CMS.API.changeLanguage=function(e){var i=!0,t=!1;if(a.length&&(a.data("changed")||n.data("changed"))&&(t=!0),t){var d=gettext("Are you sure you want to change tabs without saving the page first?");i=confirm(d)}i&&(window.location.href=e)}});var h={$:d.a,Class:r.a,API:{Helpers:i.c},KEYS:i.d};window.CMS=h}},[346]);