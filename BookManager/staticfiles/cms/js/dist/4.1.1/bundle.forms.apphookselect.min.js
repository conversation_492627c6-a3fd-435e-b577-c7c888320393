!function(e){function n(t){if(a[t])return a[t].exports;var o=a[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,n),o.l=!0,o.exports}var t=window.cmsWebpackJsonp;window.cmsWebpackJsonp=function(n,a,r){for(var i,c,p=0,l=[];p<n.length;p++)c=n[p],o[c]&&l.push(o[c][0]),o[c]=0;for(i in a)Object.prototype.hasOwnProperty.call(a,i)&&(e[i]=a[i]);for(t&&t(n,a,r);l.length;)l.shift()()};var a={},o={8:0};n.e=function(e){function t(){c.onerror=c.onload=null,clearTimeout(p);var n=o[e];0!==n&&(n&&n[1](new Error("Loading chunk "+e+" failed.")),o[e]=void 0)}var a=o[e];if(0===a)return new Promise(function(e){e()});if(a)return a[2];var r=new Promise(function(n,t){a=o[e]=[n,t]});a[2]=r;var i=document.getElementsByTagName("head")[0],c=document.createElement("script");c.type="text/javascript",c.charset="utf-8",c.async=!0,c.timeout=12e4,n.nc&&c.setAttribute("nonce",n.nc),c.src=n.p+"bundle."+({0:"admin.widget"}[e]||e)+".min.js";var p=setTimeout(t,12e4);return c.onerror=c.onload=t,i.appendChild(c),r},n.m=e,n.c=a,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:a})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},n.p="",n.oe=function(e){throw e},n(n.s=350)}({350:function(e,n,t){t.p=t(53)("bundle.forms.apphookselect"),t.e(0).then(function(e){var n=t(0),a=window.apphooks_configuration||{};n(function(){var e=n("#application_urls, #id_application_urls"),t=e.find("option:selected"),o=n(".form-row.application_namespace, .form-row.field-application_namespace"),r=o.find("#application_namespace, #id_application_namespace"),i=n(".form-row.application_configs, .form-row.field-application_configs"),c=i.find("#application_configs, #id_application_configs"),p=i.find("#add_application_configs"),l=r.val();e.setupNamespaces=function(){var e=n(this).find("option:selected");if(n(c).length>0&&a[e.val()]){c.html("");for(var t=0;t<a[e.val()].length;t++){var r="";a[e.val()][t][0]===window.apphooks_configuration_value&&(r='selected="selected"'),c.append("<option "+r+' value="'+a[e.val()][t][0]+'">'+a[e.val()][t][1]+"</option>")}p.attr("href",window.apphooks_configuration_url[e.val()]+(window.showRelatedObjectPopup?"?_popup=1":"")),i.removeClass("hidden"),o.addClass("hidden")}else i.addClass("hidden"),e.data("namespace")?o.removeClass("hidden"):o.addClass("hidden")},e.setupNamespaces(),e.on("change",function(){var a=n(this),o=a.find("option:selected");e.setupNamespaces(),a.val()||(r.val(""),r.removeAttr("value")),t.val()===o.val()?l&&r.val(l):o.data("namespace")?r.val(o.data("namespace")):(r.val(""),r.removeAttr("value"))})})}.bind(null,t)).catch(t.oe)},53:function(e,n){var t=function(e){var n=new RegExp(e+".*$","gi");if(document.currentScript)return document.currentScript.src.replace(n,"");var t,a;return t=document.getElementsByTagName("script"),a=function(e,t){for(var a,o,r=0;r<e.length;r++)if(o=null,void 0!==e[r].getAttribute.length&&(o=e[r].getAttribute(t,2)),o&&(a=o,a=a.split("?")[0].split("/").pop(),a.match(n)))return o}(t,"src"),a?a.replace(n,""):""};e.exports=t}});