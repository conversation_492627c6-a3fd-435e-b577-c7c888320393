// #############################################################################
// ZINDEX

// handle z-index more easily
// DOCS: http://www.sitepoint.com/better-solution-managing-z-index-sass/
// http://sassmeister.com/gist/341c052928c956c1a751
// use case: header { z-index: z("modal", "header"); }

@function map-has-nested-keys($map, $keys...) {
    @each $key in $keys {
        @if not map-has-key($map, $key) {
            @return false;
        }
        $map: map-get($map, $key);
    }

    @return true;
}
@function map-deep-get($map, $keys...) {
    @each $key in $keys {
        $map: map-get($map, $key);
    }

    @return $map;
}
@function z($layers...) {
    @if not map-has-nested-keys($z-layers, $layers...) {
        @warn "No layer found for `#{inspect($layers)}` in $z-layers map. Property omitted.";
    }

    @return map-deep-get($z-layers, $layers...);
}
