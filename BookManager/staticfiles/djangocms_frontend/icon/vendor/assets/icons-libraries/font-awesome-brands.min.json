{"prefix": "fa-brands fa-", "icon-style": "fa-brands", "list-icon": "fa-brands fa-font-awesome", "icons": ["steam-symbol", "pix", "korvue", "odnoklassniki-square", "zhihu", "intercom", "google-pay", "wodu", "git-square", "nutritionix", "wordpress-simple", "pushed", "teamspeak", "canadian-maple-leaf", "ember", "strava", "creative-commons-sampling-plus", "gulp", "buy-n-large", "wolf-pack-battalion", "airbnb", "mastodon", "j<PERSON><PERSON>", "discourse", "sitrox", "critical-role", "viacoin", "pinterest-square", "flipboard", "mixcloud", "amilia", "medium", "reacteurope", "quora", "vaadin", "tumblr", "usb", "medrt", "slack", "edge-legacy", "node-js", "old-republic", "telegram", "internet-explorer", "stumbleupon-circle", "dribbble", "btc", "yammer", "npm", "buffer", "pinterest", "rockrms", "twitter", "jenkins", "suse", "replyd", "behance-square", "firefox-browser", "skyatlas", "youtube", "alipay", "php", "github", "uber", "cc-amex", "yandex-international", "sketch", "dev", "git", "ideal", "js", "hornbill", "free-code-camp", "bots", "android", "python", "github-square", "themeco", "black-tie", "leanpub", "connectdevelop", "weebly", "grunt", "perbyte", "get-pocket", "creative-commons-nc-jp", "trello", "goodreads-g", "cpanel", "deviantart", "kaggle", "staylinked", "css3", "rebel", "supple", "pied-piper-square", "wix", "rust", "fantasy-flight-games", "snapchat", "cc-jcb", "waze", "fort-awesome", "apper", "pinterest-p", "ns8", "medapps", "octopus-deploy", "salesforce", "buromobelexperte", "wirsindhandwerk", "sass", "sellsy", "html5", "readme", "yandex", "kickstarter-k", "pied-piper-hat", "gg-circle", "cc-diners-club", "microblog", "d-and-d", "gitter", "bluetooth", "docker", "jira", "raspberry-pi", "<PERSON><PERSON>", "imdb", "bitbucket", "simplybuilt", "servicestack", "google-drive", "line", "viadeo", "google-play", "slideshare", "whatsapp", "autoprefixer", "trade-federation", "earlybirds", "ussunnah", "phabricator", "fort-awesome-alt", "uikit", "jedi-order", "gripfire", "facebook", "cmplid", "instagram", "dropbox", "cc-amazon-pay", "creative-commons-remix", "figma", "js-square", "uniregistry", "mendeley", "vimeo", "mixer", "ravelry", "twitch", "deploydog", "hubspot", "linkedin", "glide", "linux", "renren", "dribbble-square", "tiktok", "steam-square", "searchengin", "xbox", "resolving", "superpowers", "palfed", "creative-commons-nd", "elementor", "stack-exchange", "42-group", "dashcube", "cotton-bureau", "erlang", "bilibili", "think-peaks", "audible", "facebook-messenger", "etsy", "maxcdn", "symfony", "tencent-weibo", "digg", "soundcloud", "viber", "flickr", "blogger", "cuttlefish", "itunes", "aviato", "fly", "optin-monster", "spotify", "magento", "megaport", "usps", "cloudversify", "wpforms", "bandcamp", "schlix", "<PERSON><PERSON>ni", "forumbee", "cloudscale", "fulcrum", "periscope", "d-and-d-beyond", "google-wallet", "osi", "first-order-alt", "mandalorian", "facebook-f", "bimobject", "gofore", "ello", "avianex", "patreon", "creative-commons-nc-eu", "speaker-deck", "ethereum", "odnoklassniki", "bootstrap", "pied-piper-pp", "affiliatetheme", "dailymotion", "yahoo", "reddit-alien", "typo3", "gitlab", "paypal", "vine", "opencart", "blogger-b", "less", "quinscape", "playstation", "creative-commons-pd", "blackberry", "xing", "dhl", "gg", "<PERSON>uzz", "stripe", "draft2digital", "ubuntu", "galactic-senate", "umbraco", "itch-io", "opera", "keycdn", "bitcoin", "xing-square", "creative-commons-share", "cc-stripe", "squarespace", "cc-paypal", "react", "<PERSON><PERSON><PERSON>", "page4", "<PERSON><PERSON><PERSON>", "sith", "jsfiddle", "goodreads", "linode", "firefox", "product-hunt", "wordpress", "pied-piper", "<PERSON><PERSON><PERSON>", "envira", "empire", "y-combinator", "angrycreative", "speakap", "angular", "swift", "researchgate", "hackerrank", "neos", "shopify", "phoenix-framework", "fedex", "stumbleupon", "instagram-square", "amazon-pay", "padlet", "apple-pay", "keybase", "gitkraken", "hive", "apple", "reddit-square", "gratipay", "font-awesome", "asymmetrik", "500px", "yarn", "unsplash", "amazon", "ebay", "accessible-icon", "dochub", "mdb", "confluence", "wpbeginner", "cc-discover", "app-store-ios", "chrome", "discord", "reddit", "behance", "hips", "creative-commons-zero", "facebook-square", "sticker-mule", "bluetooth-b", "hotjar", "laravel", "stripe-s", "fedora", "joget", "skype", "whatsapp-square", "galactic-republic", "angellist", "wpressr", "wikipedia-w", "firstdraft", "uncharted", "weibo", "grav", "kickstarter", "golang", "itunes-note", "cc-mastercard", "app-store", "fonticons-fi", "ioxhost", "accusoft", "v<PERSON><PERSON><PERSON>", "freebsd", "delicious", "r-project", "youtube-square", "sellcast", "expeditedssl", "instalod", "openid", "scribd", "cc-apple-pay", "steam", "mix", "node", "tumblr-square", "codiepie", "pied-piper-alt", "vimeo-square", "cloudsmith", "adn", "centos", "meetup", "wizards-of-the-coast", "windows", "rev", "lyft", "git-alt", "codepen", "twitter-square", "shirtsinbulk", "weixin", "fonticons", "watchman-monitoring", "creative-commons", "viadeo-square", "adversal", "creative-commons-sampling", "hacker-news", "evernote", "chromecast", "nimblr", "digital-ocean", "google-plus-square", "linkedin-in", "atlassian", "square-font-awesome-stroke", "google", "safari", "creative-commons-sa", "red-river", "algolia", "pagelines", "phoenix-squadron", "github-alt", "stack-overflow", "foursquare", "diaspora", "google-plus", "sourcetree", "markdown", "artstation", "google-plus-g", "napster", "edge", "hacker-news-square", "the-red-yeti", "battle-net", "sistrix", "lastfm-square", "deskpro", "square-font-awesome", "contao", "vimeo-v", "css3-alt", "snapchat-square", "mailchimp", "untappd", "vk", "rocketchat", "whmcs", "unity", "creative-commons-by", "hire-a-helper", "drupal", "glide-g", "centercode", "creative-commons-pd-alt", "invision", "java", "orcid", "qq", "microsoft", "vnv", "guilded", "modx", "first-order", "buysellads", "stackpath", "bity", "dyalog", "wpexplorer", "ups", "cloudflare", "yoast", "redhat", "aws", "creative-commons-nc", "shopware", "lastfm", "cc-visa", "yelp", "hooli", "monero"]}