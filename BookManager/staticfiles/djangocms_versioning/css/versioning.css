ins.cms-diff {
    background: #cdffd8 !important;
    outline:1px solid green !important;
}
del.cms-diff {
    background: #ffdce0 !important;
    outline: 1px solid red !important;
}
[data-diff-node="ins"] {
    background: #cdffd8 !important;
    outline:1px solid green !important;
}
[data-diff-node="ins"] ins.cms-diff {
    outline: none !important;
}
[data-diff-node="del"] {
    background: #ffdce0 !important;
    outline: 1px solid red !important;
}
[data-diff-node="del"] del.cms-diff {
    outline: none !important;
}
del.cms-diff img {
    outline: 5px solid red;
    outline-offset: -5px;
    opacity: 0.6;
}
ins.cms-diff img {
    outline: 5px solid green;
    outline-offset: -5px;
    opacity: 0.6;
}

.cms-versioning-diff-frame {
    position: fixed;
    top: 46px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    border: none;
    width: 100%;
    height: 100%;
    height: calc(100vh - 46px);
}

.cms-versioning-controls {
    position: fixed;
    top: 0;
    z-index: 99999999999999;
    display: flex;
    flex-direction: row;
    right: 0;
    left: 0;
    justify-content: space-between;
    align-items: center;
    padding-left: 15px !important;
    padding-right: 15px !important;
    height: 46px !important;
}

.cms-versioning-controls .cms-btn,
.cms-versioning-controls .cms-btn:hover {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    padding: 0 12px;
}

.cms-versioning-control-close {
    height: 46px !important;
    width: 46px !important;
    display: flex;
    align-items: stretch;
    justify-content: stretch;
    position: fixed;
    top: 0 !important;
    right: 0 !important;
}

.cms-versioning-control-close a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    border-left: 1px solid #ddd !important;

}

.cms-versioning-controls .cms-toolbar-item-buttons {
    margin-left: auto !important;
    margin-top: auto !important;
    margin-bottom: auto !important;
}

@media (max-width: 440px) {
    .cms-versioning-controls {
        left: 0;
    }
    .cms-versioning-controls .cms-toolbar-item-buttons {
        margin-left: 15px !important;
    }
}

.cms-versioning-controls .cms-btn-group {
    display: flex;
    flex-direction: row;
}
.cms-versioning-controls .cms-btn {
    flex-shrink: 0 !important;
    box-sizing: content-box !important;
    width: 50px !important;
    white-space: nowrap !important;
    text-align: center !important;
}
.cms-versioning-controls .cms-btn + .cms-btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-left: -1px;
}
.cms-versioning-controls .cms-btn .cms-icon:before {
    position: relative;
    top: 3px;
}
.cms-versioning-controls .cms-btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.cms-versioning-overflow {
    overflow: hidden !important;
}

.cms-loading-bar {
    background: #0bf;

    position: fixed;
    z-index: 99999999;
    top: 0;
    left: 0;

    width: 100%;
    height: 3px;
}

.cms-loading-peg {
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px #29d, 0 0 5px #0bf;
    opacity: 1.0;
    transform: rotate(3deg) translate(0px, -4px);
}

.cms-versioning-title {
    margin-right: 10px;
    margin-left: 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    min-width: 0;
    flex-shrink: 1;
}

.cms-select {
    display: inline-block;
    width: auto;
    flex-shrink: 0;
    max-width: 300px;
    height: 32px;
    margin-right: 10px;
    padding: 0.375rem 1.75rem 0.375rem 0.75rem;
    line-height: 1.5;
    color: #495057;
    vertical-align: middle;
    background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right 0.75rem center/8px 10px;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
    -webkit-appearance: none;
        -moz-appearance: none;
            appearance: none;
}
.cms-select:focus {
    border-color: #80bdff;
    outline: 0;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(128, 189, 255, 0.5);
            box-shadow: 0 0 0 0.2rem rgba(128, 189, 255, 0.5);
}
.cms-select:focus::-ms-value {
    color: #495057;
    background-color: #fff;
}
.cms-select[multiple], .cms-select[size]:not([size="1"]) {
    height: auto;
    padding-right: 0.75rem;
    background-image: none;
}
.cms-select:disabled {
    color: #6c757d;
    background-color: #e9ecef;
}
.cms-select::-ms-expand {
    opacity: 0;
}
