/*! For license information please see bundle.adminstyle.min.js.LICENSE.txt */
(()=>{var e,t,n,r,i,o,a,s,u,l={732:function(e,t){var n,r,i;r="undefined"!=typeof window?window:this,i=function(r,i){var o=[],a=o.slice,s=o.concat,u=o.push,l=o.indexOf,c={},d=c.toString,f=c.hasOwnProperty,p={},h="1.11.3",m=function(e,t){return new m.fn.init(e,t)},g=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,v=/^-ms-/,y=/-([\da-z])/gi,x=function(e,t){return t.toUpperCase()};function b(e){var t="length"in e&&e.length,n=m.type(e);return"function"!==n&&!m.isWindow(e)&&(!(1!==e.nodeType||!t)||"array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}m.fn=m.prototype={jquery:h,constructor:m,selector:"",length:0,toArray:function(){return a.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:a.call(this)},pushStack:function(e){var t=m.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return m.each(this,e,t)},map:function(e){return this.pushStack(m.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:u,sort:o.sort,splice:o.splice},m.extend=m.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||m.isFunction(a)||(a={}),s===u&&(a=this,s--);u>s;s++)if(null!=(i=arguments[s]))for(r in i)e=a[r],a!==(n=i[r])&&(l&&n&&(m.isPlainObject(n)||(t=m.isArray(n)))?(t?(t=!1,o=e&&m.isArray(e)?e:[]):o=e&&m.isPlainObject(e)?e:{},a[r]=m.extend(l,o,n)):void 0!==n&&(a[r]=n));return a},m.extend({expando:"jQuery"+(h+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===m.type(e)},isArray:Array.isArray||function(e){return"array"===m.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!m.isArray(e)&&e-parseFloat(e)+1>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==m.type(e)||e.nodeType||m.isWindow(e))return!1;try{if(e.constructor&&!f.call(e,"constructor")&&!f.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(p.ownLast)for(t in e)return f.call(e,t);for(t in e);return void 0===t||f.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?c[d.call(e)]||"object":typeof e},globalEval:function(e){e&&m.trim(e)&&(r.execScript||function(e){r.eval.call(r,e)})(e)},camelCase:function(e){return e.replace(v,"ms-").replace(y,x)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r=0,i=e.length,o=b(e);if(n){if(o)for(;i>r&&!1!==t.apply(e[r],n);r++);else for(r in e)if(!1===t.apply(e[r],n))break}else if(o)for(;i>r&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:function(e){return null==e?"":(e+"").replace(g,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(b(Object(e))?m.merge(n,"string"==typeof e?[e]:e):u.call(n,e)),n},inArray:function(e,t,n){var r;if(t){if(l)return l.call(t,e,n);for(r=t.length,n=n?0>n?Math.max(0,r+n):n:0;r>n;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;n>r;)e[i++]=t[r++];if(n!=n)for(;void 0!==t[r];)e[i++]=t[r++];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;o>i;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i=0,o=e.length,a=[];if(b(e))for(;o>i;i++)null!=(r=t(e[i],i,n))&&a.push(r);else for(i in e)null!=(r=t(e[i],i,n))&&a.push(r);return s.apply([],a)},guid:1,proxy:function(e,t){var n,r,i;return"string"==typeof t&&(i=e[t],t=e,e=i),m.isFunction(e)?(n=a.call(arguments,2),r=function(){return e.apply(t||this,n.concat(a.call(arguments)))},r.guid=e.guid=e.guid||m.guid++,r):void 0},now:function(){return+new Date},support:p}),m.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),(function(e,t){c["[object "+t+"]"]=t.toLowerCase()}));var w=function(e){var t,n,r,i,o,a,s,u,l,c,d,f,p,h,m,g,v,y,x,b="sizzle"+1*new Date,w=e.document,T=0,C=0,E=ae(),N=ae(),k=ae(),S=function(e,t){return e===t&&(d=!0),0},j=1<<31,D={}.hasOwnProperty,A=[],L=A.pop,_=A.push,H=A.push,M=A.slice,q=function(e,t){for(var n=0,r=e.length;r>n;n++)if(e[n]===t)return n;return-1},O="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",B="[\\x20\\t\\r\\n\\f]",F="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",R=F.replace("w","w#"),P="\\["+B+"*("+F+")(?:"+B+"*([*^$|!~]?=)"+B+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+R+"))|)"+B+"*\\]",W=":("+F+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+P+")*)|.*)\\)|)",I=new RegExp(B+"+","g"),$=new RegExp("^"+B+"+|((?:^|[^\\\\])(?:\\\\.)*)"+B+"+$","g"),z=new RegExp("^"+B+"*,"+B+"*"),X=new RegExp("^"+B+"*([>+~]|"+B+")"+B+"*"),U=new RegExp("="+B+"*([^\\]'\"]*?)"+B+"*\\]","g"),J=new RegExp(W),Q=new RegExp("^"+R+"$"),V={ID:new RegExp("^#("+F+")"),CLASS:new RegExp("^\\.("+F+")"),TAG:new RegExp("^("+F.replace("w","w*")+")"),ATTR:new RegExp("^"+P),PSEUDO:new RegExp("^"+W),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+B+"*(even|odd|(([+-]|)(\\d*)n|)"+B+"*(?:([+-]|)"+B+"*(\\d+)|))"+B+"*\\)|)","i"),bool:new RegExp("^(?:"+O+")$","i"),needsContext:new RegExp("^"+B+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+B+"*((?:-\\d)?\\d*)"+B+"*\\)|)(?=[^-]|$)","i")},Y=/^(?:input|select|textarea|button)$/i,G=/^h\d$/i,K=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=/'|\\/g,ne=new RegExp("\\\\([\\da-f]{1,6}"+B+"?|("+B+")|.)","ig"),re=function(e,t,n){var r="0x"+t-65536;return r!=r||n?t:0>r?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},ie=function(){f()};try{H.apply(A=M.call(w.childNodes),w.childNodes),A[w.childNodes.length].nodeType}catch(e){H={apply:A.length?function(e,t){_.apply(e,M.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function oe(e,t,r,i){var o,s,l,c,d,h,v,y,T,C;if((t?t.ownerDocument||t:w)!==p&&f(t),r=r||[],c=(t=t||p).nodeType,"string"!=typeof e||!e||1!==c&&9!==c&&11!==c)return r;if(!i&&m){if(11!==c&&(o=Z.exec(e)))if(l=o[1]){if(9===c){if(!(s=t.getElementById(l))||!s.parentNode)return r;if(s.id===l)return r.push(s),r}else if(t.ownerDocument&&(s=t.ownerDocument.getElementById(l))&&x(t,s)&&s.id===l)return r.push(s),r}else{if(o[2])return H.apply(r,t.getElementsByTagName(e)),r;if((l=o[3])&&n.getElementsByClassName)return H.apply(r,t.getElementsByClassName(l)),r}if(n.qsa&&(!g||!g.test(e))){if(y=v=b,T=t,C=1!==c&&e,1===c&&"object"!==t.nodeName.toLowerCase()){for(h=a(e),(v=t.getAttribute("id"))?y=v.replace(te,"\\$&"):t.setAttribute("id",y),y="[id='"+y+"'] ",d=h.length;d--;)h[d]=y+ge(h[d]);T=ee.test(e)&&he(t.parentNode)||t,C=h.join(",")}if(C)try{return H.apply(r,T.querySelectorAll(C)),r}catch(e){}finally{v||t.removeAttribute("id")}}}return u(e.replace($,"$1"),t,r,i)}function ae(){var e=[];return function t(n,i){return e.push(n+" ")>r.cacheLength&&delete t[e.shift()],t[n+" "]=i}}function se(e){return e[b]=!0,e}function ue(e){var t=p.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function le(e,t){for(var n=e.split("|"),i=e.length;i--;)r.attrHandle[n[i]]=t}function ce(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||j)-(~e.sourceIndex||j);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function de(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function fe(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function pe(e){return se((function(t){return t=+t,se((function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))}))}))}function he(e){return e&&void 0!==e.getElementsByTagName&&e}for(t in n=oe.support={},o=oe.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},f=oe.setDocument=function(e){var t,i,a=e?e.ownerDocument||e:w;return a!==p&&9===a.nodeType&&a.documentElement?(p=a,h=a.documentElement,(i=a.defaultView)&&i!==i.top&&(i.addEventListener?i.addEventListener("unload",ie,!1):i.attachEvent&&i.attachEvent("onunload",ie)),m=!o(a),n.attributes=ue((function(e){return e.className="i",!e.getAttribute("className")})),n.getElementsByTagName=ue((function(e){return e.appendChild(a.createComment("")),!e.getElementsByTagName("*").length})),n.getElementsByClassName=K.test(a.getElementsByClassName),n.getById=ue((function(e){return h.appendChild(e).id=b,!a.getElementsByName||!a.getElementsByName(b).length})),n.getById?(r.find.ID=function(e,t){if(void 0!==t.getElementById&&m){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},r.filter.ID=function(e){var t=e.replace(ne,re);return function(e){return e.getAttribute("id")===t}}):(delete r.find.ID,r.filter.ID=function(e){var t=e.replace(ne,re);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),r.find.TAG=n.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},r.find.CLASS=n.getElementsByClassName&&function(e,t){return m?t.getElementsByClassName(e):void 0},v=[],g=[],(n.qsa=K.test(a.querySelectorAll))&&(ue((function(e){h.appendChild(e).innerHTML="<a id='"+b+"'></a><select id='"+b+"-\f]' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&g.push("[*^$]="+B+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||g.push("\\["+B+"*(?:value|"+O+")"),e.querySelectorAll("[id~="+b+"-]").length||g.push("~="),e.querySelectorAll(":checked").length||g.push(":checked"),e.querySelectorAll("a#"+b+"+*").length||g.push(".#.+[+~]")})),ue((function(e){var t=a.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&g.push("name"+B+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||g.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),g.push(",.*:")}))),(n.matchesSelector=K.test(y=h.matches||h.webkitMatchesSelector||h.mozMatchesSelector||h.oMatchesSelector||h.msMatchesSelector))&&ue((function(e){n.disconnectedMatch=y.call(e,"div"),y.call(e,"[s!='']:x"),v.push("!=",W)})),g=g.length&&new RegExp(g.join("|")),v=v.length&&new RegExp(v.join("|")),t=K.test(h.compareDocumentPosition),x=t||K.test(h.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},S=t?function(e,t){if(e===t)return d=!0,0;var r=!e.compareDocumentPosition-!t.compareDocumentPosition;return r||(1&(r=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!n.sortDetached&&t.compareDocumentPosition(e)===r?e===a||e.ownerDocument===w&&x(w,e)?-1:t===a||t.ownerDocument===w&&x(w,t)?1:c?q(c,e)-q(c,t):0:4&r?-1:1)}:function(e,t){if(e===t)return d=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,s=[e],u=[t];if(!i||!o)return e===a?-1:t===a?1:i?-1:o?1:c?q(c,e)-q(c,t):0;if(i===o)return ce(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)u.unshift(n);for(;s[r]===u[r];)r++;return r?ce(s[r],u[r]):s[r]===w?-1:u[r]===w?1:0},a):p},oe.matches=function(e,t){return oe(e,null,null,t)},oe.matchesSelector=function(e,t){if((e.ownerDocument||e)!==p&&f(e),t=t.replace(U,"='$1']"),!(!n.matchesSelector||!m||v&&v.test(t)||g&&g.test(t)))try{var r=y.call(e,t);if(r||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(e){}return oe(t,p,null,[e]).length>0},oe.contains=function(e,t){return(e.ownerDocument||e)!==p&&f(e),x(e,t)},oe.attr=function(e,t){(e.ownerDocument||e)!==p&&f(e);var i=r.attrHandle[t.toLowerCase()],o=i&&D.call(r.attrHandle,t.toLowerCase())?i(e,t,!m):void 0;return void 0!==o?o:n.attributes||!m?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},oe.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},oe.uniqueSort=function(e){var t,r=[],i=0,o=0;if(d=!n.detectDuplicates,c=!n.sortStable&&e.slice(0),e.sort(S),d){for(;t=e[o++];)t===e[o]&&(i=r.push(o));for(;i--;)e.splice(r[i],1)}return c=null,e},i=oe.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=i(t);return n},r=oe.selectors={cacheLength:50,createPseudo:se,match:V,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(ne,re),e[3]=(e[3]||e[4]||e[5]||"").replace(ne,re),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||oe.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&oe.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return V.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&J.test(n)&&(t=a(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(ne,re).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=E[e+" "];return t||(t=new RegExp("(^|"+B+")"+e+"("+B+"|$)"))&&E(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=oe.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(I," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,d,f,p,h,m=o!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),y=!u&&!s;if(g){if(o){for(;m;){for(d=t;d=d[m];)if(s?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;h=m="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?g.firstChild:g.lastChild],a&&y){for(p=(l=(c=g[b]||(g[b]={}))[e]||[])[0]===T&&l[1],f=l[0]===T&&l[2],d=p&&g.childNodes[p];d=++p&&d&&d[m]||(f=p=0)||h.pop();)if(1===d.nodeType&&++f&&d===t){c[e]=[T,p,f];break}}else if(y&&(l=(t[b]||(t[b]={}))[e])&&l[0]===T)f=l[1];else for(;(d=++p&&d&&d[m]||(f=p=0)||h.pop())&&((s?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++f||(y&&((d[b]||(d[b]={}))[e]=[T,f]),d!==t)););return(f-=i)===r||f%r==0&&f/r>=0}}},PSEUDO:function(e,t){var n,i=r.pseudos[e]||r.setFilters[e.toLowerCase()]||oe.error("unsupported pseudo: "+e);return i[b]?i(t):i.length>1?(n=[e,e,"",t],r.setFilters.hasOwnProperty(e.toLowerCase())?se((function(e,n){for(var r,o=i(e,t),a=o.length;a--;)e[r=q(e,o[a])]=!(n[r]=o[a])})):function(e){return i(e,0,n)}):i}},pseudos:{not:se((function(e){var t=[],n=[],r=s(e.replace($,"$1"));return r[b]?se((function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}})),has:se((function(e){return function(t){return oe(e,t).length>0}})),contains:se((function(e){return e=e.replace(ne,re),function(t){return(t.textContent||t.innerText||i(t)).indexOf(e)>-1}})),lang:se((function(e){return Q.test(e||"")||oe.error("unsupported lang: "+e),e=e.replace(ne,re).toLowerCase(),function(t){var n;do{if(n=m?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===h},focus:function(e){return e===p.activeElement&&(!p.hasFocus||p.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!r.pseudos.empty(e)},header:function(e){return G.test(e.nodeName)},input:function(e){return Y.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:pe((function(){return[0]})),last:pe((function(e,t){return[t-1]})),eq:pe((function(e,t,n){return[0>n?n+t:n]})),even:pe((function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e})),odd:pe((function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e})),lt:pe((function(e,t,n){for(var r=0>n?n+t:n;--r>=0;)e.push(r);return e})),gt:pe((function(e,t,n){for(var r=0>n?n+t:n;++r<t;)e.push(r);return e}))}},r.pseudos.nth=r.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[t]=de(t);for(t in{submit:!0,reset:!0})r.pseudos[t]=fe(t);function me(){}function ge(e){for(var t=0,n=e.length,r="";n>t;t++)r+=e[t].value;return r}function ve(e,t,n){var r=t.dir,i=n&&"parentNode"===r,o=C++;return t.first?function(t,n,o){for(;t=t[r];)if(1===t.nodeType||i)return e(t,n,o)}:function(t,n,a){var s,u,l=[T,o];if(a){for(;t=t[r];)if((1===t.nodeType||i)&&e(t,n,a))return!0}else for(;t=t[r];)if(1===t.nodeType||i){if((s=(u=t[b]||(t[b]={}))[r])&&s[0]===T&&s[1]===o)return l[2]=s[2];if(u[r]=l,l[2]=e(t,n,a))return!0}}}function ye(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function xe(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;u>s;s++)(o=e[s])&&(!n||n(o,r,i))&&(a.push(o),l&&t.push(s));return a}function be(e,t,n,r,i,o){return r&&!r[b]&&(r=be(r)),i&&!i[b]&&(i=be(i,o)),se((function(o,a,s,u){var l,c,d,f=[],p=[],h=a.length,m=o||function(e,t,n){for(var r=0,i=t.length;i>r;r++)oe(e,t[r],n);return n}(t||"*",s.nodeType?[s]:s,[]),g=!e||!o&&t?m:xe(m,f,e,s,u),v=n?i||(o?e:h||r)?[]:a:g;if(n&&n(g,v,s,u),r)for(l=xe(v,p),r(l,[],s,u),c=l.length;c--;)(d=l[c])&&(v[p[c]]=!(g[p[c]]=d));if(o){if(i||e){if(i){for(l=[],c=v.length;c--;)(d=v[c])&&l.push(g[c]=d);i(null,v=[],l,u)}for(c=v.length;c--;)(d=v[c])&&(l=i?q(o,d):f[c])>-1&&(o[l]=!(a[l]=d))}}else v=xe(v===a?v.splice(h,v.length):v),i?i(null,a,v,u):H.apply(a,v)}))}function we(e){for(var t,n,i,o=e.length,a=r.relative[e[0].type],s=a||r.relative[" "],u=a?1:0,c=ve((function(e){return e===t}),s,!0),d=ve((function(e){return q(t,e)>-1}),s,!0),f=[function(e,n,r){var i=!a&&(r||n!==l)||((t=n).nodeType?c(e,n,r):d(e,n,r));return t=null,i}];o>u;u++)if(n=r.relative[e[u].type])f=[ve(ye(f),n)];else{if((n=r.filter[e[u].type].apply(null,e[u].matches))[b]){for(i=++u;o>i&&!r.relative[e[i].type];i++);return be(u>1&&ye(f),u>1&&ge(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace($,"$1"),n,i>u&&we(e.slice(u,i)),o>i&&we(e=e.slice(i)),o>i&&ge(e))}f.push(n)}return ye(f)}function Te(e,t){var n=t.length>0,i=e.length>0,o=function(o,a,s,u,c){var d,f,h,m=0,g="0",v=o&&[],y=[],x=l,b=o||i&&r.find.TAG("*",c),w=T+=null==x?1:Math.random()||.1,C=b.length;for(c&&(l=a!==p&&a);g!==C&&null!=(d=b[g]);g++){if(i&&d){for(f=0;h=e[f++];)if(h(d,a,s)){u.push(d);break}c&&(T=w)}n&&((d=!h&&d)&&m--,o&&v.push(d))}if(m+=g,n&&g!==m){for(f=0;h=t[f++];)h(v,y,a,s);if(o){if(m>0)for(;g--;)v[g]||y[g]||(y[g]=L.call(u));y=xe(y)}H.apply(u,y),c&&!o&&y.length>0&&m+t.length>1&&oe.uniqueSort(u)}return c&&(T=w,l=x),v};return n?se(o):o}return me.prototype=r.filters=r.pseudos,r.setFilters=new me,a=oe.tokenize=function(e,t){var n,i,o,a,s,u,l,c=N[e+" "];if(c)return t?0:c.slice(0);for(s=e,u=[],l=r.preFilter;s;){for(a in(!n||(i=z.exec(s)))&&(i&&(s=s.slice(i[0].length)||s),u.push(o=[])),n=!1,(i=X.exec(s))&&(n=i.shift(),o.push({value:n,type:i[0].replace($," ")}),s=s.slice(n.length)),r.filter)!(i=V[a].exec(s))||l[a]&&!(i=l[a](i))||(n=i.shift(),o.push({value:n,type:a,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?oe.error(e):N(e,u).slice(0)},s=oe.compile=function(e,t){var n,r=[],i=[],o=k[e+" "];if(!o){for(t||(t=a(e)),n=t.length;n--;)(o=we(t[n]))[b]?r.push(o):i.push(o);(o=k(e,Te(i,r))).selector=e}return o},u=oe.select=function(e,t,i,o){var u,l,c,d,f,p="function"==typeof e&&e,h=!o&&a(e=p.selector||e);if(i=i||[],1===h.length){if((l=h[0]=h[0].slice(0)).length>2&&"ID"===(c=l[0]).type&&n.getById&&9===t.nodeType&&m&&r.relative[l[1].type]){if(!(t=(r.find.ID(c.matches[0].replace(ne,re),t)||[])[0]))return i;p&&(t=t.parentNode),e=e.slice(l.shift().value.length)}for(u=V.needsContext.test(e)?0:l.length;u--&&(c=l[u],!r.relative[d=c.type]);)if((f=r.find[d])&&(o=f(c.matches[0].replace(ne,re),ee.test(l[0].type)&&he(t.parentNode)||t))){if(l.splice(u,1),!(e=o.length&&ge(l)))return H.apply(i,o),i;break}}return(p||s(e,h))(o,t,!m,i,ee.test(e)&&he(t.parentNode)||t),i},n.sortStable=b.split("").sort(S).join("")===b,n.detectDuplicates=!!d,f(),n.sortDetached=ue((function(e){return 1&e.compareDocumentPosition(p.createElement("div"))})),ue((function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")}))||le("type|href|height|width",(function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)})),n.attributes&&ue((function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")}))||le("value",(function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue})),ue((function(e){return null==e.getAttribute("disabled")}))||le(O,(function(e,t,n){var r;return n?void 0:!0===e[t]?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null})),oe}(r);m.find=w,m.expr=w.selectors,m.expr[":"]=m.expr.pseudos,m.unique=w.uniqueSort,m.text=w.getText,m.isXMLDoc=w.isXML,m.contains=w.contains;var T=m.expr.match.needsContext,C=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,E=/^.[^:#\[\.,]*$/;function N(e,t,n){if(m.isFunction(t))return m.grep(e,(function(e,r){return!!t.call(e,r,e)!==n}));if(t.nodeType)return m.grep(e,(function(e){return e===t!==n}));if("string"==typeof t){if(E.test(t))return m.filter(t,e,n);t=m.filter(t,e)}return m.grep(e,(function(e){return m.inArray(e,t)>=0!==n}))}m.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?m.find.matchesSelector(r,e)?[r]:[]:m.find.matches(e,m.grep(t,(function(e){return 1===e.nodeType})))},m.fn.extend({find:function(e){var t,n=[],r=this,i=r.length;if("string"!=typeof e)return this.pushStack(m(e).filter((function(){for(t=0;i>t;t++)if(m.contains(r[t],this))return!0})));for(t=0;i>t;t++)m.find(e,r[t],n);return(n=this.pushStack(i>1?m.unique(n):n)).selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(N(this,e||[],!1))},not:function(e){return this.pushStack(N(this,e||[],!0))},is:function(e){return!!N(this,"string"==typeof e&&T.test(e)?m(e):e||[],!1).length}});var k,S=r.document,j=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,D=m.fn.init=function(e,t){var n,r;if(!e)return this;if("string"==typeof e){if(!(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:j.exec(e))||!n[1]&&t)return!t||t.jquery?(t||k).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof m?t[0]:t,m.merge(this,m.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:S,!0)),C.test(n[1])&&m.isPlainObject(t))for(n in t)m.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if((r=S.getElementById(n[2]))&&r.parentNode){if(r.id!==n[2])return k.find(e);this.length=1,this[0]=r}return this.context=S,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):m.isFunction(e)?void 0!==k.ready?k.ready(e):e(m):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),m.makeArray(e,this))};D.prototype=m.fn,k=m(S);var A=/^(?:parents|prev(?:Until|All))/,L={children:!0,contents:!0,next:!0,prev:!0};function _(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}m.extend({dir:function(e,t,n){for(var r=[],i=e[t];i&&9!==i.nodeType&&(void 0===n||1!==i.nodeType||!m(i).is(n));)1===i.nodeType&&r.push(i),i=i[t];return r},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),m.fn.extend({has:function(e){var t,n=m(e,this),r=n.length;return this.filter((function(){for(t=0;r>t;t++)if(m.contains(this,n[t]))return!0}))},closest:function(e,t){for(var n,r=0,i=this.length,o=[],a=T.test(e)||"string"!=typeof e?m(e,t||this.context):0;i>r;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&m.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?m.unique(o):o)},index:function(e){return e?"string"==typeof e?m.inArray(this[0],m(e)):m.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(m.unique(m.merge(this.get(),m(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),m.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return m.dir(e,"parentNode")},parentsUntil:function(e,t,n){return m.dir(e,"parentNode",n)},next:function(e){return _(e,"nextSibling")},prev:function(e){return _(e,"previousSibling")},nextAll:function(e){return m.dir(e,"nextSibling")},prevAll:function(e){return m.dir(e,"previousSibling")},nextUntil:function(e,t,n){return m.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return m.dir(e,"previousSibling",n)},siblings:function(e){return m.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return m.sibling(e.firstChild)},contents:function(e){return m.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:m.merge([],e.childNodes)}},(function(e,t){m.fn[e]=function(n,r){var i=m.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=m.filter(r,i)),this.length>1&&(L[e]||(i=m.unique(i)),A.test(e)&&(i=i.reverse())),this.pushStack(i)}}));var H,M=/\S+/g,q={};function O(){S.addEventListener?(S.removeEventListener("DOMContentLoaded",B,!1),r.removeEventListener("load",B,!1)):(S.detachEvent("onreadystatechange",B),r.detachEvent("onload",B))}function B(){(S.addEventListener||"load"===event.type||"complete"===S.readyState)&&(O(),m.ready())}m.Callbacks=function(e){e="string"==typeof e?q[e]||function(e){var t=q[e]={};return m.each(e.match(M)||[],(function(e,n){t[n]=!0})),t}(e):m.extend({},e);var t,n,r,i,o,a,s=[],u=!e.once&&[],l=function(d){for(n=e.memory&&d,r=!0,o=a||0,a=0,i=s.length,t=!0;s&&i>o;o++)if(!1===s[o].apply(d[0],d[1])&&e.stopOnFalse){n=!1;break}t=!1,s&&(u?u.length&&l(u.shift()):n?s=[]:c.disable())},c={add:function(){if(s){var r=s.length;!function t(n){m.each(n,(function(n,r){var i=m.type(r);"function"===i?e.unique&&c.has(r)||s.push(r):r&&r.length&&"string"!==i&&t(r)}))}(arguments),t?i=s.length:n&&(a=r,l(n))}return this},remove:function(){return s&&m.each(arguments,(function(e,n){for(var r;(r=m.inArray(n,s,r))>-1;)s.splice(r,1),t&&(i>=r&&i--,o>=r&&o--)})),this},has:function(e){return e?m.inArray(e,s)>-1:!(!s||!s.length)},empty:function(){return s=[],i=0,this},disable:function(){return s=u=n=void 0,this},disabled:function(){return!s},lock:function(){return u=void 0,n||c.disable(),this},locked:function(){return!u},fireWith:function(e,n){return!s||r&&!u||(n=[e,(n=n||[]).slice?n.slice():n],t?u.push(n):l(n)),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},m.extend({Deferred:function(e){var t=[["resolve","done",m.Callbacks("once memory"),"resolved"],["reject","fail",m.Callbacks("once memory"),"rejected"],["notify","progress",m.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return m.Deferred((function(n){m.each(t,(function(t,o){var a=m.isFunction(e[t])&&e[t];i[o[1]]((function(){var e=a&&a.apply(this,arguments);e&&m.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o[0]+"With"](this===r?n.promise():this,a?[e]:arguments)}))})),e=null})).promise()},promise:function(e){return null!=e?m.extend(e,r):r}},i={};return r.pipe=r.then,m.each(t,(function(e,o){var a=o[2],s=o[3];r[o[1]]=a.add,s&&a.add((function(){n=s}),t[1^e][2].disable,t[2][2].lock),i[o[0]]=function(){return i[o[0]+"With"](this===i?r:this,arguments),this},i[o[0]+"With"]=a.fireWith})),r.promise(i),e&&e.call(i,i),i},when:function(e){var t,n,r,i=0,o=a.call(arguments),s=o.length,u=1!==s||e&&m.isFunction(e.promise)?s:0,l=1===u?e:m.Deferred(),c=function(e,n,r){return function(i){n[e]=this,r[e]=arguments.length>1?a.call(arguments):i,r===t?l.notifyWith(n,r):--u||l.resolveWith(n,r)}};if(s>1)for(t=new Array(s),n=new Array(s),r=new Array(s);s>i;i++)o[i]&&m.isFunction(o[i].promise)?o[i].promise().done(c(i,r,o)).fail(l.reject).progress(c(i,n,t)):--u;return u||l.resolveWith(r,o),l.promise()}}),m.fn.ready=function(e){return m.ready.promise().done(e),this},m.extend({isReady:!1,readyWait:1,holdReady:function(e){e?m.readyWait++:m.ready(!0)},ready:function(e){if(!0===e?! --m.readyWait:!m.isReady){if(!S.body)return setTimeout(m.ready);m.isReady=!0,!0!==e&&--m.readyWait>0||(H.resolveWith(S,[m]),m.fn.triggerHandler&&(m(S).triggerHandler("ready"),m(S).off("ready")))}}}),m.ready.promise=function(e){if(!H)if(H=m.Deferred(),"complete"===S.readyState)setTimeout(m.ready);else if(S.addEventListener)S.addEventListener("DOMContentLoaded",B,!1),r.addEventListener("load",B,!1);else{S.attachEvent("onreadystatechange",B),r.attachEvent("onload",B);var t=!1;try{t=null==r.frameElement&&S.documentElement}catch(e){}t&&t.doScroll&&function e(){if(!m.isReady){try{t.doScroll("left")}catch(t){return setTimeout(e,50)}O(),m.ready()}}()}return H.promise(e)};var F,R="undefined";for(F in m(p))break;p.ownLast="0"!==F,p.inlineBlockNeedsLayout=!1,m((function(){var e,t,n,r;(n=S.getElementsByTagName("body")[0])&&n.style&&(t=S.createElement("div"),(r=S.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(t),typeof t.style.zoom!==R&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",p.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(r))})),function(){var e=S.createElement("div");if(null==p.deleteExpando){p.deleteExpando=!0;try{delete e.test}catch(e){p.deleteExpando=!1}}e=null}(),m.acceptData=function(e){var t=m.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)};var P=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,W=/([A-Z])/g;function I(e,t,n){if(void 0===n&&1===e.nodeType){var r="data-"+t.replace(W,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:P.test(n)?m.parseJSON(n):n)}catch(e){}m.data(e,t,n)}else n=void 0}return n}function $(e){var t;for(t in e)if(("data"!==t||!m.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function z(e,t,n,r){if(m.acceptData(e)){var i,a,s=m.expando,u=e.nodeType,l=u?m.cache:e,c=u?e[s]:e[s]&&s;if(c&&l[c]&&(r||l[c].data)||void 0!==n||"string"!=typeof t)return c||(c=u?e[s]=o.pop()||m.guid++:s),l[c]||(l[c]=u?{}:{toJSON:m.noop}),("object"==typeof t||"function"==typeof t)&&(r?l[c]=m.extend(l[c],t):l[c].data=m.extend(l[c].data,t)),a=l[c],r||(a.data||(a.data={}),a=a.data),void 0!==n&&(a[m.camelCase(t)]=n),"string"==typeof t?null==(i=a[t])&&(i=a[m.camelCase(t)]):i=a,i}}function X(e,t,n){if(m.acceptData(e)){var r,i,o=e.nodeType,a=o?m.cache:e,s=o?e[m.expando]:m.expando;if(a[s]){if(t&&(r=n?a[s]:a[s].data)){i=(t=m.isArray(t)?t.concat(m.map(t,m.camelCase)):t in r||(t=m.camelCase(t))in r?[t]:t.split(" ")).length;for(;i--;)delete r[t[i]];if(n?!$(r):!m.isEmptyObject(r))return}(n||(delete a[s].data,$(a[s])))&&(o?m.cleanData([e],!0):p.deleteExpando||a!=a.window?delete a[s]:a[s]=null)}}}m.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(e){return!!(e=e.nodeType?m.cache[e[m.expando]]:e[m.expando])&&!$(e)},data:function(e,t,n){return z(e,t,n)},removeData:function(e,t){return X(e,t)},_data:function(e,t,n){return z(e,t,n,!0)},_removeData:function(e,t){return X(e,t,!0)}}),m.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=m.data(o),1===o.nodeType&&!m._data(o,"parsedAttrs"))){for(n=a.length;n--;)a[n]&&0===(r=a[n].name).indexOf("data-")&&I(o,r=m.camelCase(r.slice(5)),i[r]);m._data(o,"parsedAttrs",!0)}return i}return"object"==typeof e?this.each((function(){m.data(this,e)})):arguments.length>1?this.each((function(){m.data(this,e,t)})):o?I(o,e,m.data(o,e)):void 0},removeData:function(e){return this.each((function(){m.removeData(this,e)}))}}),m.extend({queue:function(e,t,n){var r;return e?(t=(t||"fx")+"queue",r=m._data(e,t),n&&(!r||m.isArray(n)?r=m._data(e,t,m.makeArray(n)):r.push(n)),r||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=m.queue(e,t),r=n.length,i=n.shift(),o=m._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,(function(){m.dequeue(e,t)}),o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return m._data(e,n)||m._data(e,n,{empty:m.Callbacks("once memory").add((function(){m._removeData(e,t+"queue"),m._removeData(e,n)}))})}}),m.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?m.queue(this[0],e):void 0===t?this:this.each((function(){var n=m.queue(this,e,t);m._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&m.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){m.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=m.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=m._data(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var U=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,J=["Top","Right","Bottom","Left"],Q=function(e,t){return e=t||e,"none"===m.css(e,"display")||!m.contains(e.ownerDocument,e)},V=m.access=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===m.type(n))for(s in i=!0,n)m.access(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,m.isFunction(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(m(e),n)})),t))for(;u>s;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},Y=/^(?:checkbox|radio)$/i;!function(){var e=S.createElement("input"),t=S.createElement("div"),n=S.createDocumentFragment();if(t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",p.leadingWhitespace=3===t.firstChild.nodeType,p.tbody=!t.getElementsByTagName("tbody").length,p.htmlSerialize=!!t.getElementsByTagName("link").length,p.html5Clone="<:nav></:nav>"!==S.createElement("nav").cloneNode(!0).outerHTML,e.type="checkbox",e.checked=!0,n.appendChild(e),p.appendChecked=e.checked,t.innerHTML="<textarea>x</textarea>",p.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,n.appendChild(t),t.innerHTML="<input type='radio' checked='checked' name='t'/>",p.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,p.noCloneEvent=!0,t.attachEvent&&(t.attachEvent("onclick",(function(){p.noCloneEvent=!1})),t.cloneNode(!0).click()),null==p.deleteExpando){p.deleteExpando=!0;try{delete t.test}catch(e){p.deleteExpando=!1}}}(),function(){var e,t,n=S.createElement("div");for(e in{submit:!0,change:!0,focusin:!0})t="on"+e,(p[e+"Bubbles"]=t in r)||(n.setAttribute(t,"t"),p[e+"Bubbles"]=!1===n.attributes[t].expando);n=null}();var G=/^(?:input|select|textarea)$/i,K=/^key/,Z=/^(?:mouse|pointer|contextmenu)|click/,ee=/^(?:focusinfocus|focusoutblur)$/,te=/^([^.]*)(?:\.(.+)|)$/;function ne(){return!0}function re(){return!1}function ie(){try{return S.activeElement}catch(e){}}function oe(e){var t=ae.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}m.event={global:{},add:function(e,t,n,r,i){var o,a,s,u,l,c,d,f,p,h,g,v=m._data(e);if(v){for(n.handler&&(n=(u=n).handler,i=u.selector),n.guid||(n.guid=m.guid++),(a=v.events)||(a=v.events={}),(c=v.handle)||(c=v.handle=function(e){return typeof m===R||e&&m.event.triggered===e.type?void 0:m.event.dispatch.apply(c.elem,arguments)},c.elem=e),s=(t=(t||"").match(M)||[""]).length;s--;)p=g=(o=te.exec(t[s])||[])[1],h=(o[2]||"").split(".").sort(),p&&(l=m.event.special[p]||{},p=(i?l.delegateType:l.bindType)||p,l=m.event.special[p]||{},d=m.extend({type:p,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&m.expr.match.needsContext.test(i),namespace:h.join(".")},u),(f=a[p])||((f=a[p]=[]).delegateCount=0,l.setup&&!1!==l.setup.call(e,r,h,c)||(e.addEventListener?e.addEventListener(p,c,!1):e.attachEvent&&e.attachEvent("on"+p,c))),l.add&&(l.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,d):f.push(d),m.event.global[p]=!0);e=null}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,d,f,p,h,g,v=m.hasData(e)&&m._data(e);if(v&&(c=v.events)){for(l=(t=(t||"").match(M)||[""]).length;l--;)if(p=g=(s=te.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),p){for(d=m.event.special[p]||{},f=c[p=(r?d.delegateType:d.bindType)||p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),u=o=f.length;o--;)a=f[o],!i&&g!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||r&&r!==a.selector&&("**"!==r||!a.selector)||(f.splice(o,1),a.selector&&f.delegateCount--,d.remove&&d.remove.call(e,a));u&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,v.handle)||m.removeEvent(e,p,v.handle),delete c[p])}else for(p in c)m.event.remove(e,p+t[l],n,r,!0);m.isEmptyObject(c)&&(delete v.handle,m._removeData(e,"events"))}},trigger:function(e,t,n,i){var o,a,s,u,l,c,d,p=[n||S],h=f.call(e,"type")?e.type:e,g=f.call(e,"namespace")?e.namespace.split("."):[];if(s=c=n=n||S,3!==n.nodeType&&8!==n.nodeType&&!ee.test(h+m.event.triggered)&&(h.indexOf(".")>=0&&(g=h.split("."),h=g.shift(),g.sort()),a=h.indexOf(":")<0&&"on"+h,(e=e[m.expando]?e:new m.Event(h,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=g.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:m.makeArray(t,[e]),l=m.event.special[h]||{},i||!l.trigger||!1!==l.trigger.apply(n,t))){if(!i&&!l.noBubble&&!m.isWindow(n)){for(u=l.delegateType||h,ee.test(u+h)||(s=s.parentNode);s;s=s.parentNode)p.push(s),c=s;c===(n.ownerDocument||S)&&p.push(c.defaultView||c.parentWindow||r)}for(d=0;(s=p[d++])&&!e.isPropagationStopped();)e.type=d>1?u:l.bindType||h,(o=(m._data(s,"events")||{})[e.type]&&m._data(s,"handle"))&&o.apply(s,t),(o=a&&s[a])&&o.apply&&m.acceptData(s)&&(e.result=o.apply(s,t),!1===e.result&&e.preventDefault());if(e.type=h,!i&&!e.isDefaultPrevented()&&(!l._default||!1===l._default.apply(p.pop(),t))&&m.acceptData(n)&&a&&n[h]&&!m.isWindow(n)){(c=n[a])&&(n[a]=null),m.event.triggered=h;try{n[h]()}catch(e){}m.event.triggered=void 0,c&&(n[a]=c)}return e.result}},dispatch:function(e){e=m.event.fix(e);var t,n,r,i,o,s=[],u=a.call(arguments),l=(m._data(this,"events")||{})[e.type]||[],c=m.event.special[e.type]||{};if(u[0]=e,e.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,e)){for(s=m.event.handlers.call(this,e,l),t=0;(i=s[t++])&&!e.isPropagationStopped();)for(e.currentTarget=i.elem,o=0;(r=i.handlers[o++])&&!e.isImmediatePropagationStopped();)(!e.namespace_re||e.namespace_re.test(r.namespace))&&(e.handleObj=r,e.data=r.data,void 0!==(n=((m.event.special[r.origType]||{}).handle||r.handler).apply(i.elem,u))&&!1===(e.result=n)&&(e.preventDefault(),e.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,o,a=[],s=t.delegateCount,u=e.target;if(s&&u.nodeType&&(!e.button||"click"!==e.type))for(;u!=this;u=u.parentNode||this)if(1===u.nodeType&&(!0!==u.disabled||"click"!==e.type)){for(i=[],o=0;s>o;o++)void 0===i[n=(r=t[o]).selector+" "]&&(i[n]=r.needsContext?m(n,this).index(u)>=0:m.find(n,this,null,[u]).length),i[n]&&i.push(r);i.length&&a.push({elem:u,handlers:i})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[m.expando])return e;var t,n,r,i=e.type,o=e,a=this.fixHooks[i];for(a||(this.fixHooks[i]=a=Z.test(i)?this.mouseHooks:K.test(i)?this.keyHooks:{}),r=a.props?this.props.concat(a.props):this.props,e=new m.Event(o),t=r.length;t--;)e[n=r[t]]=o[n];return e.target||(e.target=o.srcElement||S),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,o=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=(r=e.target.ownerDocument||S).documentElement,n=r.body,e.pageX=t.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==ie()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){return this===ie()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return m.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(e){return m.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){var i=m.extend(new m.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?m.event.trigger(i,null,t):m.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},m.removeEvent=S.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(typeof e[r]===R&&(e[r]=null),e.detachEvent(r,n))},m.Event=function(e,t){return this instanceof m.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?ne:re):this.type=e,t&&m.extend(this,t),this.timeStamp=e&&e.timeStamp||m.now(),void(this[m.expando]=!0)):new m.Event(e,t)},m.Event.prototype={isDefaultPrevented:re,isPropagationStopped:re,isImmediatePropagationStopped:re,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=ne,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=ne,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=ne,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},m.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){m.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;return(!r||r!==this&&!m.contains(this,r))&&(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}})),p.submitBubbles||(m.event.special.submit={setup:function(){return!m.nodeName(this,"form")&&void m.event.add(this,"click._submit keypress._submit",(function(e){var t=e.target,n=m.nodeName(t,"input")||m.nodeName(t,"button")?t.form:void 0;n&&!m._data(n,"submitBubbles")&&(m.event.add(n,"submit._submit",(function(e){e._submit_bubble=!0})),m._data(n,"submitBubbles",!0))}))},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&m.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){return!m.nodeName(this,"form")&&void m.event.remove(this,"._submit")}}),p.changeBubbles||(m.event.special.change={setup:function(){return G.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(m.event.add(this,"propertychange._change",(function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)})),m.event.add(this,"click._change",(function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),m.event.simulate("change",this,e,!0)}))),!1):void m.event.add(this,"beforeactivate._change",(function(e){var t=e.target;G.test(t.nodeName)&&!m._data(t,"changeBubbles")&&(m.event.add(t,"change._change",(function(e){!this.parentNode||e.isSimulated||e.isTrigger||m.event.simulate("change",this.parentNode,e,!0)})),m._data(t,"changeBubbles",!0))}))},handle:function(e){var t=e.target;return this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type?e.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return m.event.remove(this,"._change"),!G.test(this.nodeName)}}),p.focusinBubbles||m.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=function(e){m.event.simulate(t,e.target,m.event.fix(e),!0)};m.event.special[t]={setup:function(){var r=this.ownerDocument||this,i=m._data(r,t);i||r.addEventListener(e,n,!0),m._data(r,t,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=m._data(r,t)-1;i?m._data(r,t,i):(r.removeEventListener(e,n,!0),m._removeData(r,t))}}})),m.fn.extend({on:function(e,t,n,r,i){var o,a;if("object"==typeof e){for(o in"string"!=typeof t&&(n=n||t,t=void 0),e)this.on(o,t,n,e[o],i);return this}if(null==n&&null==r?(r=t,n=t=void 0):null==r&&("string"==typeof t?(r=n,n=void 0):(r=n,n=t,t=void 0)),!1===r)r=re;else if(!r)return this;return 1===i&&(a=r,r=function(e){return m().off(e),a.apply(this,arguments)},r.guid=a.guid||(a.guid=m.guid++)),this.each((function(){m.event.add(this,e,r,n,t)}))},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,m(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return(!1===t||"function"==typeof t)&&(n=t,t=void 0),!1===n&&(n=re),this.each((function(){m.event.remove(this,e,n,t)}))},trigger:function(e,t){return this.each((function(){m.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];return n?m.event.trigger(e,t,n,!0):void 0}});var ae="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",se=/ jQuery\d+="(?:null|\d+)"/g,ue=new RegExp("<(?:"+ae+")[\\s/>]","i"),le=/^\s+/,ce=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,de=/<([\w:]+)/,fe=/<tbody/i,pe=/<|&#?\w+;/,he=/<(?:script|style|link)/i,me=/checked\s*(?:[^=]|=\s*.checked.)/i,ge=/^$|\/(?:java|ecma)script/i,ve=/^true\/(.*)/,ye=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,xe={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:p.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},be=oe(S).appendChild(S.createElement("div"));function we(e,t){var n,r,i=0,o=typeof e.getElementsByTagName!==R?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!==R?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(r=n[i]);i++)!t||m.nodeName(r,t)?o.push(r):m.merge(o,we(r,t));return void 0===t||t&&m.nodeName(e,t)?m.merge([e],o):o}function Te(e){Y.test(e.type)&&(e.defaultChecked=e.checked)}function Ce(e,t){return m.nodeName(e,"table")&&m.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function Ee(e){return e.type=(null!==m.find.attr(e,"type"))+"/"+e.type,e}function Ne(e){var t=ve.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function ke(e,t){for(var n,r=0;null!=(n=e[r]);r++)m._data(n,"globalEval",!t||m._data(t[r],"globalEval"))}function Se(e,t){if(1===t.nodeType&&m.hasData(e)){var n,r,i,o=m._data(e),a=m._data(t,o),s=o.events;if(s)for(n in delete a.handle,a.events={},s)for(r=0,i=s[n].length;i>r;r++)m.event.add(t,n,s[n][r]);a.data&&(a.data=m.extend({},a.data))}}function je(e,t){var n,r,i;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!p.noCloneEvent&&t[m.expando]){for(r in(i=m._data(t)).events)m.removeEvent(t,r,i.handle);t.removeAttribute(m.expando)}"script"===n&&t.text!==e.text?(Ee(t).text=e.text,Ne(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),p.html5Clone&&e.innerHTML&&!m.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&Y.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}}xe.optgroup=xe.option,xe.tbody=xe.tfoot=xe.colgroup=xe.caption=xe.thead,xe.th=xe.td,m.extend({clone:function(e,t,n){var r,i,o,a,s,u=m.contains(e.ownerDocument,e);if(p.html5Clone||m.isXMLDoc(e)||!ue.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(be.innerHTML=e.outerHTML,be.removeChild(o=be.firstChild)),!(p.noCloneEvent&&p.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||m.isXMLDoc(e)))for(r=we(o),s=we(e),a=0;null!=(i=s[a]);++a)r[a]&&je(i,r[a]);if(t)if(n)for(s=s||we(e),r=r||we(o),a=0;null!=(i=s[a]);a++)Se(i,r[a]);else Se(e,o);return(r=we(o,"script")).length>0&&ke(r,!u&&we(e,"script")),r=s=i=null,o},buildFragment:function(e,t,n,r){for(var i,o,a,s,u,l,c,d=e.length,f=oe(t),h=[],g=0;d>g;g++)if((o=e[g])||0===o)if("object"===m.type(o))m.merge(h,o.nodeType?[o]:o);else if(pe.test(o)){for(s=s||f.appendChild(t.createElement("div")),u=(de.exec(o)||["",""])[1].toLowerCase(),c=xe[u]||xe._default,s.innerHTML=c[1]+o.replace(ce,"<$1></$2>")+c[2],i=c[0];i--;)s=s.lastChild;if(!p.leadingWhitespace&&le.test(o)&&h.push(t.createTextNode(le.exec(o)[0])),!p.tbody)for(i=(o="table"!==u||fe.test(o)?"<table>"!==c[1]||fe.test(o)?0:s:s.firstChild)&&o.childNodes.length;i--;)m.nodeName(l=o.childNodes[i],"tbody")&&!l.childNodes.length&&o.removeChild(l);for(m.merge(h,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=f.lastChild}else h.push(t.createTextNode(o));for(s&&f.removeChild(s),p.appendChecked||m.grep(we(h,"input"),Te),g=0;o=h[g++];)if((!r||-1===m.inArray(o,r))&&(a=m.contains(o.ownerDocument,o),s=we(f.appendChild(o),"script"),a&&ke(s),n))for(i=0;o=s[i++];)ge.test(o.type||"")&&n.push(o);return s=null,f},cleanData:function(e,t){for(var n,r,i,a,s=0,u=m.expando,l=m.cache,c=p.deleteExpando,d=m.event.special;null!=(n=e[s]);s++)if((t||m.acceptData(n))&&(a=(i=n[u])&&l[i])){if(a.events)for(r in a.events)d[r]?m.event.remove(n,r):m.removeEvent(n,r,a.handle);l[i]&&(delete l[i],c?delete n[u]:typeof n.removeAttribute!==R?n.removeAttribute(u):n[u]=null,o.push(i))}}}),m.fn.extend({text:function(e){return V(this,(function(e){return void 0===e?m.text(this):this.empty().append((this[0]&&this[0].ownerDocument||S).createTextNode(e))}),null,e,arguments.length)},append:function(){return this.domManip(arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Ce(this,e).appendChild(e)}))},prepend:function(){return this.domManip(arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ce(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return this.domManip(arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return this.domManip(arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},remove:function(e,t){for(var n,r=e?m.filter(e,this):this,i=0;null!=(n=r[i]);i++)t||1!==n.nodeType||m.cleanData(we(n)),n.parentNode&&(t&&m.contains(n.ownerDocument,n)&&ke(we(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&m.cleanData(we(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&m.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return m.clone(this,e,t)}))},html:function(e){return V(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(se,""):void 0;if(!("string"!=typeof e||he.test(e)||!p.htmlSerialize&&ue.test(e)||!p.leadingWhitespace&&le.test(e)||xe[(de.exec(e)||["",""])[1].toLowerCase()])){e=e.replace(ce,"<$1></$2>");try{for(;r>n;n++)1===(t=this[n]||{}).nodeType&&(m.cleanData(we(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=arguments[0];return this.domManip(arguments,(function(t){e=this.parentNode,m.cleanData(we(this)),e&&e.replaceChild(t,this)})),e&&(e.length||e.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t){e=s.apply([],e);var n,r,i,o,a,u,l=0,c=this.length,d=this,f=c-1,h=e[0],g=m.isFunction(h);if(g||c>1&&"string"==typeof h&&!p.checkClone&&me.test(h))return this.each((function(n){var r=d.eq(n);g&&(e[0]=h.call(this,n,r.html())),r.domManip(e,t)}));if(c&&(n=(u=m.buildFragment(e,this[0].ownerDocument,!1,this)).firstChild,1===u.childNodes.length&&(u=n),n)){for(i=(o=m.map(we(u,"script"),Ee)).length;c>l;l++)r=u,l!==f&&(r=m.clone(r,!0,!0),i&&m.merge(o,we(r,"script"))),t.call(this[l],r,l);if(i)for(a=o[o.length-1].ownerDocument,m.map(o,Ne),l=0;i>l;l++)r=o[l],ge.test(r.type||"")&&!m._data(r,"globalEval")&&m.contains(a,r)&&(r.src?m._evalUrl&&m._evalUrl(r.src):m.globalEval((r.text||r.textContent||r.innerHTML||"").replace(ye,"")));u=n=null}return this}}),m.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){m.fn[e]=function(e){for(var n,r=0,i=[],o=m(e),a=o.length-1;a>=r;r++)n=r===a?this:this.clone(!0),m(o[r])[t](n),u.apply(i,n.get());return this.pushStack(i)}}));var De,Ae={};function Le(e,t){var n,i=m(t.createElement(e)).appendTo(t.body),o=r.getDefaultComputedStyle&&(n=r.getDefaultComputedStyle(i[0]))?n.display:m.css(i[0],"display");return i.detach(),o}function _e(e){var t=S,n=Ae[e];return n||("none"!==(n=Le(e,t))&&n||((t=((De=(De||m("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement))[0].contentWindow||De[0].contentDocument).document).write(),t.close(),n=Le(e,t),De.detach()),Ae[e]=n),n}!function(){var e;p.shrinkWrapBlocks=function(){return null!=e?e:(e=!1,(n=S.getElementsByTagName("body")[0])&&n.style?(t=S.createElement("div"),(r=S.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(t),typeof t.style.zoom!==R&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(S.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(r),e):void 0);var t,n,r}}();var He,Me,qe=/^margin/,Oe=new RegExp("^("+U+")(?!px)[a-z%]+$","i"),Be=/^(top|right|bottom|left)$/;function Fe(e,t){return{get:function(){var n=e();if(null!=n)return n?void delete this.get:(this.get=t).apply(this,arguments)}}}r.getComputedStyle?(He=function(e){return e.ownerDocument.defaultView.opener?e.ownerDocument.defaultView.getComputedStyle(e,null):r.getComputedStyle(e,null)},Me=function(e,t,n){var r,i,o,a,s=e.style;return a=(n=n||He(e))?n.getPropertyValue(t)||n[t]:void 0,n&&(""!==a||m.contains(e.ownerDocument,e)||(a=m.style(e,t)),Oe.test(a)&&qe.test(t)&&(r=s.width,i=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=i,s.maxWidth=o)),void 0===a?a:a+""}):S.documentElement.currentStyle&&(He=function(e){return e.currentStyle},Me=function(e,t,n){var r,i,o,a,s=e.style;return null==(a=(n=n||He(e))?n[t]:void 0)&&s&&s[t]&&(a=s[t]),Oe.test(a)&&!Be.test(t)&&(r=s.left,(o=(i=e.runtimeStyle)&&i.left)&&(i.left=e.currentStyle.left),s.left="fontSize"===t?"1em":a,a=s.pixelLeft+"px",s.left=r,o&&(i.left=o)),void 0===a?a:a+""||"auto"}),function(){var e,t,n,i,o,a,s;if((e=S.createElement("div")).innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",t=(n=e.getElementsByTagName("a")[0])&&n.style){function u(){var e,t,n,u;(t=S.getElementsByTagName("body")[0])&&t.style&&(e=S.createElement("div"),(n=S.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",t.appendChild(n).appendChild(e),e.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",i=o=!1,s=!0,r.getComputedStyle&&(i="1%"!==(r.getComputedStyle(e,null)||{}).top,o="4px"===(r.getComputedStyle(e,null)||{width:"4px"}).width,(u=e.appendChild(S.createElement("div"))).style.cssText=e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",u.style.marginRight=u.style.width="0",e.style.width="1px",s=!parseFloat((r.getComputedStyle(u,null)||{}).marginRight),e.removeChild(u)),e.innerHTML="<table><tr><td></td><td>t</td></tr></table>",(u=e.getElementsByTagName("td"))[0].style.cssText="margin:0;border:0;padding:0;display:none",(a=0===u[0].offsetHeight)&&(u[0].style.display="",u[1].style.display="none",a=0===u[0].offsetHeight),t.removeChild(n))}t.cssText="float:left;opacity:.5",p.opacity="0.5"===t.opacity,p.cssFloat=!!t.cssFloat,e.style.backgroundClip="content-box",e.cloneNode(!0).style.backgroundClip="",p.clearCloneStyle="content-box"===e.style.backgroundClip,p.boxSizing=""===t.boxSizing||""===t.MozBoxSizing||""===t.WebkitBoxSizing,m.extend(p,{reliableHiddenOffsets:function(){return null==a&&u(),a},boxSizingReliable:function(){return null==o&&u(),o},pixelPosition:function(){return null==i&&u(),i},reliableMarginRight:function(){return null==s&&u(),s}})}}(),m.swap=function(e,t,n,r){var i,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];for(o in i=n.apply(e,r||[]),t)e.style[o]=a[o];return i};var Re=/alpha\([^)]*\)/i,Pe=/opacity\s*=\s*([^)]*)/,We=/^(none|table(?!-c[ea]).+)/,Ie=new RegExp("^("+U+")(.*)$","i"),$e=new RegExp("^([+-])=("+U+")","i"),ze={position:"absolute",visibility:"hidden",display:"block"},Xe={letterSpacing:"0",fontWeight:"400"},Ue=["Webkit","O","Moz","ms"];function Je(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=Ue.length;i--;)if((t=Ue[i]+n)in e)return t;return r}function Qe(e,t){for(var n,r,i,o=[],a=0,s=e.length;s>a;a++)(r=e[a]).style&&(o[a]=m._data(r,"olddisplay"),n=r.style.display,t?(o[a]||"none"!==n||(r.style.display=""),""===r.style.display&&Q(r)&&(o[a]=m._data(r,"olddisplay",_e(r.nodeName)))):(i=Q(r),(n&&"none"!==n||!i)&&m._data(r,"olddisplay",i?n:m.css(r,"display"))));for(a=0;s>a;a++)(r=e[a]).style&&(t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?o[a]||"":"none"));return e}function Ve(e,t,n){var r=Ie.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function Ye(e,t,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===t?1:0,a=0;4>o;o+=2)"margin"===n&&(a+=m.css(e,n+J[o],!0,i)),r?("content"===n&&(a-=m.css(e,"padding"+J[o],!0,i)),"margin"!==n&&(a-=m.css(e,"border"+J[o]+"Width",!0,i))):(a+=m.css(e,"padding"+J[o],!0,i),"padding"!==n&&(a+=m.css(e,"border"+J[o]+"Width",!0,i)));return a}function Ge(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,o=He(e),a=p.boxSizing&&"border-box"===m.css(e,"boxSizing",!1,o);if(0>=i||null==i){if((0>(i=Me(e,t,o))||null==i)&&(i=e.style[t]),Oe.test(i))return i;r=a&&(p.boxSizingReliable()||i===e.style[t]),i=parseFloat(i)||0}return i+Ye(e,t,n||(a?"border":"content"),r,o)+"px"}function Ke(e,t,n,r,i){return new Ke.prototype.init(e,t,n,r,i)}m.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Me(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:p.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=m.camelCase(t),u=e.style;if(t=m.cssProps[s]||(m.cssProps[s]=Je(u,s)),a=m.cssHooks[t]||m.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:u[t];if("string"==(o=typeof n)&&(i=$e.exec(n))&&(n=(i[1]+1)*i[2]+parseFloat(m.css(e,t)),o="number"),null!=n&&n==n&&("number"!==o||m.cssNumber[s]||(n+="px"),p.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),!a||!("set"in a)||void 0!==(n=a.set(e,n,r))))try{u[t]=n}catch(e){}}},css:function(e,t,n,r){var i,o,a,s=m.camelCase(t);return t=m.cssProps[s]||(m.cssProps[s]=Je(e.style,s)),(a=m.cssHooks[t]||m.cssHooks[s])&&"get"in a&&(o=a.get(e,!0,n)),void 0===o&&(o=Me(e,t,r)),"normal"===o&&t in Xe&&(o=Xe[t]),""===n||n?(i=parseFloat(o),!0===n||m.isNumeric(i)?i||0:o):o}}),m.each(["height","width"],(function(e,t){m.cssHooks[t]={get:function(e,n,r){return n?We.test(m.css(e,"display"))&&0===e.offsetWidth?m.swap(e,ze,(function(){return Ge(e,t,r)})):Ge(e,t,r):void 0},set:function(e,n,r){var i=r&&He(e);return Ve(0,n,r?Ye(e,t,r,p.boxSizing&&"border-box"===m.css(e,"boxSizing",!1,i),i):0)}}})),p.opacity||(m.cssHooks.opacity={get:function(e,t){return Pe.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,r=e.currentStyle,i=m.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=r&&r.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===m.trim(o.replace(Re,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||r&&!r.filter)||(n.filter=Re.test(o)?o.replace(Re,i):o+" "+i)}}),m.cssHooks.marginRight=Fe(p.reliableMarginRight,(function(e,t){return t?m.swap(e,{display:"inline-block"},Me,[e,"marginRight"]):void 0})),m.each({margin:"",padding:"",border:"Width"},(function(e,t){m.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];4>r;r++)i[e+J[r]+t]=o[r]||o[r-2]||o[0];return i}},qe.test(e)||(m.cssHooks[e+t].set=Ve)})),m.fn.extend({css:function(e,t){return V(this,(function(e,t,n){var r,i,o={},a=0;if(m.isArray(t)){for(r=He(e),i=t.length;i>a;a++)o[t[a]]=m.css(e,t[a],!1,r);return o}return void 0!==n?m.style(e,t,n):m.css(e,t)}),e,t,arguments.length>1)},show:function(){return Qe(this,!0)},hide:function(){return Qe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){Q(this)?m(this).show():m(this).hide()}))}}),m.Tween=Ke,Ke.prototype={constructor:Ke,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(m.cssNumber[n]?"":"px")},cur:function(){var e=Ke.propHooks[this.prop];return e&&e.get?e.get(this):Ke.propHooks._default.get(this)},run:function(e){var t,n=Ke.propHooks[this.prop];return this.options.duration?this.pos=t=m.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Ke.propHooks._default.set(this),this}},Ke.prototype.init.prototype=Ke.prototype,Ke.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=m.css(e.elem,e.prop,""))&&"auto"!==t?t:0:e.elem[e.prop]},set:function(e){m.fx.step[e.prop]?m.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[m.cssProps[e.prop]]||m.cssHooks[e.prop])?m.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},Ke.propHooks.scrollTop=Ke.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},m.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},m.fx=Ke.prototype.init,m.fx.step={};var Ze,et,tt=/^(?:toggle|show|hide)$/,nt=new RegExp("^(?:([+-])=|)("+U+")([a-z%]*)$","i"),rt=/queueHooks$/,it=[function(e,t,n){var r,i,o,a,s,u,l,c=this,d={},f=e.style,h=e.nodeType&&Q(e),g=m._data(e,"fxshow");for(r in n.queue||(null==(s=m._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,u=s.empty.fire,s.empty.fire=function(){s.unqueued||u()}),s.unqueued++,c.always((function(){c.always((function(){s.unqueued--,m.queue(e,"fx").length||s.empty.fire()}))}))),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],"inline"===("none"===(l=m.css(e,"display"))?m._data(e,"olddisplay")||_e(e.nodeName):l)&&"none"===m.css(e,"float")&&(p.inlineBlockNeedsLayout&&"inline"!==_e(e.nodeName)?f.zoom=1:f.display="inline-block")),n.overflow&&(f.overflow="hidden",p.shrinkWrapBlocks()||c.always((function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]}))),t)if(i=t[r],tt.exec(i)){if(delete t[r],o=o||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;h=!0}d[r]=g&&g[r]||m.style(e,r)}else l=void 0;if(m.isEmptyObject(d))"inline"===("none"===l?_e(e.nodeName):l)&&(f.display=l);else for(r in g?"hidden"in g&&(h=g.hidden):g=m._data(e,"fxshow",{}),o&&(g.hidden=!h),h?m(e).show():c.done((function(){m(e).hide()})),c.done((function(){var t;for(t in m._removeData(e,"fxshow"),d)m.style(e,t,d[t])})),d)a=ut(h?g[r]:0,r,c),r in g||(g[r]=a.start,h&&(a.end=a.start,a.start="width"===r||"height"===r?1:0))}],ot={"*":[function(e,t){var n=this.createTween(e,t),r=n.cur(),i=nt.exec(t),o=i&&i[3]||(m.cssNumber[e]?"":"px"),a=(m.cssNumber[e]||"px"!==o&&+r)&&nt.exec(m.css(n.elem,e)),s=1,u=20;if(a&&a[3]!==o){o=o||a[3],i=i||[],a=+r||1;do{a/=s=s||".5",m.style(n.elem,e,a+o)}while(s!==(s=n.cur()/r)&&1!==s&&--u)}return i&&(a=n.start=+a||+r||0,n.unit=o,n.end=i[1]?a+(i[1]+1)*i[2]:+i[2]),n}]};function at(){return setTimeout((function(){Ze=void 0})),Ze=m.now()}function st(e,t){var n,r={height:e},i=0;for(t=t?1:0;4>i;i+=2-t)r["margin"+(n=J[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function ut(e,t,n){for(var r,i=(ot[t]||[]).concat(ot["*"]),o=0,a=i.length;a>o;o++)if(r=i[o].call(n,t,e))return r}function lt(e,t,n){var r,i,o=0,a=it.length,s=m.Deferred().always((function(){delete u.elem})),u=function(){if(i)return!1;for(var t=Ze||at(),n=Math.max(0,l.startTime+l.duration-t),r=1-(n/l.duration||0),o=0,a=l.tweens.length;a>o;o++)l.tweens[o].run(r);return s.notifyWith(e,[l,r,n]),1>r&&a?n:(s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:m.extend({},t),opts:m.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:Ze||at(),duration:n.duration,tweens:[],createTween:function(t,n){var r=m.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;r>n;n++)l.tweens[n].run(1);return t?s.resolveWith(e,[l,t]):s.rejectWith(e,[l,t]),this}}),c=l.props;for(function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=m.camelCase(n)],o=e[n],m.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=m.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);a>o;o++)if(r=it[o].call(l,e,c,l.opts))return r;return m.map(c,ut,l),m.isFunction(l.opts.start)&&l.opts.start.call(e,l),m.fx.timer(m.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}m.Animation=m.extend(lt,{tweener:function(e,t){m.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,r=0,i=e.length;i>r;r++)n=e[r],ot[n]=ot[n]||[],ot[n].unshift(t)},prefilter:function(e,t){t?it.unshift(e):it.push(e)}}),m.speed=function(e,t,n){var r=e&&"object"==typeof e?m.extend({},e):{complete:n||!n&&t||m.isFunction(e)&&e,duration:e,easing:n&&t||t&&!m.isFunction(t)&&t};return r.duration=m.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in m.fx.speeds?m.fx.speeds[r.duration]:m.fx.speeds._default,(null==r.queue||!0===r.queue)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){m.isFunction(r.old)&&r.old.call(this),r.queue&&m.dequeue(this,r.queue)},r},m.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Q).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=m.isEmptyObject(e),o=m.speed(t,n,r),a=function(){var t=lt(this,m.extend({},e),o);(i||m._data(this,"finish"))&&t.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=m.timers,a=m._data(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&rt.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));(t||!n)&&m.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=m._data(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=m.timers,a=r?r.length:0;for(n.finish=!0,m.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;a>t;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish}))}}),m.each(["toggle","show","hide"],(function(e,t){var n=m.fn[t];m.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(st(t,!0),e,r,i)}})),m.each({slideDown:st("show"),slideUp:st("hide"),slideToggle:st("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){m.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),m.timers=[],m.fx.tick=function(){var e,t=m.timers,n=0;for(Ze=m.now();n<t.length;n++)(e=t[n])()||t[n]!==e||t.splice(n--,1);t.length||m.fx.stop(),Ze=void 0},m.fx.timer=function(e){m.timers.push(e),e()?m.fx.start():m.timers.pop()},m.fx.interval=13,m.fx.start=function(){et||(et=setInterval(m.fx.tick,m.fx.interval))},m.fx.stop=function(){clearInterval(et),et=null},m.fx.speeds={slow:600,fast:200,_default:400},m.fn.delay=function(e,t){return e=m.fx&&m.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}}))},function(){var e,t,n,r,i;(t=S.createElement("div")).setAttribute("className","t"),t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",r=t.getElementsByTagName("a")[0],i=(n=S.createElement("select")).appendChild(S.createElement("option")),e=t.getElementsByTagName("input")[0],r.style.cssText="top:1px",p.getSetAttribute="t"!==t.className,p.style=/top/.test(r.getAttribute("style")),p.hrefNormalized="/a"===r.getAttribute("href"),p.checkOn=!!e.value,p.optSelected=i.selected,p.enctype=!!S.createElement("form").enctype,n.disabled=!0,p.optDisabled=!i.disabled,(e=S.createElement("input")).setAttribute("value",""),p.input=""===e.getAttribute("value"),e.value="t",e.setAttribute("type","radio"),p.radioValue="t"===e.value}();var ct=/\r/g;m.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=m.isFunction(e),this.each((function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,m(this).val()):e)?i="":"number"==typeof i?i+="":m.isArray(i)&&(i=m.map(i,(function(e){return null==e?"":e+""}))),(t=m.valHooks[this.type]||m.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=m.valHooks[i.type]||m.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(ct,""):null==n?"":n:void 0}}),m.extend({valHooks:{option:{get:function(e){var t=m.find.attr(e,"value");return null!=t?t:m.trim(m.text(e))}},select:{get:function(e){for(var t,n,r=e.options,i=e.selectedIndex,o="select-one"===e.type||0>i,a=o?null:[],s=o?i+1:r.length,u=0>i?s:o?i:0;s>u;u++)if(!(!(n=r[u]).selected&&u!==i||(p.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&m.nodeName(n.parentNode,"optgroup"))){if(t=m(n).val(),o)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=m.makeArray(t),a=i.length;a--;)if(r=i[a],m.inArray(m.valHooks.option.get(r),o)>=0)try{r.selected=n=!0}catch(e){r.scrollHeight}else r.selected=!1;return n||(e.selectedIndex=-1),i}}}}),m.each(["radio","checkbox"],(function(){m.valHooks[this]={set:function(e,t){return m.isArray(t)?e.checked=m.inArray(m(e).val(),t)>=0:void 0}},p.checkOn||(m.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var dt,ft,pt=m.expr.attrHandle,ht=/^(?:checked|selected)$/i,mt=p.getSetAttribute,gt=p.input;m.fn.extend({attr:function(e,t){return V(this,m.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){m.removeAttr(this,e)}))}}),m.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===R?m.prop(e,t,n):(1===o&&m.isXMLDoc(e)||(t=t.toLowerCase(),r=m.attrHooks[t]||(m.expr.match.bool.test(t)?ft:dt)),void 0===n?r&&"get"in r&&null!==(i=r.get(e,t))?i:null==(i=m.find.attr(e,t))?void 0:i:null!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):void m.removeAttr(e,t))},removeAttr:function(e,t){var n,r,i=0,o=t&&t.match(M);if(o&&1===e.nodeType)for(;n=o[i++];)r=m.propFix[n]||n,m.expr.match.bool.test(n)?gt&&mt||!ht.test(n)?e[r]=!1:e[m.camelCase("default-"+n)]=e[r]=!1:m.attr(e,n,""),e.removeAttribute(mt?n:r)},attrHooks:{type:{set:function(e,t){if(!p.radioValue&&"radio"===t&&m.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}}}),ft={set:function(e,t,n){return!1===t?m.removeAttr(e,n):gt&&mt||!ht.test(n)?e.setAttribute(!mt&&m.propFix[n]||n,n):e[m.camelCase("default-"+n)]=e[n]=!0,n}},m.each(m.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=pt[t]||m.find.attr;pt[t]=gt&&mt||!ht.test(t)?function(e,t,r){var i,o;return r||(o=pt[t],pt[t]=i,i=null!=n(e,t,r)?t.toLowerCase():null,pt[t]=o),i}:function(e,t,n){return n?void 0:e[m.camelCase("default-"+t)]?t.toLowerCase():null}})),gt&&mt||(m.attrHooks.value={set:function(e,t,n){return m.nodeName(e,"input")?void(e.defaultValue=t):dt&&dt.set(e,t,n)}}),mt||(dt={set:function(e,t,n){var r=e.getAttributeNode(n);return r||e.setAttributeNode(r=e.ownerDocument.createAttribute(n)),r.value=t+="","value"===n||t===e.getAttribute(n)?t:void 0}},pt.id=pt.name=pt.coords=function(e,t,n){var r;return n?void 0:(r=e.getAttributeNode(t))&&""!==r.value?r.value:null},m.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&n.specified?n.value:void 0},set:dt.set},m.attrHooks.contenteditable={set:function(e,t,n){dt.set(e,""!==t&&t,n)}},m.each(["width","height"],(function(e,t){m.attrHooks[t]={set:function(e,n){return""===n?(e.setAttribute(t,"auto"),n):void 0}}}))),p.style||(m.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var vt=/^(?:input|select|textarea|button|object)$/i,yt=/^(?:a|area)$/i;m.fn.extend({prop:function(e,t){return V(this,m.prop,e,t,arguments.length>1)},removeProp:function(e){return e=m.propFix[e]||e,this.each((function(){try{this[e]=void 0,delete this[e]}catch(e){}}))}}),m.extend({propFix:{for:"htmlFor",class:"className"},prop:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return(1!==o||!m.isXMLDoc(e))&&(t=m.propFix[t]||t,i=m.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=m.find.attr(e,"tabindex");return t?parseInt(t,10):vt.test(e.nodeName)||yt.test(e.nodeName)&&e.href?0:-1}}}}),p.hrefNormalized||m.each(["href","src"],(function(e,t){m.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}})),p.optSelected||(m.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}}),m.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){m.propFix[this.toLowerCase()]=this})),p.enctype||(m.propFix.enctype="encoding");var xt=/[\t\r\n\f]/g;m.fn.extend({addClass:function(e){var t,n,r,i,o,a,s=0,u=this.length,l="string"==typeof e&&e;if(m.isFunction(e))return this.each((function(t){m(this).addClass(e.call(this,t,this.className))}));if(l)for(t=(e||"").match(M)||[];u>s;s++)if(r=1===(n=this[s]).nodeType&&(n.className?(" "+n.className+" ").replace(xt," "):" ")){for(o=0;i=t[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");a=m.trim(r),n.className!==a&&(n.className=a)}return this},removeClass:function(e){var t,n,r,i,o,a,s=0,u=this.length,l=0===arguments.length||"string"==typeof e&&e;if(m.isFunction(e))return this.each((function(t){m(this).removeClass(e.call(this,t,this.className))}));if(l)for(t=(e||"").match(M)||[];u>s;s++)if(r=1===(n=this[s]).nodeType&&(n.className?(" "+n.className+" ").replace(xt," "):"")){for(o=0;i=t[o++];)for(;r.indexOf(" "+i+" ")>=0;)r=r.replace(" "+i+" "," ");a=e?m.trim(r):"",n.className!==a&&(n.className=a)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):this.each(m.isFunction(e)?function(n){m(this).toggleClass(e.call(this,n,this.className,t),t)}:function(){if("string"===n)for(var t,r=0,i=m(this),o=e.match(M)||[];t=o[r++];)i.hasClass(t)?i.removeClass(t):i.addClass(t);else(n===R||"boolean"===n)&&(this.className&&m._data(this,"__className__",this.className),this.className=this.className||!1===e?"":m._data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;r>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(xt," ").indexOf(t)>=0)return!0;return!1}}),m.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),(function(e,t){m.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}})),m.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var bt=m.now(),wt=/\?/,Tt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;m.parseJSON=function(e){if(r.JSON&&r.JSON.parse)return r.JSON.parse(e+"");var t,n=null,i=m.trim(e+"");return i&&!m.trim(i.replace(Tt,(function(e,r,i,o){return t&&r&&(n=0),0===n?e:(t=i||r,n+=!o-!i,"")})))?Function("return "+i)():m.error("Invalid JSON: "+e)},m.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{r.DOMParser?t=(new DOMParser).parseFromString(e,"text/xml"):((t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e))}catch(e){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||m.error("Invalid XML: "+e),t};var Ct,Et,Nt=/#.*$/,kt=/([?&])_=[^&]*/,St=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,jt=/^(?:GET|HEAD)$/,Dt=/^\/\//,At=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Lt={},_t={},Ht="*/".concat("*");try{Et=location.href}catch(e){(Et=S.createElement("a")).href="",Et=Et.href}function Mt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(M)||[];if(m.isFunction(n))for(;r=o[i++];)"+"===r.charAt(0)?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function qt(e,t,n,r){var i={},o=e===_t;function a(s){var u;return i[s]=!0,m.each(e[s]||[],(function(e,s){var l=s(t,n,r);return"string"!=typeof l||o||i[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),a(l),!1)})),u}return a(t.dataTypes[0])||!i["*"]&&a("*")}function Ot(e,t){var n,r,i=m.ajaxSettings.flatOptions||{};for(r in t)void 0!==t[r]&&((i[r]?e:n||(n={}))[r]=t[r]);return n&&m.extend(!0,e,n),e}Ct=At.exec(Et.toLowerCase())||[],m.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Et,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ct[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ht,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":m.parseJSON,"text xml":m.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Ot(Ot(e,m.ajaxSettings),t):Ot(m.ajaxSettings,e)},ajaxPrefilter:Mt(Lt),ajaxTransport:Mt(_t),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,r,i,o,a,s,u,l,c=m.ajaxSetup({},t),d=c.context||c,f=c.context&&(d.nodeType||d.jquery)?m(d):m.event,p=m.Deferred(),h=m.Callbacks("once memory"),g=c.statusCode||{},v={},y={},x=0,b="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!l)for(l={};t=St.exec(o);)l[t[1].toLowerCase()]=t[2];t=l[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?o:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=y[n]=y[n]||e,v[e]=t),this},overrideMimeType:function(e){return x||(c.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>x)for(t in e)g[t]=[g[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||b;return u&&u.abort(t),T(0,t),this}};if(p.promise(w).complete=h.add,w.success=w.done,w.error=w.fail,c.url=((e||c.url||Et)+"").replace(Nt,"").replace(Dt,Ct[1]+"//"),c.type=t.method||t.type||c.method||c.type,c.dataTypes=m.trim(c.dataType||"*").toLowerCase().match(M)||[""],null==c.crossDomain&&(n=At.exec(c.url.toLowerCase()),c.crossDomain=!(!n||n[1]===Ct[1]&&n[2]===Ct[2]&&(n[3]||("http:"===n[1]?"80":"443"))===(Ct[3]||("http:"===Ct[1]?"80":"443")))),c.data&&c.processData&&"string"!=typeof c.data&&(c.data=m.param(c.data,c.traditional)),qt(Lt,c,t,w),2===x)return w;for(r in(s=m.event&&c.global)&&0==m.active++&&m.event.trigger("ajaxStart"),c.type=c.type.toUpperCase(),c.hasContent=!jt.test(c.type),i=c.url,c.hasContent||(c.data&&(i=c.url+=(wt.test(i)?"&":"?")+c.data,delete c.data),!1===c.cache&&(c.url=kt.test(i)?i.replace(kt,"$1_="+bt++):i+(wt.test(i)?"&":"?")+"_="+bt++)),c.ifModified&&(m.lastModified[i]&&w.setRequestHeader("If-Modified-Since",m.lastModified[i]),m.etag[i]&&w.setRequestHeader("If-None-Match",m.etag[i])),(c.data&&c.hasContent&&!1!==c.contentType||t.contentType)&&w.setRequestHeader("Content-Type",c.contentType),w.setRequestHeader("Accept",c.dataTypes[0]&&c.accepts[c.dataTypes[0]]?c.accepts[c.dataTypes[0]]+("*"!==c.dataTypes[0]?", "+Ht+"; q=0.01":""):c.accepts["*"]),c.headers)w.setRequestHeader(r,c.headers[r]);if(c.beforeSend&&(!1===c.beforeSend.call(d,w,c)||2===x))return w.abort();for(r in b="abort",{success:1,error:1,complete:1})w[r](c[r]);if(u=qt(_t,c,t,w)){w.readyState=1,s&&f.trigger("ajaxSend",[w,c]),c.async&&c.timeout>0&&(a=setTimeout((function(){w.abort("timeout")}),c.timeout));try{x=1,u.send(v,T)}catch(e){if(!(2>x))throw e;T(-1,e)}}else T(-1,"No Transport");function T(e,t,n,r){var l,v,y,b,T,C=t;2!==x&&(x=2,a&&clearTimeout(a),u=void 0,o=r||"",w.readyState=e>0?4:0,l=e>=200&&300>e||304===e,n&&(b=function(e,t,n){for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(a in s)if(s[a]&&s[a].test(i)){u.unshift(a);break}if(u[0]in n)o=u[0];else{for(a in n){if(!u[0]||e.converters[a+" "+u[0]]){o=a;break}r||(r=a)}o=o||r}return o?(o!==u[0]&&u.unshift(o),n[o]):void 0}(c,w,n)),b=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(c,b,w,l),l?(c.ifModified&&((T=w.getResponseHeader("Last-Modified"))&&(m.lastModified[i]=T),(T=w.getResponseHeader("etag"))&&(m.etag[i]=T)),204===e||"HEAD"===c.type?C="nocontent":304===e?C="notmodified":(C=b.state,v=b.data,l=!(y=b.error))):(y=C,(e||!C)&&(C="error",0>e&&(e=0))),w.status=e,w.statusText=(t||C)+"",l?p.resolveWith(d,[v,C,w]):p.rejectWith(d,[w,C,y]),w.statusCode(g),g=void 0,s&&f.trigger(l?"ajaxSuccess":"ajaxError",[w,c,l?v:y]),h.fireWith(d,[w,C]),s&&(f.trigger("ajaxComplete",[w,c]),--m.active||m.event.trigger("ajaxStop")))}return w},getJSON:function(e,t,n){return m.get(e,t,n,"json")},getScript:function(e,t){return m.get(e,void 0,t,"script")}}),m.each(["get","post"],(function(e,t){m[t]=function(e,n,r,i){return m.isFunction(n)&&(i=i||r,r=n,n=void 0),m.ajax({url:e,type:t,dataType:i,data:n,success:r})}})),m._evalUrl=function(e){return m.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},m.fn.extend({wrapAll:function(e){if(m.isFunction(e))return this.each((function(t){m(this).wrapAll(e.call(this,t))}));if(this[0]){var t=m(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e})).append(this)}return this},wrapInner:function(e){return this.each(m.isFunction(e)?function(t){m(this).wrapInner(e.call(this,t))}:function(){var t=m(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=m.isFunction(e);return this.each((function(n){m(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(){return this.parent().each((function(){m.nodeName(this,"body")||m(this).replaceWith(this.childNodes)})).end()}}),m.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0||!p.reliableHiddenOffsets()&&"none"===(e.style&&e.style.display||m.css(e,"display"))},m.expr.filters.visible=function(e){return!m.expr.filters.hidden(e)};var Bt=/%20/g,Ft=/\[\]$/,Rt=/\r?\n/g,Pt=/^(?:submit|button|image|reset|file)$/i,Wt=/^(?:input|select|textarea|keygen)/i;function It(e,t,n,r){var i;if(m.isArray(t))m.each(t,(function(t,i){n||Ft.test(e)?r(e,i):It(e+"["+("object"==typeof i?t:"")+"]",i,n,r)}));else if(n||"object"!==m.type(t))r(e,t);else for(i in t)It(e+"["+i+"]",t[i],n,r)}m.param=function(e,t){var n,r=[],i=function(e,t){t=m.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=m.ajaxSettings&&m.ajaxSettings.traditional),m.isArray(e)||e.jquery&&!m.isPlainObject(e))m.each(e,(function(){i(this.name,this.value)}));else for(n in e)It(n,e[n],t,i);return r.join("&").replace(Bt,"+")},m.fn.extend({serialize:function(){return m.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=m.prop(this,"elements");return e?m.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!m(this).is(":disabled")&&Wt.test(this.nodeName)&&!Pt.test(e)&&(this.checked||!Y.test(e))})).map((function(e,t){var n=m(this).val();return null==n?null:m.isArray(n)?m.map(n,(function(e){return{name:t.name,value:e.replace(Rt,"\r\n")}})):{name:t.name,value:n.replace(Rt,"\r\n")}})).get()}}),m.ajaxSettings.xhr=void 0!==r.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&Ut()||function(){try{return new r.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}()}:Ut;var $t=0,zt={},Xt=m.ajaxSettings.xhr();function Ut(){try{return new r.XMLHttpRequest}catch(e){}}r.attachEvent&&r.attachEvent("onunload",(function(){for(var e in zt)zt[e](void 0,!0)})),p.cors=!!Xt&&"withCredentials"in Xt,(Xt=p.ajax=!!Xt)&&m.ajaxTransport((function(e){var t;if(!e.crossDomain||p.cors)return{send:function(n,r){var i,o=e.xhr(),a=++$t;if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(i in e.xhrFields)o[i]=e.xhrFields[i];for(i in e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest"),n)void 0!==n[i]&&o.setRequestHeader(i,n[i]+"");o.send(e.hasContent&&e.data||null),t=function(n,i){var s,u,l;if(t&&(i||4===o.readyState))if(delete zt[a],t=void 0,o.onreadystatechange=m.noop,i)4!==o.readyState&&o.abort();else{l={},s=o.status,"string"==typeof o.responseText&&(l.text=o.responseText);try{u=o.statusText}catch(e){u=""}s||!e.isLocal||e.crossDomain?1223===s&&(s=204):s=l.text?200:404}l&&r(s,u,l,o.getAllResponseHeaders())},e.async?4===o.readyState?setTimeout(t):o.onreadystatechange=zt[a]=t:t()},abort:function(){t&&t(void 0,!0)}}})),m.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return m.globalEval(e),e}}}),m.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)})),m.ajaxTransport("script",(function(e){if(e.crossDomain){var t,n=S.head||m("head")[0]||S.documentElement;return{send:function(r,i){(t=S.createElement("script")).async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||i(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}}));var Jt=[],Qt=/(=)\?(?=&|$)|\?\?/;m.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Jt.pop()||m.expando+"_"+bt++;return this[e]=!0,e}}),m.ajaxPrefilter("json jsonp",(function(e,t,n){var i,o,a,s=!1!==e.jsonp&&(Qt.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Qt.test(e.data)&&"data");return s||"jsonp"===e.dataTypes[0]?(i=e.jsonpCallback=m.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Qt,"$1"+i):!1!==e.jsonp&&(e.url+=(wt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return a||m.error(i+" was not called"),a[0]},e.dataTypes[0]="json",o=r[i],r[i]=function(){a=arguments},n.always((function(){r[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,Jt.push(i)),a&&m.isFunction(o)&&o(a[0]),a=o=void 0})),"script"):void 0})),m.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||S;var r=C.exec(e),i=!n&&[];return r?[t.createElement(r[1])]:(r=m.buildFragment([e],t,i),i&&i.length&&m(i).remove(),m.merge([],r.childNodes))};var Vt=m.fn.load;m.fn.load=function(e,t,n){if("string"!=typeof e&&Vt)return Vt.apply(this,arguments);var r,i,o,a=this,s=e.indexOf(" ");return s>=0&&(r=m.trim(e.slice(s,e.length)),e=e.slice(0,s)),m.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&m.ajax({url:e,type:o,dataType:"html",data:t}).done((function(e){i=arguments,a.html(r?m("<div>").append(m.parseHTML(e)).find(r):e)})).complete(n&&function(e,t){a.each(n,i||[e.responseText,t,e])}),this},m.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){m.fn[t]=function(e){return this.on(t,e)}})),m.expr.filters.animated=function(e){return m.grep(m.timers,(function(t){return e===t.elem})).length};var Yt=r.document.documentElement;function Gt(e){return m.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}m.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=m.css(e,"position"),c=m(e),d={};"static"===l&&(e.style.position="relative"),s=c.offset(),o=m.css(e,"top"),u=m.css(e,"left"),("absolute"===l||"fixed"===l)&&m.inArray("auto",[o,u])>-1?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),m.isFunction(t)&&(t=t.call(e,n,s)),null!=t.top&&(d.top=t.top-s.top+a),null!=t.left&&(d.left=t.left-s.left+i),"using"in t?t.using.call(e,d):c.css(d)}},m.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){m.offset.setOffset(this,e,t)}));var t,n,r={top:0,left:0},i=this[0],o=i&&i.ownerDocument;return o?(t=o.documentElement,m.contains(t,i)?(typeof i.getBoundingClientRect!==R&&(r=i.getBoundingClientRect()),n=Gt(o),{top:r.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:r.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):r):void 0},position:function(){if(this[0]){var e,t,n={top:0,left:0},r=this[0];return"fixed"===m.css(r,"position")?t=r.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),m.nodeName(e[0],"html")||(n=e.offset()),n.top+=m.css(e[0],"borderTopWidth",!0),n.left+=m.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-m.css(r,"marginTop",!0),left:t.left-n.left-m.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent||Yt;e&&!m.nodeName(e,"html")&&"static"===m.css(e,"position");)e=e.offsetParent;return e||Yt}))}}),m.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n=/Y/.test(t);m.fn[e]=function(r){return V(this,(function(e,r,i){var o=Gt(e);return void 0===i?o?t in o?o[t]:o.document.documentElement[r]:e[r]:void(o?o.scrollTo(n?m(o).scrollLeft():i,n?i:m(o).scrollTop()):e[r]=i)}),e,r,arguments.length,null)}})),m.each(["top","left"],(function(e,t){m.cssHooks[t]=Fe(p.pixelPosition,(function(e,n){return n?(n=Me(e,t),Oe.test(n)?m(e).position()[t]+"px":n):void 0}))})),m.each({Height:"height",Width:"width"},(function(e,t){m.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){m.fn[r]=function(r,i){var o=arguments.length&&(n||"boolean"!=typeof r),a=n||(!0===r||!0===i?"margin":"border");return V(this,(function(t,n,r){var i;return m.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(i=t.documentElement,Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])):void 0===r?m.css(t,n,a):m.style(t,n,r,a)}),t,o?r:void 0,o,null)}}))})),m.fn.size=function(){return this.length},m.fn.andSelf=m.fn.addBack,void 0===(n=function(){return m}.apply(t,[]))||(e.exports=n);var Kt=r.jQuery,Zt=r.$;return m.noConflict=function(e){return r.$===m&&(r.$=Zt),e&&r.jQuery===m&&(r.jQuery=Kt),m},typeof i===R&&(r.jQuery=r.$=m),m},"object"==typeof e.exports?e.exports=r.document?i(r,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return i(e)}:i(r)},602:(e,t,n)=>{var r,i;void 0===(i="function"==typeof(r=function(){function e(){for(var e=0,t={};e<arguments.length;e++){var n=arguments[e];for(var r in n)t[r]=n[r]}return t}function t(n){function r(t,i,o){var a;if("undefined"!=typeof document){if(arguments.length>1){if("number"==typeof(o=e({path:"/"},r.defaults,o)).expires){var s=new Date;s.setMilliseconds(s.getMilliseconds()+864e5*o.expires),o.expires=s}try{a=JSON.stringify(i),/^[\{\[]/.test(a)&&(i=a)}catch(e){}return i=n.write?n.write(i,t):encodeURIComponent(String(i)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),t=(t=(t=encodeURIComponent(String(t))).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent)).replace(/[\(\)]/g,escape),document.cookie=[t,"=",i,o.expires&&"; expires="+o.expires.toUTCString(),o.path&&"; path="+o.path,o.domain&&"; domain="+o.domain,o.secure?"; secure":""].join("")}t||(a={});for(var u=document.cookie?document.cookie.split("; "):[],l=/(%[0-9A-Z]{2})+/g,c=0;c<u.length;c++){var d=u[c].split("="),f=d.slice(1).join("=");'"'===f.charAt(0)&&(f=f.slice(1,-1));try{var p=d[0].replace(l,decodeURIComponent);if(f=n.read?n.read(f,p):n(f,p)||f.replace(l,decodeURIComponent),this.json)try{f=JSON.parse(f)}catch(e){}if(t===p){a=f;break}t||(a[p]=f)}catch(e){}}return a}}return r.set=r,r.get=function(e){return r(e)},r.getJSON=function(){return r.apply({json:!0},[].slice.call(arguments))},r.defaults={},r.remove=function(t,n){r(t,"",e(n,{expires:-1}))},r.withConverter=t,r}return t((function(){}))})?r.call(t,n,t,e):r)||(e.exports=i)},632:e=>{e.exports=function(){var e=document.documentElement.dataset.theme;if(!e&&!(e=localStorage.getItem("theme"))&&!document.documentElement.dataset.colorScheme){var t=function(){for(var e=window;e.parent!==e;)e=e.parent;return e}();e=t.CMS?function(e){var t;if(e)return e.settings&&e.settings.color_scheme?t=e.settings.color_scheme:e.config&&e.config.color_scheme&&(t=e.config.color_scheme),t}(t.CMS):JSON.parse(localStorage.getItem("cms_cookie")||"{}").color_scheme}e&&(document.documentElement.dataset.theme=e)}},369:e=>{function t(e){return[].slice.call(e)}function n(e,t){return!(!e.className||!e.className.match(new RegExp(t)))}function r(e,t){for(var r=e;r&&1===r.nodeType;){if(n(r,t))return r;r=r.parentNode}return null}function i(){return t(document.getElementsByTagName("input")).map((function(e){if(n(e,"vDateField")||n(e,"vTimeField"))return e})).filter((function(e){return!!e}))}e.exports=function(){i().forEach((function(e){var t,i=r(e,"fieldBox"),o=r(e,"datetime");if(o&&(t=r(o,"fieldBox")),!(t&&t===i||i)){var a=e.parentNode;if(!a)return;n(a,"datetime")&&(a.innerHTML.split(/<br ?\/*>/).forEach((function(e){document.body.contains(a)&&a.insertAdjacentHTML("beforebegin",'<div class="fieldBox">'+e+"</div>")})),document.body.contains(a)&&(a.parentNode.removeChild(a),a=null))}})),i().forEach((function(e){var n=r(e,"datetime");n||(n=r(e,"fieldBox")),n&&t(n.childNodes).forEach((function(e){if(e.nodeType===Node.TEXT_NODE&&""!==e.textContent.trim()){var t=document.createElement("label");t.appendChild(document.createTextNode(e.textContent)),e.parentNode.replaceChild(t,e)}}))}))}},455:e=>{(window.jQuery||window.django&&window.django.jQuery)&&((window.jQuery||window.django.jQuery).fn.touchSupport=function(){function e(e){var t=e.originalEvent.changedTouches[0],n=document.createEvent("MouseEvent");n.initMouseEvent({touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup"}[e.type],!0,!0,window,1,t.screenX,t.screenY,t.clientX,t.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(n),e.stopPropagation()}this.on({touchstart:e,touchmove:e,touchend:e,touchcancel:e})}),e.exports=function(){var e;window.jQuery&&window.django&&window.django.jQuery&&(e=window.jQuery||window.django.jQuery).fn.touchSupport&&e(".drag-handler").length&&e(window).touchSupport()}},622:(e,t,n)=>{var r=n(732);e.exports=function(){r(document).on("submit","form",(function(e){e.isDefaultPrevented()||r(e.target).on("submit",(function(e){e.preventDefault()}))}))}},495:e=>{function t(){var e,t;(t=document.getElementsByClassName("related-widget-wrapper"),(e=t,[].slice.call(e)).map((function(e){return e})).filter((function(e){return!!e}))).forEach((function(e){var t=e.getElementsByClassName("related-widget-wrapper-link");t&&t.length&&(e.className+=" widget-wrapper-links-"+t.length)}))}e.exports=function(){t()}},568:e=>{function t(e,t){var n=e.className,r=n.indexOf(t);if(!e||!t)return!1;-1===r?n+=" "+t:n=n.substr(0,r)+n.substr(r+t.length),e.className=n}e.exports=function(){var e=document.getElementsByClassName("submenu")[0],n=document.getElementsByClassName("menu-item")[0],r=document.getElementsByTagName("html")[0];e&&n&&(n.addEventListener("click",(function(r){(r||window.event).preventDefault(),t(e,"submenu-open"),t(n,"menu-item-open")})),r.addEventListener("click",(function(e){if(e.target!==n){var t=document.getElementsByClassName("submenu-open")[0],r=document.getElementsByClassName("menu-item-open")[0];t&&r&&(t.className=t.className.replace("submenu-open",""),r.className=r.className.replace("menu-item-open",""))}})))}},61:(e,t,n)=>{var r=n(732);e.exports=function(){var e=r(".toplinks");r("#changelist-form").find(".actions").length&&r("#toolbar").addClass("actions-visible"),0===e.children().length&&e.parent().hasClass("xfull")&&e.parent().addClass("hidden")}},412:(e,t,n)=>{var r=n(732),i=n(602),o="https://pypi.org/pypi/django-cms/json",a=365,s=14;function u(e,t){var n=function(e,t){var n,r,i,o=/(\.0)+[^\.]*$/;for(e=(e+"").replace(o,"").split("."),t=(t+"").replace(o,"").split("."),i=Math.min(e.length,t.length),n=0;n<i;n++)if(0!=(r=parseInt(e[n],10)-parseInt(t[n],10)))return r;return e.length-t.length}(e,t);return n>0||0===n&&!!t.match(/[^\.\d]+/)}e.exports=function(){var e=r('meta[name="djangocms_version"]');if(e.length&&!i.get("cms_upgrade_notification_closed_recently")){var t,n=e.attr("content").split("rc")[0],l=r('meta[name="djangocms_version_check_type"]').attr("content");(t={version:n,type:l},r.ajax({url:o,data:t})).done((function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}var t=function(e,t,n){var r,i,o=t.split("rc")[0].split(".");for(var a in e)a.includes("rc")||(i=a.split("."))[0]!==o[0]||"patch"===n&&i[1]!==o[1]||(void 0===r||u(a,r))&&(r=a);if(r)return{version:r,url:"https://github.com/django-cms/django-cms/blob/"+r+"/CHANGELOG.rst"}}(e.releases,n,l);(function(e,t,n){if(void 0!==e){var r=i.get("cms_upgrade_notification_closed");return r&&(r=JSON.parse(r)),(!r||r.type!==n||r.version!==e.version)&&u(e.version,t)}return!1})(t,n,l)&&function(e,t){var n=r(r("#cms-update-notification").html());n.find(".js-latest-version").text(e.version),n.find(".js-release-notes-link").attr("href",e.url),n.find(".close").on("click",(function(r){r.preventDefault(),i.set("cms_upgrade_notification_closed",JSON.stringify({version:e.version,type:t}),{expires:a}),i.set("cms_upgrade_notification_closed_recently",!0,{expires:s}),n.slideUp("fast",(function(){n.remove()}))})),n.prependTo("#content").slideDown("fast")}(t,l)}))}}},212:(e,t,n)=>{var r=n(732);window.jQuery&&window.jQuery.ui&&function(e){function t(e,t){if(!(e.originalEvent.touches.length>1)){e.preventDefault();var n=e.originalEvent.changedTouches[0],r=document.createEvent("MouseEvents");r.initMouseEvent(t,!0,!0,window,1,n.screenX,n.screenY,n.clientX,n.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(r)}}if(e.support.touch="ontouchend"in document,e.support.touch){var n,r=e.ui.mouse.prototype,i=r._mouseInit,o=r._mouseDestroy;r._touchStart=function(e){!n&&this._mouseCapture(e.originalEvent.changedTouches[0])&&(n=!0,this._touchMoved=!1,t(e,"mouseover"),t(e,"mousemove"),t(e,"mousedown"))},r._touchMove=function(e){n&&(this._touchMoved=!0,t(e,"mousemove"))},r._touchEnd=function(e){n&&(t(e,"mouseup"),t(e,"mouseout"),this._touchMoved||t(e,"click"),n=!1)},r._mouseInit=function(){var t=this;t.element.bind({touchstart:e.proxy(t,"_touchStart"),touchmove:e.proxy(t,"_touchMove"),touchend:e.proxy(t,"_touchEnd")}),i.call(t)},r._mouseDestroy=function(){var t=this;t.element.unbind({touchstart:e.proxy(t,"_touchStart"),touchmove:e.proxy(t,"_touchMove"),touchend:e.proxy(t,"_touchEnd")}),o.call(t)}}}(r)}},c={};function d(e){var t=c[e];if(void 0!==t)return t.exports;var n=c[e]={exports:{}};return l[e].call(n.exports,n,n.exports,d),n.exports}e=d(732),t=d(369),n=d(455),r=d(61),i=d(495),o=d(568),a=d(412),s=d(622),u=d(632),d(212),e((function(){t(),n(),r(),i(),o(),a(),s(),u()}))})();