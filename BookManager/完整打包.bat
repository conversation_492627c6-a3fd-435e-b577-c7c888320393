@echo off
chcp 65001
echo ================================================
echo 📚 图书管理系统 - 完整打包工具 v2.0
echo ================================================

echo.
echo 🔍 正在检查环境...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)
echo ✅ Python环境正常

REM 检查PyInstaller是否安装
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyInstaller未安装，正在安装...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
    echo ✅ PyInstaller安装完成
) else (
    echo ✅ PyInstaller已安装
)

echo.
echo 🧹 正在清理旧文件...
if exist "build" (
    rmdir /s /q "build"
    echo ✅ 清理build目录
)
if exist "dist\图书管理系统" (
    rmdir /s /q "dist\图书管理系统"
    echo ✅ 清理旧的打包文件
)

echo.
echo 📦 正在收集静态文件...
python manage.py collectstatic --noinput
if errorlevel 1 (
    echo ❌ 静态文件收集失败
    pause
    exit /b 1
)
echo ✅ 静态文件收集完成

echo.
echo 🔨 正在执行打包...
pyinstaller library_system.spec --clean --noconfirm
if errorlevel 1 (
    echo ❌ 打包失败！
    echo 请检查错误信息并重试
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo 📁 正在创建目录结构...
mkdir "dist\图书管理系统\data" 2>nul
mkdir "dist\图书管理系统\media\covers" 2>nul
mkdir "dist\图书管理系统\weekly_backups" 2>nul
mkdir "dist\图书管理系统\backup_logs" 2>nul
mkdir "dist\图书管理系统\restore_backups" 2>nul
mkdir "dist\图书管理系统\database_backups" 2>nul
echo ✅ 目录结构创建完成

echo.
echo 📋 正在复制数据文件...

REM 复制数据库文件
if exist "db.sqlite3" (
    copy "db.sqlite3" "dist\图书管理系统\data\" >nul
    echo ✅ 复制数据库文件
) else (
    echo ⚠️ 数据库文件不存在，将在首次运行时创建
)

REM 复制媒体文件
if exist "media" (
    xcopy "media" "dist\图书管理系统\media" /E /I /Y >nul
    echo ✅ 复制媒体文件
) else (
    echo ⚠️ 媒体文件目录不存在
)

REM 复制备份文件
if exist "weekly_backups" (
    xcopy "weekly_backups" "dist\图书管理系统\weekly_backups" /E /I /Y >nul
    echo ✅ 复制备份文件
)

if exist "database_backups" (
    xcopy "database_backups" "dist\图书管理系统\database_backups" /E /I /Y >nul
    echo ✅ 复制数据库备份
)

echo.
echo 📄 正在复制文档和脚本...
copy "启动图书管理系统.bat" "dist\图书管理系统\" >nul
copy "使用说明.txt" "dist\图书管理系统\" >nul
copy "requirements_fixed.txt" "dist\图书管理系统\requirements.txt" >nul
echo ✅ 文档复制完成

echo.
echo 🧪 正在验证打包结果...
if not exist "dist\图书管理系统\图书管理系统.exe" (
    echo ❌ 可执行文件未生成
    pause
    exit /b 1
)

if not exist "dist\图书管理系统\_internal" (
    echo ❌ 内部文件目录缺失
    pause
    exit /b 1
)

echo ✅ 打包验证通过

echo.
echo 📊 正在生成打包报告...
echo 图书管理系统打包报告 > "dist\图书管理系统\打包报告.txt"
echo ======================== >> "dist\图书管理系统\打包报告.txt"
echo 打包时间：%date% %time% >> "dist\图书管理系统\打包报告.txt"
echo 打包版本：v2.0 >> "dist\图书管理系统\打包报告.txt"
echo Python版本：>> "dist\图书管理系统\打包报告.txt"
python --version >> "dist\图书管理系统\打包报告.txt"
echo. >> "dist\图书管理系统\打包报告.txt"
echo 包含的文件： >> "dist\图书管理系统\打包报告.txt"
dir "dist\图书管理系统" /B >> "dist\图书管理系统\打包报告.txt"

echo.
echo ================================================
echo ✅ 打包完成！
echo ================================================
echo.
echo 📁 打包文件位置：dist\图书管理系统\
echo 🚀 可执行文件：dist\图书管理系统\图书管理系统.exe
echo 🎯 启动脚本：dist\图书管理系统\启动图书管理系统.bat
echo 📖 使用说明：dist\图书管理系统\使用说明.txt
echo 📊 打包报告：dist\图书管理系统\打包报告.txt
echo.
echo 💡 使用提示：
echo - 可以将整个"图书管理系统"文件夹复制到任何Windows电脑
echo - 双击"启动图书管理系统.bat"即可运行
echo - 数据库和媒体文件已外部化，便于备份和迁移
echo - 支持多语言界面（中文、英文、日语、韩语、德语）
echo.
echo 🎉 打包成功！您的图书管理系统已准备就绪。
echo.
pause
