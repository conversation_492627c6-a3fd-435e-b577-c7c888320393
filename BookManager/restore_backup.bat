@echo off
REM 数据库恢复批处理文件

echo ========================================
echo 图书管理系统 - 数据库恢复工具
echo 开始时间: %date% %time%
echo ========================================

REM 切换到项目目录
cd /d "C:\Users\<USER>\PycharmProjects\BookManager"

REM 激活虚拟环境（如果使用的话）
REM call venv\Scripts\activate

REM 执行恢复脚本（交互式模式）
python restore_backup_script.py

echo ========================================
echo 恢复完成时间: %date% %time%
echo ========================================

pause
