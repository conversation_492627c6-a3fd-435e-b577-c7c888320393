📚 图书管理系统 - 使用说明
========================================

版本信息：
- 系统版本：v2.0
- 构建日期：2025-06-20
- 基于：Django 5.2.1

========================================
🚀 快速开始
========================================

1. 启动系统：
   - 双击"启动图书管理系统.bat"
   - 或直接双击"图书管理系统.exe"

2. 访问地址：
   - 主页：http://127.0.0.1:8000
   - 管理后台：http://127.0.0.1:8000/admin

3. 默认管理员账户：
   - 用户名：admin
   - 密码：admin123

========================================
📁 目录结构说明
========================================

图书管理系统/
├── 图书管理系统.exe          # 主程序
├── 启动图书管理系统.bat      # 启动脚本
├── 使用说明.txt              # 本文件
├── data/                     # 数据库目录
│   └── db.sqlite3           # 数据库文件
├── media/                    # 媒体文件目录
│   └── covers/              # 图书封面
├── weekly_backups/           # 自动备份目录
├── backup_logs/              # 备份日志
└── restore_backups/          # 恢复备份

⚠️ 重要提示：
- 请勿删除data、media、weekly_backups等目录
- 这些目录包含重要的数据文件
- 删除可能导致数据丢失

========================================
🌟 主要功能
========================================

📖 图书管理：
- 添加、编辑、删除图书信息
- 图书分类管理
- 图书封面上传
- 库存数量管理
- 图书搜索和筛选

👥 用户管理：
- 学生信息管理
- 用户权限控制
- 批量导入用户

📋 借阅管理：
- 图书借阅和归还
- 借阅记录查询
- 超期提醒
- 借阅统计

🔧 系统管理：
- 数据库自动备份
- 数据库恢复功能
- 通告栏管理
- 系统设置

🌍 多语言支持：
- 中文（简体）
- English
- 日本語
- 한국어
- Deutsch

========================================
⚙️ 系统要求
========================================

操作系统：
- Windows 7 及以上版本
- 64位系统

硬件要求：
- 内存：至少 2GB RAM
- 硬盘：至少 500MB 可用空间
- 网络：无需联网（本地运行）

端口要求：
- 8000端口未被占用
- 如端口被占用，程序会自动尝试其他端口

========================================
🔧 常见问题解决
========================================

Q: 程序无法启动？
A: 1. 检查8000端口是否被占用
   2. 确保有足够的磁盘空间
   3. 检查防火墙设置
   4. 以管理员身份运行

Q: 无法访问网页？
A: 1. 确认程序已正常启动
   2. 检查浏览器地址是否正确
   3. 尝试使用 http://localhost:8000

Q: 数据丢失了怎么办？
A: 1. 检查weekly_backups目录中的备份文件
   2. 使用管理后台的数据库恢复功能
   3. 联系技术支持

Q: 语言切换不生效？
A: 1. 刷新浏览器页面
   2. 清除浏览器缓存
   3. 重启程序

Q: 图书封面无法显示？
A: 1. 检查media/covers目录是否存在
   2. 确认图片文件格式正确（支持jpg、png、gif）
   3. 检查文件权限

========================================
🛠️ 高级功能
========================================

数据库备份：
- 系统每周自动备份
- 手动备份：管理后台 → 数据库管理 → 创建备份
- 备份文件保存在weekly_backups目录

数据库恢复：
- 管理后台 → 数据库管理 → 恢复备份
- 选择备份文件进行恢复
- 恢复前会自动创建当前数据备份

用户批量导入：
- 下载Excel模板
- 填写用户信息
- 管理后台上传Excel文件

通告栏管理：
- 左右侧边栏通告
- 支持富文本编辑
- 可控制显示/隐藏

========================================
📞 技术支持
========================================

如遇到问题，请按以下步骤操作：

1. 查看本使用说明
2. 检查backup_logs目录中的日志文件
3. 记录错误信息和操作步骤
4. 联系技术支持

系统信息收集：
- 操作系统版本
- 错误截图
- 日志文件内容
- 问题重现步骤

========================================
📝 更新日志
========================================

v2.0 (2025-06-20):
- 新增多语言支持
- 优化用户界面
- 增强数据库备份功能
- 改进打包部署方式
- 修复已知问题

v1.0:
- 基础图书管理功能
- 用户管理系统
- 借阅管理功能

========================================
⚖️ 许可证信息
========================================

本软件仅供学习和内部使用。
请勿用于商业用途。
保留所有权利。

========================================
