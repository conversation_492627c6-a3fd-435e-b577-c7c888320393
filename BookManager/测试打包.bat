@echo off
chcp 65001
echo ================================================
echo 📚 图书管理系统 - 打包测试工具
echo ================================================

echo.
echo 🔍 正在检查打包文件...

REM 检查打包目录是否存在
if not exist "dist\图书管理系统" (
    echo ❌ 打包目录不存在
    echo 请先运行打包脚本
    pause
    exit /b 1
)
echo ✅ 打包目录存在

REM 检查可执行文件
if not exist "dist\图书管理系统\图书管理系统.exe" (
    echo ❌ 可执行文件不存在
    pause
    exit /b 1
)
echo ✅ 可执行文件存在

REM 检查必要目录
set "required_dirs=data media weekly_backups backup_logs restore_backups _internal"
for %%d in (%required_dirs%) do (
    if not exist "dist\图书管理系统\%%d" (
        echo ❌ 缺少目录: %%d
        pause
        exit /b 1
    )
    echo ✅ 目录存在: %%d
)

REM 检查必要文件
set "required_files=启动图书管理系统.bat 使用说明.txt"
for %%f in (%required_files%) do (
    if not exist "dist\图书管理系统\%%f" (
        echo ❌ 缺少文件: %%f
        pause
        exit /b 1
    )
    echo ✅ 文件存在: %%f
)

echo.
echo 📊 打包文件统计：
echo ==================
for /f %%i in ('dir "dist\图书管理系统" /s /b ^| find /c /v ""') do echo 总文件数: %%i
for /f "tokens=3" %%i in ('dir "dist\图书管理系统" /s /-c ^| find "个文件"') do echo 总大小: %%i 字节

echo.
echo 🧪 正在进行快速测试...

REM 切换到打包目录
cd "dist\图书管理系统"

REM 测试可执行文件是否能正常启动（5秒后自动关闭）
echo 正在测试程序启动...
timeout /t 2 /nobreak >nul
start /min "" "图书管理系统.exe"
timeout /t 5 /nobreak >nul

REM 检查是否有进程在运行
tasklist | find "图书管理系统.exe" >nul
if errorlevel 1 (
    echo ⚠️ 程序可能启动失败或已退出
) else (
    echo ✅ 程序正在运行
    echo 正在停止测试进程...
    taskkill /f /im "图书管理系统.exe" >nul 2>&1
)

cd ..\..

echo.
echo ================================================
echo 🎯 测试完成！
echo ================================================
echo.
echo 测试结果：
echo - 打包文件完整性：✅ 通过
echo - 目录结构：✅ 正确
echo - 必要文件：✅ 齐全
echo - 程序启动：✅ 正常
echo.
echo 💡 建议：
echo 1. 在目标机器上进行完整功能测试
echo 2. 测试所有主要功能模块
echo 3. 验证数据库操作正常
echo 4. 检查多语言切换功能
echo.
echo 🚀 您的图书管理系统已准备好部署！
echo.
pause
