import os
import sys
import subprocess
import threading
import time
import psutil
from PIL import Image
import io
import pystray
from pystray import MenuItem as item

if getattr(sys, 'frozen', False):
    BASE_DIR = os.path.dirname(sys.executable)
else:
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))

icon_path = os.path.join(BASE_DIR, "school_logo.ico")
server_script = os.path.join(BASE_DIR, "run_server.py")
server_process = None
tray_icon = None  # 用于后面刷新菜单

def is_server_running():
    global server_process
    return server_process is not None and server_process.poll() is None

def start_server():
    global server_process
    if not is_server_running():
        server_process = subprocess.Popen(
            [sys.executable, server_script],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            cwd=BASE_DIR
        )
    refresh_menu()

def stop_server():
    global server_process
    if is_server_running():
        server_process.terminate()
        server_process.wait()
        server_process = None
    refresh_menu()

def open_browser():
    import webbrowser
    webbrowser.open("http://127.0.0.1:8000")

def quit_app(icon, item):
    stop_server()
    icon.stop()

def get_status_item():
    status = "[状态]：运行中" if is_server_running() else "[状态]：未启动"
    return item(status, None, enabled=False)

def create_menu():
    return pystray.Menu(
        get_status_item(),
        item("启动服务", lambda: start_server()),
        item("停止服务", lambda: stop_server()),
        item("打开网页", open_browser),
        item("退出", quit_app)
    )

def load_icon():
    with open(icon_path, 'rb') as f:
        return Image.open(io.BytesIO(f.read()))

def refresh_menu():
    if tray_icon:
        tray_icon.menu = create_menu()

def run_tray():
    global tray_icon
    image = load_icon()
    tray_icon = pystray.Icon("BookManager", icon=image, title="图书管理系统")
    tray_icon.menu = create_menu()
    tray_icon.run()

if __name__ == "__main__":
    start_server()
    run_tray()
