#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图书管理系统启动器
确保数据库和媒体文件在外部目录
"""

import os
import sys
import django
import webbrowser
import threading
import time
from pathlib import Path

def setup_environment():
    """设置环境变量和路径"""
    # 获取可执行文件所在目录
    if getattr(sys, 'frozen', False):
        # 打包后的exe环境
        base_dir = Path(sys.executable).parent
    else:
        # 开发环境
        base_dir = Path(__file__).parent
    
    # 设置项目路径
    project_dir = base_dir
    
    # 确保外部目录存在
    external_dirs = [
        base_dir / 'data',  # 数据库目录
        base_dir / 'media',  # 媒体文件目录
        base_dir / 'weekly_backups',  # 备份目录
        base_dir / 'backup_logs',  # 备份日志目录
        base_dir / 'restore_backups',  # 恢复备份目录
    ]
    
    for dir_path in external_dirs:
        dir_path.mkdir(exist_ok=True)
    
    # 设置环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BookManager.settings')
    
    # 添加项目路径到Python路径
    if str(project_dir) not in sys.path:
        sys.path.insert(0, str(project_dir))
    
    return base_dir

def check_database(base_dir):
    """检查并初始化数据库"""
    db_path = base_dir / 'data' / 'db.sqlite3'
    
    # 如果数据库不存在，创建初始数据库
    if not db_path.exists():
        print("正在初始化数据库...")
        
        # 设置Django
        django.setup()
        
        # 执行迁移
        from django.core.management import execute_from_command_line
        execute_from_command_line(['manage.py', 'migrate'])
        
        # 创建超级用户（如果需要）
        from django.contrib.auth.models import User
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
            print("已创建管理员账户: admin / admin123")
    
    return str(db_path)

def start_server(port=8000):
    """启动Django服务器"""
    try:
        from django.core.management import execute_from_command_line
        execute_from_command_line(['manage.py', 'runserver', f'127.0.0.1:{port}', '--noreload'])
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        input("按回车键退出...")

def open_browser(port=8000):
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    webbrowser.open(f'http://127.0.0.1:{port}')

def main():
    """主函数"""
    print("=" * 50)
    print("📚 图书管理系统")
    print("=" * 50)
    
    try:
        # 设置环境
        base_dir = setup_environment()
        print(f"工作目录: {base_dir}")
        
        # 设置Django
        django.setup()
        
        # 检查数据库
        db_path = check_database(base_dir)
        print(f"数据库路径: {db_path}")
        
        # 检查端口
        port = 8000
        print(f"启动服务器在端口: {port}")
        
        # 在后台线程中打开浏览器
        browser_thread = threading.Thread(target=open_browser, args=(port,))
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n正在启动服务器...")
        print("服务器启动后将自动打开浏览器")
        print("按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        # 启动服务器
        start_server(port)
        
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
