# start_library.py

import os
import sys
import django
import webbrowser
import threading
import time
from django.core.management import execute_from_command_line

# 打开默认浏览器首页
def open_browser():
    time.sleep(1)  # 等待 Django 启动
    webbrowser.open("http://127.0.0.1:8000/")

# 启动浏览器线程
threading.Thread(target=open_browser).start()

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BookManager.settings')
django.setup()

# 启动 Django 开发服务器（加 --noreload 避免 PyInstaller 出错）
execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000', '--noreload'])
