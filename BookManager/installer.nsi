!define APP_NAME "BookManager"
!define EXE_NAME "tray_app.exe"
!define INSTALL_DIR "$PROGRAMFILES\${APP_NAME}"
!define ICON "school_logo.ico"

Outfile "BookManagerInstaller.exe"
InstallDir "${INSTALL_DIR}"
RequestExecutionLevel admin
SetCompressor lzma

Page directory
Page instfiles

Section "Install"
  SetOutPath "$INSTDIR"
  File "dist\${EXE_NAME}"
  File "school_logo.ico"

  ; 创建桌面快捷方式
  CreateShortCut "$DESKTOP\图书管理系统.lnk" "$INSTDIR\${EXE_NAME}" "" "$INSTDIR\school_logo.ico"
  ; 创建开始菜单快捷方式
  CreateShortCut "$SMPROGRAMS\图书管理系统.lnk" "$INSTDIR\${EXE_NAME}" "" "$INSTDIR\school_logo.ico"

  ; 注册为开机启动（可选，可注释）
  WriteRegStr HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "${APP_NAME}" "$INSTDIR\${EXE_NAME}"
SectionEnd

Section "Uninstall"
  Delete "$INSTDIR\${EXE_NAME}"
  Delete "$INSTDIR\school_logo.ico"
  Delete "$DESKTOP\图书管理系统.lnk"
  Delete "$SMPROGRAMS\图书管理系统.lnk"
  DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "${APP_NAME}"
  RMDir "$INSTDIR"
SectionEnd
