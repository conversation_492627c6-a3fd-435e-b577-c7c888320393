@echo off
chcp 65001
echo ================================================
echo 📚 图书管理系统 - 快速打包工具
echo ================================================

echo.
echo 正在收集静态文件...
python manage.py collectstatic --noinput

echo.
echo 正在清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist\图书管理系统" rmdir /s /q "dist\图书管理系统"

echo.
echo 正在打包应用程序...
pyinstaller library_system.spec --clean

if errorlevel 1 (
    echo ❌ 打包失败！
    pause
    exit /b 1
)

echo.
echo 正在创建外部目录结构...
mkdir "dist\图书管理系统\data" 2>nul
mkdir "dist\图书管理系统\media" 2>nul
mkdir "dist\图书管理系统\weekly_backups" 2>nul
mkdir "dist\图书管理系统\backup_logs" 2>nul
mkdir "dist\图书管理系统\restore_backups" 2>nul

echo.
echo 正在复制数据文件...
if exist "db.sqlite3" copy "db.sqlite3" "dist\图书管理系统\data\" >nul
if exist "media" xcopy "media" "dist\图书管理系统\media" /E /I /Y >nul
if exist "weekly_backups" xcopy "weekly_backups" "dist\图书管理系统\weekly_backups" /E /I /Y >nul

echo.
echo 正在复制启动脚本...
copy "启动图书管理系统.bat" "dist\图书管理系统\" >nul

echo.
echo 正在创建使用说明...
copy "使用说明.txt" "dist\图书管理系统\使用说明.txt" >nul

echo.
echo ================================================
echo ✅ 打包完成！
echo ================================================
echo.
echo 📁 打包文件位置：dist\图书管理系统\
echo 🚀 可执行文件：dist\图书管理系统\图书管理系统.exe
echo 📖 使用说明：dist\图书管理系统\使用说明.txt
echo.
echo 💡 提示：
echo - 可以将整个"图书管理系统"文件夹复制到任何Windows电脑运行
echo - 数据库和媒体文件已放在外部目录，便于备份和迁移
echo - 双击"启动图书管理系统.bat"即可运行
echo.
pause
