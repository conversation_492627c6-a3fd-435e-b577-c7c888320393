#!/usr/bin/env python
"""
每周自动数据库备份脚本
每周运行一次，自动备份数据库并清理半年以外的旧备份

使用方法：
1. 手动运行: python weekly_backup_script.py
2. 定时任务: 
   - Windows: 使用任务计划程序，每周运行一次
   - Linux/Mac: 使用crontab，每周运行一次
   
示例crontab设置（每周日凌晨2点备份）：
0 2 * * 0 cd /path/to/your/project && python weekly_backup_script.py
"""

import os
import sys
import django
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BookManager.settings')
django.setup()

from django.core.management import call_command


def main():
    """执行每周自动备份"""
    print(f"🚀 开始每周自动备份 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 执行备份命令（包含清理旧备份）
        call_command('weekly_backup')
        
        print("✅ 每周自动备份完成！")
        
        # 记录备份日志
        log_backup_success()
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        log_backup_error(str(e))
        return False
    
    return True


def log_backup_success():
    """记录备份成功日志"""
    log_dir = "backup_logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, "weekly_backup.log")
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] SUCCESS: 每周数据库备份成功\n")


def log_backup_error(error_msg):
    """记录备份错误日志"""
    log_dir = "backup_logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, "weekly_backup.log")
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] ERROR: 备份失败 - {error_msg}\n")


if __name__ == "__main__":
    main()
