# 📋 数据库恢复快速指南

## 🚨 紧急恢复步骤

如果您的数据库出现问题，请按照以下步骤快速恢复：

### 1. 停止Django服务器
```bash
# 在运行Django的终端按 Ctrl+C 停止服务器
```

### 2. 选择恢复方式

#### 方式一：交互式恢复（推荐新手）
```bash
# Windows用户
双击运行 restore_backup.bat

# 或者命令行
python restore_backup_script.py
```

#### 方式二：命令行恢复（推荐有经验用户）
```bash
# 查看可用备份
ls weekly_backups/

# 恢复最新的JSON备份（推荐）
python manage.py restore_backup weekly_backups/weekly_data_最新时间戳.json --backup-current

# 或恢复SQLite备份
python manage.py restore_backup weekly_backups/weekly_backup_最新时间戳.db --backup-current
```

### 3. 重启Django服务器
```bash
python manage.py runserver
```

## 📁 备份文件说明

### 文件类型
- **`.json` 文件**：数据导出文件，推荐用于恢复
- **`.db` 文件**：SQLite数据库文件，直接替换数据库

### 文件命名
- `weekly_data_YYYYMMDD_HHMMSS.json` - JSON数据备份
- `weekly_backup_YYYYMMDD_HHMMSS.db` - SQLite数据库备份

## 🔧 恢复选项说明

### 命令参数
- `--backup-current`：恢复前先备份当前数据库
- `--force`：强制恢复，不询问确认

### 示例命令
```bash
# 安全恢复（推荐）
python manage.py restore_backup weekly_backups/weekly_data_20250615_221327.json --backup-current

# 快速恢复
python manage.py restore_backup weekly_backups/weekly_data_20250615_221327.json --force

# 完整恢复
python manage.py restore_backup weekly_backups/weekly_data_20250615_221327.json --backup-current --force
```

## ⚠️ 重要提醒

1. **数据安全**：恢复会完全替换当前数据，请谨慎操作
2. **备份当前**：建议使用 `--backup-current` 参数
3. **服务器状态**：恢复前请停止Django服务器
4. **重启必要**：恢复后必须重启Django服务器

## 🆘 常见问题

### Q: 恢复失败怎么办？
A: 检查备份文件是否存在，确保Django服务器已停止

### Q: 恢复后数据不对？
A: 检查是否选择了正确的备份文件，查看备份时间

### Q: 恢复后无法启动？
A: 检查数据库文件权限，尝试重新运行迁移命令

### Q: 如何选择备份文件？
A: 优先选择JSON文件，选择最接近问题发生前的时间点

## 📞 技术支持

如果遇到恢复问题，请：
1. 查看错误信息
2. 检查备份文件完整性
3. 确认Django环境配置
4. 联系系统管理员

## 🔄 恢复后检查清单

- [ ] Django服务器正常启动
- [ ] 网站可以正常访问
- [ ] 用户可以正常登录
- [ ] 数据显示正确
- [ ] 功能运行正常
