#!/usr/bin/env python
"""
数据库恢复脚本
用于从备份文件恢复数据库

使用方法：
python restore_backup_script.py [备份文件路径] [选项]

示例：
python restore_backup_script.py weekly_backups/weekly_data_20250615_221327.json
python restore_backup_script.py weekly_backups/weekly_backup_20250615_221327.db --backup-current
"""

import os
import sys
import django
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'BookManager.settings')
django.setup()

from django.core.management import call_command


def list_available_backups():
    """列出可用的备份文件"""
    backup_dir = 'weekly_backups'
    
    if not os.path.exists(backup_dir):
        print("❌ 备份目录不存在")
        return []
    
    backup_files = []
    for filename in sorted(os.listdir(backup_dir), reverse=True):
        if filename.endswith('.json') or filename.endswith('.db'):
            file_path = os.path.join(backup_dir, filename)
            file_size = os.path.getsize(file_path)
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            backup_files.append({
                'filename': filename,
                'path': file_path,
                'size': f"{file_size / (1024 * 1024):.2f} MB",
                'time': file_time.strftime('%Y-%m-%d %H:%M:%S'),
                'type': 'JSON数据' if filename.endswith('.json') else 'SQLite数据库'
            })
    
    return backup_files


def show_backup_list():
    """显示备份文件列表"""
    print("📋 可用的备份文件:")
    print("=" * 80)
    
    backup_files = list_available_backups()
    
    if not backup_files:
        print("❌ 没有找到备份文件")
        return None
    
    for i, backup in enumerate(backup_files, 1):
        print(f"{i:2d}. {backup['filename']}")
        print(f"    类型: {backup['type']}")
        print(f"    大小: {backup['size']}")
        print(f"    时间: {backup['time']}")
        print()
    
    return backup_files


def interactive_restore():
    """交互式恢复"""
    print("🔄 数据库恢复工具")
    print("=" * 50)
    
    backup_files = show_backup_list()
    
    if not backup_files:
        return
    
    try:
        choice = input("请选择要恢复的备份文件编号 (输入 q 退出): ")
        
        if choice.lower() == 'q':
            print("❌ 恢复操作已取消")
            return
        
        choice_num = int(choice)
        if 1 <= choice_num <= len(backup_files):
            selected_backup = backup_files[choice_num - 1]
            
            print(f"\n📁 选择的备份文件: {selected_backup['filename']}")
            print(f"📊 文件大小: {selected_backup['size']}")
            print(f"🕒 备份时间: {selected_backup['time']}")
            
            # 询问是否备份当前数据库
            backup_current = input("\n💾 是否在恢复前备份当前数据库？ (y/n): ").lower() == 'y'
            
            # 执行恢复
            restore_database(selected_backup['path'], backup_current)
            
        else:
            print("❌ 无效的选择")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except KeyboardInterrupt:
        print("\n❌ 恢复操作已取消")


def restore_database(backup_file, backup_current=False):
    """恢复数据库"""
    try:
        args = [backup_file]
        if backup_current:
            args.append('--backup-current')
        
        call_command('restore_backup', *args)
        
        print("\n✅ 数据库恢复成功！")
        print("⚠️  请重启Django服务器以使更改生效")
        
    except Exception as e:
        print(f"❌ 恢复失败: {e}")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        backup_file = sys.argv[1]
        backup_current = '--backup-current' in sys.argv
        
        if not os.path.exists(backup_file):
            print(f"❌ 备份文件不存在: {backup_file}")
            return
        
        restore_database(backup_file, backup_current)
    else:
        # 交互式模式
        interactive_restore()


if __name__ == "__main__":
    main()
