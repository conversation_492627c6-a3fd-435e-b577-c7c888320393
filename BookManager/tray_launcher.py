import os
import subprocess
import sys
import time
import threading
import pystray
from pystray import MenuItem as item
from PIL import Image
import webbrowser
import pygetwindow as gw
import psutil

PORT = 8000
MANAGE_PY = os.path.join(os.getcwd(), 'manage.py')
PYTHON_PATH = sys.executable  # 使用当前解释器
server_process = None

def start_server():
    global server_process
    if server_process is None or server_process.poll() is not None:
        server_process = subprocess.Popen(
            [PYTHON_PATH, MANAGE_PY, 'runserver', f'127.0.0.1:{PORT}'],
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
        time.sleep(2)
        webbrowser.open(f"http://127.0.0.1:{PORT}")

def stop_server():
    global server_process
    if server_process and server_process.poll() is None:
        parent = psutil.Process(server_process.pid)
        for child in parent.children(recursive=True):
            child.terminate()
        parent.terminate()
        server_process = None

def show_console():
    try:
        win_list = gw.getWindowsWithTitle("manage.py runserver")
        for win in win_list:
            if win.isMinimized:
                win.restore()
            win.activate()
    except Exception as e:
        print("无法找到控制台窗口:", e)

def on_quit(icon, _):
    stop_server()
    icon.stop()
    sys.exit()

def setup_tray():
    image = Image.open("icon.png")
    icon = pystray.Icon("BookManager", image, "BookManager 后台系统", menu=pystray.Menu(
        item('启动服务', lambda: start_server()),
        item('停止服务', lambda: stop_server()),
        item('查看控制台', lambda: show_console()),
        item('退出', on_quit)
    ))
    start_server()  # 启动时默认开启服务
    icon.run()

if __name__ == "__main__":
    threading.Thread(target=setup_tray).start()
