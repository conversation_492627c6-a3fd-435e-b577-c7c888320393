# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = Path(os.getcwd())

# Django相关的隐藏导入
hiddenimports = [
    # Django核心模块
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.core.management',
    'django.core.management.commands',
    'django.core.management.commands.runserver',
    'django.core.management.commands.migrate',
    'django.core.management.commands.collectstatic',
    'django.core.management.commands.makemigrations',
    'django.db.backends.sqlite3',
    'django.template.loaders.filesystem',
    'django.template.loaders.app_directories',
    # 国际化相关
    'django.utils.translation',
    'django.middleware.locale',
    'django.conf.locale',
    # Book应用相关
    'book',
    'book.models',
    'book.views',
    'book.admin',
    'book.urls',
    'book.apps',
    'book.context_processors',
    'book.middleware',
    'book.translations',
    'book.templatetags',
    'book.templatetags.i18n_tags',
    'book.management',
    'book.management.commands',
    'book.management.commands.weekly_backup',
    'book.management.commands.restore_backup',
    'book.signals',
    'book.forms',
    'book.utils',
    # 第三方库
    'import_export',
    'import_export.admin',
    'import_export.resources',
    'import_export.formats',
    'import_export.formats.base_formats',
    'colorfield',
    'colorfield.fields',
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    'pandas',
    'xlsxwriter',
    'PIL',
    'PIL.Image',
    # 系统库
    'webbrowser',
    'threading',
    'sqlite3',
    'json',
    'csv',
    'datetime',
    'pathlib',
]

# 数据文件
datas = [
    # Django模板
    (str(project_root / 'book' / 'templates'), 'book/templates'),
    # 静态文件
    (str(project_root / 'static'), 'static'),
    (str(project_root / 'staticfiles'), 'staticfiles'),
    # 多语言文件
    (str(project_root / 'locale'), 'locale'),
    # Excel模板文件
    (str(project_root / 'book' / 'user_import_template.xlsx'), 'book'),
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy.distutils',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
]

block_cipher = None

a = Analysis(
    ['library_launcher.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='图书管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 显示控制台窗口，方便查看启动信息
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'library.ico') if (project_root / 'library.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='图书管理系统',
)
