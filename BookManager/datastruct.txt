文件夹 PATH 列表
卷序列号为 28AE-2170
C:.
├─.idea
│  └─inspectionProfiles
├─.venv
│  ├─Lib
│  │  └─site-packages
│  │      ├─pip
│  │      │  ├─_internal
│  │      │  │  ├─cli
│  │      │  │  ├─commands
│  │      │  │  ├─distributions
│  │      │  │  ├─index
│  │      │  │  ├─locations
│  │      │  │  ├─metadata
│  │      │  │  │  └─importlib
│  │      │  │  ├─models
│  │      │  │  ├─network
│  │      │  │  ├─operations
│  │      │  │  │  ├─build
│  │      │  │  │  └─install
│  │      │  │  ├─req
│  │      │  │  ├─resolution
│  │      │  │  │  ├─legacy
│  │      │  │  │  └─resolvelib
│  │      │  │  ├─utils
│  │      │  │  └─vcs
│  │      │  └─_vendor
│  │      │      ├─cachecontrol
│  │      │      │  └─caches
│  │      │      ├─certifi
│  │      │      ├─chardet
│  │      │      │  ├─cli
│  │      │      │  └─metadata
│  │      │      ├─colorama
│  │      │      │  └─tests
│  │      │      ├─distlib
│  │      │      ├─distro
│  │      │      ├─idna
│  │      │      ├─msgpack
│  │      │      ├─packaging
│  │      │      ├─pkg_resources
│  │      │      ├─platformdirs
│  │      │      ├─pygments
│  │      │      │  ├─filters
│  │      │      │  ├─formatters
│  │      │      │  ├─lexers
│  │      │      │  └─styles
│  │      │      ├─pyparsing
│  │      │      │  └─diagram
│  │      │      ├─pyproject_hooks
│  │      │      │  └─_in_process
│  │      │      ├─requests
│  │      │      ├─resolvelib
│  │      │      │  └─compat
│  │      │      ├─rich
│  │      │      ├─tenacity
│  │      │      ├─tomli
│  │      │      ├─urllib3
│  │      │      │  ├─contrib
│  │      │      │  │  └─_securetransport
│  │      │      │  ├─packages
│  │      │      │  │  └─backports
│  │      │      │  └─util
│  │      │      └─webencodings
│  │      ├─pip-23.2.1.dist-info
│  │      └─__pycache__
│  └─Scripts
├─book
│  ├─migrations
│  │  └─__pycache__
│  ├─templates
│  │  ├─partials
│  │  └─registration
│  └─__pycache__
├─BookManager
│  ├─static
│  └─__pycache__
├─build
│  └─start_library
│      └─localpycs
├─dist
├─media
│  └─covers
├─output
│  └─venv
│      ├─Include
│      ├─Lib
│      │  └─site-packages
│      │      ├─pip
│      │      │  ├─_internal
│      │      │  │  ├─cli
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─commands
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─distributions
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─index
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─locations
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─metadata
│      │      │  │  │  ├─importlib
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─models
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─network
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─operations
│      │      │  │  │  ├─build
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  ├─install
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─req
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─resolution
│      │      │  │  │  ├─legacy
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  ├─resolvelib
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─utils
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─vcs
│      │      │  │  │  └─__pycache__
│      │      │  │  └─__pycache__
│      │      │  ├─_vendor
│      │      │  │  ├─cachecontrol
│      │      │  │  │  ├─caches
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─certifi
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─distlib
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─distro
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─idna
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─msgpack
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─packaging
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─pkg_resources
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─platformdirs
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─pygments
│      │      │  │  │  ├─filters
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  ├─formatters
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  ├─lexers
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  ├─styles
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─pyproject_hooks
│      │      │  │  │  ├─_in_process
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─requests
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─resolvelib
│      │      │  │  │  ├─compat
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─rich
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─tomli
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─truststore
│      │      │  │  │  └─__pycache__
│      │      │  │  ├─urllib3
│      │      │  │  │  ├─contrib
│      │      │  │  │  │  ├─_securetransport
│      │      │  │  │  │  │  └─__pycache__
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  ├─packages
│      │      │  │  │  │  ├─backports
│      │      │  │  │  │  │  └─__pycache__
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  ├─util
│      │      │  │  │  │  └─__pycache__
│      │      │  │  │  └─__pycache__
│      │      │  │  └─__pycache__
│      │      │  └─__pycache__
│      │      └─pip-24.3.1.dist-info
│      └─Scripts
└─static
    ├─admin
    │  ├─css
    │  │  └─vendor
    │  │      └─select2
    │  ├─img
    │  │  └─gis
    │  └─js
    │      ├─admin
    │      └─vendor
    │          ├─jquery
    │          ├─select2
    │          │  └─i18n
    │          └─xregexp
    ├─books
    ├─cms
    │  ├─css
    │  │  └─4.1.1
    │  ├─fonts
    │  │  ├─4.1.1
    │  │  └─src
    │  ├─images
    │  ├─img
    │  │  ├─icons
    │  │  │  ├─filetypes
    │  │  │  └─plugins
    │  │  ├─pagetree
    │  │  └─toolbar
    │  ├─js
    │  │  ├─admin
    │  │  ├─dist
    │  │  │  └─4.1.1
    │  │  ├─libs
    │  │  │  └─jstree
    │  │  ├─modules
    │  │  │  └─shortcuts
    │  │  ├─polyfills
    │  │  ├─select2
    │  │  └─widgets
    │  └─sass
    │      ├─components
    │      │  └─pagetree
    │      ├─libs
    │      ├─mixins
    │      └─settings
    ├─css
    ├─djangocms_admin_style
    │  ├─css
    │  ├─fonts
    │  │  └─src
    │  ├─img
    │  └─js
    │      ├─dist
    │      ├─libs
    │      └─modules
    ├─djangocms_alias
    │  └─js
    │      └─dist
    ├─djangocms_frontend
    │  ├─css
    │  ├─icon
    │  │  └─vendor
    │  │      └─assets
    │  │          ├─fonts
    │  │          ├─icons-libraries
    │  │          ├─images
    │  │          ├─js
    │  │          ├─scss
    │  │          └─stylesheets
    │  └─js
    ├─djangocms_googlemap
    │  └─js
    ├─djangocms_text
    │  ├─bundles
    │  ├─css
    │  ├─js
    │  └─vendor
    │      └─ckeditor4
    │          ├─adapters
    │          ├─lang
    │          ├─plugins
    │          │  ├─a11yhelp
    │          │  │  └─dialogs
    │          │  │      └─lang
    │          │  ├─about
    │          │  │  └─dialogs
    │          │  │      └─hidpi
    │          │  ├─clipboard
    │          │  │  └─dialogs
    │          │  ├─colordialog
    │          │  │  └─dialogs
    │          │  ├─copyformatting
    │          │  │  ├─cursors
    │          │  │  └─styles
    │          │  ├─dialog
    │          │  │  └─styles
    │          │  ├─div
    │          │  │  └─dialogs
    │          │  ├─exportpdf
    │          │  │  └─tests
    │          │  │      ├─manual
    │          │  │      │  └─integrations
    │          │  │      └─_helpers
    │          │  ├─find
    │          │  │  └─dialogs
    │          │  ├─forms
    │          │  │  ├─dialogs
    │          │  │  └─images
    │          │  ├─iframe
    │          │  │  ├─dialogs
    │          │  │  └─images
    │          │  ├─image
    │          │  │  ├─dialogs
    │          │  │  └─images
    │          │  ├─link
    │          │  │  ├─dialogs
    │          │  │  └─images
    │          │  │      └─hidpi
    │          │  ├─liststyle
    │          │  │  └─dialogs
    │          │  ├─magicline
    │          │  │  └─images
    │          │  │      └─hidpi
    │          │  ├─pagebreak
    │          │  │  └─images
    │          │  ├─pastefromgdocs
    │          │  │  └─filter
    │          │  ├─pastefromlibreoffice
    │          │  │  └─filter
    │          │  ├─pastefromword
    │          │  │  └─filter
    │          │  ├─pastetools
    │          │  │  └─filter
    │          │  ├─preview
    │          │  │  ├─images
    │          │  │  └─styles
    │          │  ├─scayt
    │          │  │  ├─dialogs
    │          │  │  └─skins
    │          │  │      └─moono-lisa
    │          │  ├─showblocks
    │          │  │  └─images
    │          │  ├─smiley
    │          │  │  ├─dialogs
    │          │  │  └─images
    │          │  ├─specialchar
    │          │  │  └─dialogs
    │          │  │      └─lang
    │          │  ├─table
    │          │  │  └─dialogs
    │          │  ├─tableselection
    │          │  │  └─styles
    │          │  ├─tabletools
    │          │  │  └─dialogs
    │          │  ├─templates
    │          │  │  ├─dialogs
    │          │  │  └─templates
    │          │  │      └─images
    │          │  ├─widget
    │          │  │  └─images
    │          │  └─wsc
    │          │      ├─dialogs
    │          │      └─skins
    │          │          └─moono-lisa
    │          └─skins
    │              └─moono-lisa
    │                  └─images
    │                      └─hidpi
    ├─djangocms_text_ckeditor
    │  ├─ckeditor
    │  │  └─icon
    │  ├─icons
    │  └─js
    ├─djangocms_versioning
    │  ├─css
    │  ├─js
    │  │  ├─admin
    │  │  ├─dist
    │  │  └─libs
    │  │      └─api
    │  └─svg
    ├─filer
    │  ├─css
    │  │  └─maps
    │  ├─fonts
    │  │  └─src
    │  ├─icons
    │  ├─img
    │  └─js
    │      ├─addons
    │      └─libs
    ├─js
    ├─parler
    │  └─admin
    ├─sortedm2m
    └─treebeard
