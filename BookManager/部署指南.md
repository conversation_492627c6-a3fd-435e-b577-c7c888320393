# 📚 图书管理系统 - 部署指南

## 🎯 概述

本指南将帮助您将图书管理系统打包并部署到目标机器上。系统采用Django框架开发，使用PyInstaller打包成独立的可执行文件。

## 📋 系统要求

### 开发环境要求
- Windows 7/8/10/11 (64位)
- Python 3.8+ 
- 至少 4GB RAM
- 至少 2GB 可用磁盘空间

### 目标机器要求
- Windows 7/8/10/11 (64位)
- 至少 2GB RAM
- 至少 500MB 可用磁盘空间
- 8000端口可用（或其他可用端口）

## 🔧 打包步骤

### 1. 环境准备

```bash
# 确保所有依赖已安装
pip install -r requirements_fixed.txt

# 收集静态文件
python manage.py collectstatic --noinput

# 确保数据库是最新的
python manage.py migrate
```

### 2. 执行打包

#### 方法一：使用完整打包脚本（推荐）
```bash
完整打包.bat
```

#### 方法二：使用快速打包脚本
```bash
快速打包.bat
```

#### 方法三：手动打包
```bash
pyinstaller library_system.spec --clean --noconfirm
```

### 3. 验证打包结果

```bash
测试打包.bat
```

## 📁 打包输出结构

```
dist/图书管理系统/
├── 图书管理系统.exe          # 主程序
├── 启动图书管理系统.bat      # 启动脚本
├── 使用说明.txt              # 用户手册
├── 打包报告.txt              # 打包信息
├── requirements.txt          # 依赖列表
├── _internal/                # 程序内部文件
├── data/                     # 数据库目录
│   └── db.sqlite3           # 数据库文件（如果存在）
├── media/                    # 媒体文件
│   └── covers/              # 图书封面
├── weekly_backups/           # 自动备份
├── backup_logs/              # 备份日志
├── restore_backups/          # 恢复备份
└── database_backups/         # 数据库备份
```

## 🚀 部署步骤

### 1. 准备部署包

1. 完成打包后，整个 `dist/图书管理系统/` 文件夹就是完整的部署包
2. 可以压缩成ZIP文件便于传输
3. 确保所有文件和目录都包含在内

### 2. 目标机器部署

1. **复制文件**
   ```
   将整个"图书管理系统"文件夹复制到目标机器
   建议放在 C:\Program Files\ 或用户目录下
   ```

2. **设置权限**
   ```
   确保程序有读写data、media等目录的权限
   如需要，以管理员身份运行
   ```

3. **启动程序**
   ```
   双击"启动图书管理系统.bat"
   或直接运行"图书管理系统.exe"
   ```

### 3. 首次运行配置

1. **数据库初始化**
   - 如果没有现有数据库，程序会自动创建
   - 默认管理员账户：admin / admin123

2. **访问系统**
   - 主页：http://127.0.0.1:8000
   - 管理后台：http://127.0.0.1:8000/admin

3. **基础配置**
   - 修改管理员密码
   - 配置系统设置
   - 添加初始数据

## 🔧 高级配置

### 端口配置

如果8000端口被占用，可以修改启动脚本：

```batch
# 编辑 启动图书管理系统.bat
# 在启动命令后添加端口参数
"图书管理系统.exe" --port 8080
```

### 数据迁移

从旧系统迁移数据：

1. **备份旧数据**
   ```
   复制旧系统的data目录
   复制旧系统的media目录
   ```

2. **恢复到新系统**
   ```
   替换新系统的data目录
   替换新系统的media目录
   ```

### 网络配置

如需局域网访问：

1. 修改Django设置中的ALLOWED_HOSTS
2. 配置防火墙允许端口访问
3. 使用机器IP地址访问

## 🛠️ 故障排除

### 常见问题

1. **程序无法启动**
   - 检查端口占用：`netstat -an | find "8000"`
   - 检查权限：以管理员身份运行
   - 查看错误日志

2. **数据库错误**
   - 检查data目录权限
   - 尝试删除db.sqlite3重新初始化
   - 使用备份恢复

3. **静态文件无法加载**
   - 检查_internal目录完整性
   - 重新打包程序
   - 检查防火墙设置

### 日志查看

- 程序运行日志：控制台输出
- 备份日志：backup_logs目录
- Django日志：程序内部日志

## 📊 性能优化

### 打包优化

1. **减小包大小**
   ```python
   # 在library_system.spec中添加更多排除项
   excludes = [
       'tkinter', 'matplotlib', 'scipy', 
       'IPython', 'jupyter', 'notebook',
       'test', 'tests', 'unittest'
   ]
   ```

2. **启用UPX压缩**
   ```python
   # 在spec文件中
   upx=True
   ```

### 运行时优化

1. **数据库优化**
   - 定期清理日志
   - 优化查询语句
   - 建立适当索引

2. **文件管理**
   - 定期清理备份文件
   - 压缩旧的媒体文件
   - 监控磁盘空间

## 🔐 安全考虑

### 数据安全

1. **定期备份**
   - 启用自动备份功能
   - 手动备份重要数据
   - 异地备份存储

2. **访问控制**
   - 修改默认密码
   - 设置强密码策略
   - 限制管理员权限

### 系统安全

1. **网络安全**
   - 仅在必要时开放网络访问
   - 使用防火墙保护
   - 定期更新系统

2. **文件安全**
   - 设置适当的文件权限
   - 防止未授权访问
   - 监控异常活动

## 📞 技术支持

如遇到问题，请提供以下信息：

1. 操作系统版本
2. 错误截图或日志
3. 问题重现步骤
4. 系统配置信息

---

**版本**: v2.0  
**更新日期**: 2025-06-20  
**适用系统**: Windows 7/8/10/11
