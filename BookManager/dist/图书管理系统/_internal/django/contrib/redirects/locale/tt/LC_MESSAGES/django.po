# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tatar (http://www.transifex.com/django/django/language/tt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tt\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Redirects"
msgstr ""

msgid "site"
msgstr ""

msgid "redirect from"
msgstr "бу сәхифәдән юнәлтергә:"

msgid ""
"This should be an absolute path, excluding the domain name. Example: '/"
"events/search/'."
msgstr ""
"Бу домен исеменнән тыш абсолүт юл булырга тиеш. Мәсәлән: '/events/search/'."

msgid "redirect to"
msgstr "бу сәхифәгә юнәлтергә:"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"'http://'."
msgstr ""
"Бу я абсолүт юл (өстәге кебек), я 'http://' белән башланган тулы URL булырга "
"тиеш."

msgid "redirect"
msgstr "юнәлтү"

msgid "redirects"
msgstr "юнәлтүләр"
