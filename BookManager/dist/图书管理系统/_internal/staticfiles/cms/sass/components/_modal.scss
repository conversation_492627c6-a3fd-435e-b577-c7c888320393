//##############################################################################
// MODAL

@use "sass:math";

// Editing plugins in frontend
.cms-modal {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    overflow: hidden;
    z-index: z(modal, base);
    border-radius: $modal-border-radius;
    background: $modal-bgcolor;
    box-shadow: $modal-shadow;
    user-select: none;
    opacity: 0;
    transform: translate3d(0, -10%, 0);
    transition: transform 150ms, opacity 150ms;

    @at-root .cms-structure-mode-structure & {
        transform: translate3d(10%, 0, 0);
    }

    .cms-modal-maximized & {
        right: 0;
        bottom: 0;
        top: 0 !important;
        left: 0 !important;
        border-radius: 0;
        margin: 0 !important;
        width: auto !important;
        height: auto !important;

        .cms-modal-title {
            // set correct cursor when maximized #3111
            cursor: default;
        }
    }

    .cms-modal-minimized & {
        width: auto !important;
        height: auto !important;
        top: 1px !important;
        margin: 0 !important;

        .cms-modal-body,
        .cms-modal-breadcrumb,
        .cms-modal-foot {
            display: none !important;
        }
        .cms-modal-title {
            // set correct cursor when maximized #3111
            cursor: default;
            padding-inline-end: $modal-header-button-area-size * 3;
        }
        .cms-modal-title-suffix {
            display: none;
        }
        .cms-modal-minimize {
            inset-inline-end: $modal-header-button-area-size + $padding-base;
        }
    }
}
.cms-modal-morphing {
    transition: all $speed-base;
}
.cms-modal-open {
    opacity: 1;
    @at-root .cms-structure-mode-structure &,
    & {
        transform: translate3d(0, 0, 0);
    }
}
.cms-modal-body {
    position: absolute;
    z-index: z(modal, body);
    left: 0;
    top: $modal-header-height;
    right: 0;
    bottom: $modal-footer-height;
    border-top: 1px solid $gray-lighter;
    background: $white;
    border-bottom: 1px solid $gray-lighter;
}

.cms-modal-foot {
    position: absolute;
    overflow: hidden;
    clear: both;
    height: $modal-footer-height;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: z(modal, footer);
}
// this elements shows up on top of an iframe as soon as the dragging starts
// so if you are moving the mouse really fast and mouse hovers over an iframe
// mouse events do not go to the iframe but keep firing in the parent document
.cms-modal-shim {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: z(modal, shim);
    width: 100%;
    height: 100%;
}
.cms-modal-frame {
    position: relative;
    z-index: z(modal, frame);
    width: 100%;
    height: 100%;
    // forces things on touch devices like iPad
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
}
.cms-modal-frame iframe {
    display: block;
    // starting from iOS 4.something iframe size is forced
    // to be equal to it's contents height, so these are not really
    // respected
    width: 100%;
    height: 100%;
}

.cms-modal-head {
    position: relative;
}
.cms-modal-title {
    display: block;
    color: $gray-darker;
    font-size: $modal-header-title-font-size;
    font-weight: bold;
    line-height: $modal-header-height;
    min-height: $modal-header-height;
    padding: 0 $padding-large;
    cursor: move;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-inline-end: $modal-header-button-area-size * 3 + $padding-normal;
    .cms-modal-title-suffix {
        font-weight: normal;
        padding-inline-start: $padding-normal;
    }
}

.cms-modal-minimize,
.cms-modal-close,
.cms-modal-maximize {
    display: block;
    position: absolute;
    top: 50%;
    margin-top: math.div(-$modal-header-button-area-size, 2);
    inset-inline-end: $padding-normal;
    color: $gray-light;
    text-align: center;
    width: $modal-header-button-area-size;
    height: $modal-header-button-area-size;
    cursor: pointer;
    &:before {
        position: relative;
        top: math.div($modal-header-button-area-size - $icon-size, 2);
    }
    &:hover {
        color: $color-primary;
    }
}
.cms-modal-minimize {
    inset-inline-end: 2 * $modal-header-button-area-size + $padding-normal;
    .cms-modal-minimized & {
        @include icon(plus);
        color: $color-primary;
    }
    .cms-modal-maximized & {
        // ensure minimize button is hidden #3111
        display: none !important;
    }
}
.cms-modal-maximize {
    inset-inline-end: $modal-header-button-area-size + $padding-normal;
    // ensure maximize element is hidden #3111
    .cms-modal-minimized & {
        display: none !important;
    }
    .cms-modal-maximized & {
        color: $color-primary;
        @include icon(minimize);
    }
}

.cms-modal-resize {
    position: absolute;
    inset-inline-end: 0;
    bottom: 0;
    z-index: z(modal, resize);
    font-size: 10px;
    color: $gray-light;
    width: $modal-resize-size;
    height: $modal-resize-size;
    cursor: nw-resize;
    &:dir(rtl) {
        cursor: ne-resize;
    }
    span {
        position: absolute;
        bottom: 5px;
        right: 5px;
        font-size: $font-size-small;
        &:dir(rtl) {
            transform: scale(-1,1) translate(2px, 0);  // flip drag triangle
        }
    }
}

// breadcrumb only shows for nested elements
.cms-modal-breadcrumb {
    display: none !important;
    font-size: $font-size-normal;
    line-height: $modal-breadcrumb-height;
    padding: 0 $padding-large;
    border-top: 1px solid $gray-lighter;
    overflow-y: hidden;
    overflow-x: scroll;
    height: $modal-breadcrumb-height * 2; // to account for the scrollbar
    width: 100%;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    a {
        color: $color-primary;
        &:hover {
            filter: brightness(0.8);
        }
        &:after {
            content: "/";
            color: $gray-lighter;
            text-decoration: none;
            padding: 0 $padding-normal;
        }
        &.active {
            color: $gray-light;
        }
        &:last-child:after {
            content: "";
        }
    }
}

// buttons
.cms-modal-buttons {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: z(modal, buttons);
    padding-block: 0;
    padding-inline: $padding-normal $modal-resize-size;
}
.cms-modal-item-buttons {
    $margin: math.div($toolbar-height - $toolbar-button-height, 2);
    @extend .cms-toolbar-item-buttons;
    float: right;
    &:dir(rtl) {
        float: left;
    }
    margin-inline-start: $margin;

    .cancel-link {
        display: none;
    }
}
.cms-modal-item-buttons-left {
    float: left;
    &:dir(rtl) {
        float: right;
    }
}

// alter footer when html markup is loaded
.cms-modal-markup {
    .cms-modal-foot {
        height: math.div($modal-footer-height, 2);
    }
    .cms-modal-body {
        bottom: math.div($modal-footer-height, 2);
    }
}

.cms-modal-has-breadcrumb {
    .cms-modal-body {
        top: $modal-header-height + $modal-breadcrumb-height !important;
    }
    .cms-modal-breadcrumb {
        display: block !important;
    }
}

// placed outside the cms-toolbar scope
@at-root .cms-modal-maximized {
    overflow: hidden !important;
    .cms.cms-toolbar-debug .cms-modal {
        top: $toolbar-debug-height !important;
    }
}
@at-root .cms-modal-minimized {
    .cms.cms-toolbar-debug .cms-modal {
        top: $toolbar-debug-height + 1px !important;
    }
}
