import pystray
from PIL import Image, ImageDraw

def create_image():
    size = (64, 64)
    image = Image.new('RGBA', size, (0, 0, 255, 255))  # 蓝底
    draw = ImageDraw.Draw(image)
    draw.ellipse((16, 16, 48, 48), fill='yellow')      # 黄圆
    return image

def on_exit(icon, item):
    icon.stop()

icon = pystray.Icon(
    'test_icon',
    create_image(),
    '测试托盘图标',
    menu=pystray.Menu(
        pystray.MenuItem('退出', on_exit)
    )
)

icon.run()
