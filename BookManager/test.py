from PyQt5 import QtWidgets, QtGui
import sys
import os

app = QtWidgets.QApplication(sys.argv)

# 图标绝对路径
icon_path = os.path.abspath("school_logo.ico")
print(f"加载图标路径: {icon_path}")
if not os.path.exists(icon_path):
    QtWidgets.QMessageBox.critical(None, "错误", f"找不到图标文件: {icon_path}")
    sys.exit(1)

tray_icon = QtGui.QIcon(icon_path)
tray = QtWidgets.QSystemTrayIcon(tray_icon)
tray.setToolTip("托盘测试图标")
tray.setVisible(True)

menu = QtWidgets.QMenu()
menu.addAction("退出", app.quit)
tray.setContextMenu(menu)

# 保持运行
sys.exit(app.exec_())
