@echo off
REM 每周自动数据库备份批处理文件
REM 用于Windows任务计划程序

echo ========================================
echo 图书管理系统 - 每周数据库备份
echo 开始时间: %date% %time%
echo ========================================

REM 切换到项目目录
cd /d "C:\Users\<USER>\PycharmProjects\BookManager"

REM 激活虚拟环境（如果使用的话）
REM call venv\Scripts\activate

REM 执行备份脚本
python weekly_backup_script.py

echo ========================================
echo 备份完成时间: %date% %time%
echo ========================================

REM 暂停3秒后关闭（可选）
timeout /t 3 /nobreak > nul
