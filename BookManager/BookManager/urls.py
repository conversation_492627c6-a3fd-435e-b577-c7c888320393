import os
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static


admin.site.site_header = "图书管理系统后台"
admin.site.site_title = "图书管理系统管理后台"
admin.site.index_title = "欢迎使用图书管理系统后台"

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('django.contrib.auth.urls')),  # 登录登出
    path('', include('book.urls')),  # 所有 book 应用相关的路由
]

# 静态文件（CSS、JS）
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=os.path.join(settings.BASE_DIR, 'static'))

# 媒体文件（封面图等）
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
