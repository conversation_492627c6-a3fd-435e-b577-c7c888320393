"""
Django settings for BookManager project.

Generated by 'django-admin startproject' using Django 1.11.29.

For more information on this file, see
https://docs.djangoproject.com/en/1.11/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.11/ref/settings/
"""
import mimetypes
mimetypes.add_type("text/css", ".css", True)

import os
from pathlib import Path

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.11/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'pv(i_q1=-a=5)s+6m%(u8rn8gmekp#gq+wj*6$3upko&d!i+9x'

# SECURITY WARNING: don't run with debug turned on in production!
# DEBUG设置移到后面统一配置

# ALLOWED_HOSTS设置移到后面统一配置

import sys
import os

# Application definition

INSTALLED_APPS = [
    # 'jazzmin',  # 添加在最前
    'colorfield',  # 必须放在前面
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'book',
    'import_export',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'book.middleware.CustomLanguageMiddleware',  # 我们的自定义语言中间件
    'django.middleware.locale.LocaleMiddleware',  # Django的语言中间件
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'BookManager.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'book/templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'book.context_processors.advertisements',
                'book.context_processors.language_context',
            ],
        },
    },
]

WSGI_APPLICATION = 'BookManager.wsgi.application'


# Database
# https://docs.djangoproject.com/en/1.11/ref/settings/#databases

# 判断是否是打包后的可执行文件
if getattr(sys, 'frozen', False):
    # 打包后的exe环境
    BASE_DIR = Path(sys.executable).resolve().parent
    # 数据库放在外部data目录
    DB_PATH = os.path.join(BASE_DIR, 'data', 'db.sqlite3')
else:
    # 开发环境
    BASE_DIR = Path(__file__).resolve().parent.parent
    DB_PATH = os.path.join(BASE_DIR, 'db.sqlite3')

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': DB_PATH,
    }
}


# Password validation
# https://docs.djangoproject.com/en/1.11/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/1.11/topics/i18n/


LANGUAGE_CODE = 'zh-hans'  # 中文简体

# 支持的语言列表（Django标准格式）
LANGUAGES = [
    ('zh-hans', '中文'),
    ('en', 'English'),
    ('ja', '日本語'),
    ('ko', '한국어'),
    ('de', 'Deutsch'),
]

# 启用国际化
USE_I18N = True  # 启用Django标准国际化
USE_L10N = True
USE_TZ = True

TIME_ZONE = 'Asia/Shanghai'

# 语言设置
LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale'),
]

# 确保Django能找到语言文件
import django
DJANGO_LOCALE_PATH = os.path.join(os.path.dirname(django.__file__), 'conf', 'locale')

# 强制设置语言环境
import os
os.environ.setdefault('LANG', 'zh_CN.UTF-8')
os.environ.setdefault('LANGUAGE', 'zh_CN:zh:en_US:en')

# 文件编码设置
DEFAULT_CHARSET = 'utf-8'
FILE_CHARSET = 'utf-8'

# Excel导出设置
IMPORT_EXPORT_USE_TRANSACTIONS = True



# LANGUAGE_CODE = 'en-us'
#
# TIME_ZONE = 'UTC'


# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'

# 静态文件配置 - 根据运行环境调整
if getattr(sys, 'frozen', False):
    # 打包后的exe环境
    # 静态文件在_internal目录中
    STATIC_ROOT = os.path.join(os.path.dirname(sys.executable), '_internal', 'staticfiles')
    STATICFILES_DIRS = [
        os.path.join(os.path.dirname(sys.executable), '_internal', 'static'),
    ]
    # Media文件放在外部
    MEDIA_ROOT = os.path.join(os.path.dirname(sys.executable), 'media')
    # 在打包环境中保持DEBUG=True以便提供静态文件服务
    DEBUG = True
else:
    # 开发环境
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    STATICFILES_DIRS = [
        os.path.join(BASE_DIR, 'static'),
    ]
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
    DEBUG = True  # 开发环境开启调试模式

MEDIA_URL = '/media/'

LOGIN_URL = '/accounts/login/'
LOGIN_REDIRECT_URL = '/books/'  # 登录成功后跳转
LOGOUT_REDIRECT_URL = '/books/'  # 登出后跳转

# CSRF和Session配置
CSRF_COOKIE_SECURE = False  # 开发环境设为False
CSRF_COOKIE_HTTPONLY = False
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_TRUSTED_ORIGINS = ['http://127.0.0.1:8000', 'http://localhost:8000']
CSRF_FAILURE_VIEW = 'book.views.csrf_failure'  # 自定义CSRF错误处理

# Session配置
SESSION_COOKIE_SECURE = False  # 开发环境设为False
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_AGE = 86400  # 24小时
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# 允许的主机
ALLOWED_HOSTS = ['127.0.0.1', 'localhost', '*']

# 模板目录配置
TEMPLATES[0]['DIRS'] = [os.path.join(BASE_DIR, 'book', 'templates')]
