# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['debug_final_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('SchoolAcademicManager', 'SchoolAcademicManager'),
        ('django_utils.py', '.'),
    ],
    hiddenimports=[
        'django',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.core.management',
        'django.core.management.commands',
        'django.core.management.commands.runserver',
        'django.apps',
        'django.apps.registry',
        'django.conf',
        'django.urls',
        'django.utils.log',
        'import_export',
        'import_export.admin',
        'import_export.resources',
        'import_export.fields',
        'import_export.widgets',
        'import_export.formats',
        'import_export.formats.base_formats',
        'colorfield',
        'colorfield.fields',
        'openpyxl',
        'xlwt',
        'xlrd',
        'academic',
        'academic.models',
        'academic.views',
        'academic.admin',
        'academic.apps',
        'academic.urls',
        'SchoolAcademicManager',
        'SchoolAcademicManager.settings',
        'SchoolAcademicManager.urls',
        'SchoolAcademicManager.wsgi',
        'django_utils',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='调试版教务管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 显示控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
