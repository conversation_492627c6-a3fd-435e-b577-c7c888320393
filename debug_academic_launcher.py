#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试版中学教务管理系统启动器
包含详细的错误信息和日志
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import webbrowser
import subprocess
import time
import socket

def get_resource_path(relative_path):
    """获取资源文件路径，兼容开发环境和打包后的环境"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

class DebugAcademicLauncher:
    def __init__(self):
        self.server_process = None
        self.root = None
        
    def create_gui(self):
        """创建调试界面"""
        self.root = tk.Tk()
        self.root.title("中学教务管理系统启动器 - 调试版")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="中学教务管理系统 - 调试版", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, pady=(0, 10), sticky=(tk.W, tk.E))
        
        self.check_btn = ttk.Button(button_frame, text="检查环境", 
                                   command=self.check_environment)
        self.check_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.start_btn = ttk.Button(button_frame, text="启动系统", 
                                   command=self.start_system)
        self.start_btn.grid(row=0, column=1, padx=(0, 5))
        
        self.stop_btn = ttk.Button(button_frame, text="停止系统", 
                                  command=self.stop_system, state="disabled")
        self.stop_btn.grid(row=0, column=2, padx=(0, 5))
        
        self.open_btn = ttk.Button(button_frame, text="打开系统", 
                                  command=self.open_system, state="disabled")
        self.open_btn.grid(row=0, column=3, padx=(0, 5))
        
        self.clear_btn = ttk.Button(button_frame, text="清空日志", 
                                   command=self.clear_log)
        self.clear_btn.grid(row=0, column=4)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="系统日志", padding="5")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, sticky=(tk.W, tk.E))
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 自动检查环境
        self.root.after(100, self.check_environment)
        
        return self.root
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        # 替换Unicode字符为ASCII字符，避免编码问题
        safe_message = message.replace("✅", "[OK]").replace("❌", "[ERROR]").replace("⚠️", "[WARN]").replace("🚀", "[START]").replace("🌐", "[WEB]")
        log_message = f"[{timestamp}] {safe_message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        try:
            print(log_message.strip())  # 同时输出到控制台
        except UnicodeEncodeError:
            print(f"[{timestamp}] {safe_message}")  # 备用输出
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def check_environment(self):
        """检查环境"""
        self.log("=== 开始环境检查 ===")
        
        # 基本信息
        self.log(f"Python版本: {sys.version}")
        self.log(f"当前工作目录: {os.getcwd()}")
        self.log(f"程序文件路径: {sys.executable}")
        
        try:
            self.log(f"PyInstaller临时目录: {sys._MEIPASS}")
        except:
            self.log("PyInstaller临时目录: 未找到（开发环境）")
        
        # 检查Django项目
        self.log("\n=== 检查Django项目 ===")
        academic_dir = get_resource_path("SchoolAcademicManager")
        self.log(f"查找目录: {academic_dir}")
        
        if os.path.exists(academic_dir):
            self.log("✅ 找到SchoolAcademicManager目录")
            
            # 检查关键文件
            manage_py = os.path.join(academic_dir, "manage.py")
            if os.path.exists(manage_py):
                self.log("✅ 找到manage.py文件")
            else:
                self.log("❌ 未找到manage.py文件")
            
            settings_py = os.path.join(academic_dir, "SchoolAcademicManager", "settings.py")
            if os.path.exists(settings_py):
                self.log("✅ 找到settings.py文件")
            else:
                self.log("❌ 未找到settings.py文件")
            
            # 列出目录内容
            try:
                files = os.listdir(academic_dir)
                self.log(f"目录内容: {', '.join(files)}")
            except Exception as e:
                self.log(f"无法列出目录内容: {e}")
                
        else:
            self.log("❌ 未找到SchoolAcademicManager目录")
            
            # 尝试其他路径
            possible_paths = [
                "SchoolAcademicManager",
                os.path.join(os.path.dirname(sys.executable), "SchoolAcademicManager"),
                os.path.join(os.getcwd(), "SchoolAcademicManager")
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    self.log(f"✅ 在备用路径找到: {path}")
                    break
            else:
                self.log("❌ 所有路径都未找到Django项目")
        
        # 检查端口
        self.log("\n=== 检查端口状态 ===")
        if check_port(8001):
            self.log("⚠️ 端口8001已被占用")
        else:
            self.log("✅ 端口8001可用")
        
        # 检查Django
        self.log("\n=== 检查Django ===")
        try:
            import django
            self.log(f"✅ Django版本: {django.get_version()}")
        except ImportError:
            self.log("❌ Django未安装")
        
        self.log("=== 环境检查完成 ===\n")
    
    def start_system(self):
        """启动系统"""
        self.log("=== 开始启动系统 ===")
        self.status_var.set("正在启动系统...")
        self.start_btn.config(state="disabled")
        
        def start_server():
            try:
                # 获取Django项目目录
                academic_dir = get_resource_path("SchoolAcademicManager")
                
                if not os.path.exists(academic_dir):
                    possible_paths = [
                        "SchoolAcademicManager",
                        os.path.join(os.path.dirname(sys.executable), "SchoolAcademicManager"),
                        os.path.join(os.getcwd(), "SchoolAcademicManager")
                    ]
                    
                    academic_dir = None
                    for path in possible_paths:
                        if os.path.exists(path):
                            academic_dir = path
                            break
                    
                    if not academic_dir:
                        raise Exception("找不到SchoolAcademicManager目录")
                
                self.root.after(0, lambda: self.log(f"使用Django目录: {academic_dir}"))
                
                # 检查manage.py
                manage_py = os.path.join(academic_dir, "manage.py")
                if not os.path.exists(manage_py):
                    raise Exception(f"找不到manage.py文件: {manage_py}")
                
                self.root.after(0, lambda: self.log("✅ manage.py文件存在"))
                
                # 启动Django服务器
                self.root.after(0, lambda: self.log("启动Django开发服务器..."))
                
                self.server_process = subprocess.Popen([
                    sys.executable, "manage.py", "runserver", "127.0.0.1:8001", "--noreload"
                ], cwd=academic_dir, 
                   stdout=subprocess.PIPE, 
                   stderr=subprocess.STDOUT,  # 合并stderr到stdout
                   universal_newlines=True,
                   creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
                
                # 监控服务器输出
                def monitor_output():
                    while self.server_process and self.server_process.poll() is None:
                        try:
                            line = self.server_process.stdout.readline()
                            if line:
                                self.root.after(0, lambda l=line.strip(): self.log(f"Django: {l}"))
                        except:
                            break
                
                threading.Thread(target=monitor_output, daemon=True).start()
                
                # 等待服务器启动
                self.root.after(0, lambda: self.log("等待服务器启动..."))
                
                for i in range(10):  # 等待最多10秒
                    time.sleep(1)
                    if check_port(8001):
                        self.root.after(0, self.on_server_started)
                        return
                    self.root.after(0, lambda i=i: self.log(f"等待中... ({i+1}/10)"))
                
                # 如果10秒后还没启动成功
                if not check_port(8001):
                    # 检查进程是否还在运行
                    if self.server_process and self.server_process.poll() is not None:
                        # 进程已退出，读取错误信息
                        try:
                            stdout, stderr = self.server_process.communicate(timeout=1)
                            error_msg = f"Django进程退出，返回码: {self.server_process.returncode}"
                            if stdout:
                                error_msg += f"\n输出: {stdout}"
                            if stderr:
                                error_msg += f"\n错误: {stderr}"
                            raise Exception(error_msg)
                        except:
                            raise Exception("Django进程意外退出")
                    else:
                        raise Exception("服务器启动超时，端口8001未响应")

            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda msg=error_msg: self.on_server_error(msg))
        
        threading.Thread(target=start_server, daemon=True).start()
    
    def on_server_started(self):
        """服务器启动成功回调"""
        self.log("✅ Django服务器启动成功！")
        self.log("🌐 系统地址: http://127.0.0.1:8001")
        self.status_var.set("系统已启动 - http://127.0.0.1:8001")
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.open_btn.config(state="normal")
        
        # 自动打开浏览器
        webbrowser.open("http://127.0.0.1:8001")
        self.log("🚀 已自动打开浏览器")
    
    def on_server_error(self, error):
        """服务器启动失败回调"""
        self.log(f"❌ 服务器启动失败: {error}")
        self.status_var.set("启动失败")
        self.start_btn.config(state="normal")
        messagebox.showerror("启动失败", f"系统启动失败：{error}")
    
    def stop_system(self):
        """停止系统"""
        self.log("=== 停止系统 ===")
        if self.server_process:
            self.server_process.terminate()
            self.server_process = None
            self.log("✅ Django服务器已停止")
        
        self.status_var.set("系统已停止")
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.open_btn.config(state="disabled")
    
    def open_system(self):
        """打开系统页面"""
        if check_port(8001):
            webbrowser.open("http://127.0.0.1:8001")
            self.log("🚀 已打开系统页面")
        else:
            self.log("❌ 系统未运行，无法打开")
            messagebox.showwarning("警告", "系统未运行，请先启动系统")
    
    def on_closing(self):
        """关闭程序"""
        if self.server_process:
            self.server_process.terminate()
        self.root.destroy()
    
    def run(self):
        """运行启动器"""
        root = self.create_gui()
        root.mainloop()

def main():
    """主函数"""
    launcher = DebugAcademicLauncher()
    launcher.run()

if __name__ == '__main__':
    main()
