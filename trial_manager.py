#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
试用期管理模块
管理软件的试用期限制和授权验证
"""

import os
import json
import hashlib
import base64
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
import winreg
import uuid

class TrialManager:
    def __init__(self, trial_days=60):
        self.trial_days = trial_days
        self.app_name = "SchoolAcademicManager"
        self.registry_key = r"SOFTWARE\SchoolManagement\Academic"
        self.config_file = os.path.join(os.getenv('APPDATA'), 'SchoolAcademic', 'config.dat')
        self.license_file = os.path.join(os.getenv('APPDATA'), 'SchoolAcademic', 'license.key')
        
        # 确保配置目录存在
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        # 生成机器唯一标识
        self.machine_id = self._get_machine_id()
        
        # 加密密钥（基于机器ID生成）
        self.key = self._generate_key()
        self.cipher = Fernet(self.key)
    
    def _get_machine_id(self):
        """获取机器唯一标识"""
        try:
            # 获取CPU ID
            import wmi
            c = wmi.WMI()
            cpu_id = c.Win32_Processor()[0].ProcessorId.strip()
            
            # 获取主板序列号
            motherboard_id = c.Win32_BaseBoard()[0].SerialNumber.strip()
            
            # 组合生成唯一ID
            machine_info = f"{cpu_id}-{motherboard_id}"
            return hashlib.md5(machine_info.encode()).hexdigest()
        except:
            # 备用方案：使用MAC地址
            mac = uuid.getnode()
            return hashlib.md5(str(mac).encode()).hexdigest()
    
    def _generate_key(self):
        """基于机器ID生成加密密钥"""
        key_material = f"{self.machine_id}-{self.app_name}".encode()
        key_hash = hashlib.sha256(key_material).digest()
        return base64.urlsafe_b64encode(key_hash[:32])
    
    def _write_registry(self, name, value):
        """写入注册表"""
        try:
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, self.registry_key) as key:
                winreg.SetValueEx(key, name, 0, winreg.REG_SZ, value)
            return True
        except:
            return False
    
    def _read_registry(self, name):
        """读取注册表"""
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key) as key:
                value, _ = winreg.QueryValueEx(key, name)
                return value
        except:
            return None
    
    def _encrypt_data(self, data):
        """加密数据"""
        json_data = json.dumps(data)
        encrypted = self.cipher.encrypt(json_data.encode())
        return base64.b64encode(encrypted).decode()
    
    def _decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return json.loads(decrypted.decode())
        except:
            return None
    
    def initialize_trial(self):
        """初始化试用期"""
        now = datetime.now()
        trial_data = {
            'start_date': now.isoformat(),
            'end_date': (now + timedelta(days=self.trial_days)).isoformat(),
            'machine_id': self.machine_id,
            'version': '1.0',
            'install_count': 1
        }
        
        # 保存到文件
        encrypted_data = self._encrypt_data(trial_data)
        try:
            with open(self.config_file, 'w') as f:
                f.write(encrypted_data)
        except:
            pass
        
        # 保存到注册表（备份）
        self._write_registry('trial_info', encrypted_data)
        self._write_registry('install_date', now.isoformat())
        
        return trial_data
    
    def get_trial_info(self):
        """获取试用期信息"""
        # 优先从文件读取
        trial_data = None
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    encrypted_data = f.read()
                trial_data = self._decrypt_data(encrypted_data)
            except:
                pass
        
        # 从注册表读取（备份）
        if not trial_data:
            encrypted_data = self._read_registry('trial_info')
            if encrypted_data:
                trial_data = self._decrypt_data(encrypted_data)
        
        # 验证机器ID
        if trial_data and trial_data.get('machine_id') != self.machine_id:
            return None
        
        return trial_data
    
    def check_trial_status(self):
        """检查试用期状态"""
        trial_data = self.get_trial_info()
        
        if not trial_data:
            # 首次运行，初始化试用期
            trial_data = self.initialize_trial()
        
        now = datetime.now()
        end_date = datetime.fromisoformat(trial_data['end_date'])
        start_date = datetime.fromisoformat(trial_data['start_date'])
        
        if now > end_date:
            return {
                'status': 'expired',
                'message': '试用期已过期',
                'days_remaining': 0,
                'total_days': self.trial_days
            }
        
        days_remaining = (end_date - now).days
        return {
            'status': 'active',
            'message': f'试用期剩余 {days_remaining} 天',
            'days_remaining': days_remaining,
            'total_days': self.trial_days,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        }
    
    def is_trial_valid(self):
        """检查试用期是否有效"""
        status = self.check_trial_status()
        return status['status'] == 'active'
    
    def activate_license(self, license_key):
        """激活许可证"""
        # 这里可以实现许可证验证逻辑
        # 简单示例：检查许可证格式
        if self._validate_license_key(license_key):
            license_data = {
                'license_key': license_key,
                'activated_date': datetime.now().isoformat(),
                'machine_id': self.machine_id,
                'status': 'activated'
            }
            
            encrypted_data = self._encrypt_data(license_data)
            try:
                with open(self.license_file, 'w') as f:
                    f.write(encrypted_data)
                
                self._write_registry('license_status', 'activated')
                return True
            except:
                return False
        return False
    
    def _validate_license_key(self, license_key):
        """验证许可证密钥格式"""
        # 简单的许可证格式验证
        # 实际应用中应该连接服务器验证
        if len(license_key) == 25 and license_key.count('-') == 4:
            parts = license_key.split('-')
            return all(len(part) == 5 and part.isalnum() for part in parts)
        return False
    
    def is_licensed(self):
        """检查是否已激活许可证"""
        if os.path.exists(self.license_file):
            try:
                with open(self.license_file, 'r') as f:
                    encrypted_data = f.read()
                license_data = self._decrypt_data(encrypted_data)
                
                if license_data and license_data.get('machine_id') == self.machine_id:
                    return license_data.get('status') == 'activated'
            except:
                pass
        
        # 检查注册表
        status = self._read_registry('license_status')
        return status == 'activated'
    
    def can_run(self):
        """检查软件是否可以运行"""
        return self.is_licensed() or self.is_trial_valid()
    
    def get_status_message(self):
        """获取状态消息"""
        if self.is_licensed():
            return "软件已激活，感谢您的支持！"
        
        trial_status = self.check_trial_status()
        if trial_status['status'] == 'active':
            return f"试用期剩余 {trial_status['days_remaining']} 天"
        else:
            return "试用期已过期，请购买正式版本"

# 测试函数
def test_trial_manager():
    """测试试用期管理器"""
    tm = TrialManager(trial_days=60)
    
    print("=" * 50)
    print("🔐 试用期管理器测试")
    print("=" * 50)
    
    print(f"机器ID: {tm.machine_id}")
    print(f"配置文件: {tm.config_file}")
    
    status = tm.check_trial_status()
    print(f"试用状态: {status}")
    
    print(f"可以运行: {tm.can_run()}")
    print(f"状态消息: {tm.get_status_message()}")
    
    print("=" * 50)

if __name__ == '__main__':
    test_trial_manager()
