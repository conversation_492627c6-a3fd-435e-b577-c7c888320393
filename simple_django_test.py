#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的Django测试 - 检查Django是否能在打包环境中运行
"""

import os
import sys
import subprocess
import time

# 导入Django工具函数
try:
    from django_utils import (
        get_resource_path, safe_django_setup,
        check_django_dependencies, print_dependency_report,
        test_django_import_export
    )
except ImportError:
    # 如果django_utils不可用，使用简单版本
    def get_resource_path(relative_path):
        """获取资源文件路径"""
        try:
            base_path = sys._MEIPASS
            print(f"打包环境，基础路径: {base_path}")
        except Exception:
            base_path = os.path.abspath(".")
            print(f"开发环境，基础路径: {base_path}")

        return os.path.join(base_path, relative_path)

    def safe_django_setup(project_name='SchoolAcademicManager'):
        """简单的Django初始化"""
        try:
            project_dir = get_resource_path(project_name)
            if project_dir not in sys.path:
                sys.path.insert(0, project_dir)

            os.environ.setdefault('DJANGO_SETTINGS_MODULE', f'{project_name}.settings')

            import django
            from django.conf import settings

            if not settings.configured:
                django.setup()
            return True
        except Exception as e:
            print(f"Django初始化失败: {e}")
            return False

def test_django_import():
    """测试Django导入"""
    print("\n=== 测试Django导入 ===")
    try:
        import django
        print(f"[OK] Django版本: {django.get_version()}")
        return True
    except ImportError as e:
        print(f"[ERROR] Django导入失败: {e}")
        return False

def test_django_check():
    """测试Django检查"""
    print("\n=== 测试Django检查 ===")

    academic_dir = get_resource_path("SchoolAcademicManager")
    print(f"Django目录: {academic_dir}")

    if not os.path.exists(academic_dir):
        print("[ERROR] Django目录不存在")
        return False

    manage_py = os.path.join(academic_dir, "manage.py")
    if not os.path.exists(manage_py):
        print("[ERROR] manage.py不存在")
        return False

    print("[OK] Django项目文件存在")

    # 使用安全的Django初始化
    if not safe_django_setup():
        print("[ERROR] Django初始化失败")
        return False

    try:
        from django.conf import settings
        print(f"[OK] Django配置成功")
        print(f"数据库: {settings.DATABASES['default']['ENGINE']}")

        # 测试import_export导入
        try:
            import import_export
            print(f"[OK] import_export版本: {import_export.__version__}")
        except ImportError as e:
            print(f"[ERROR] import_export导入失败: {e}")
            return False

        # 测试import_export.admin导入
        try:
            from import_export.admin import ImportExportModelAdmin
            print("[OK] import_export.admin导入成功")
        except ImportError as e:
            print(f"[ERROR] import_export.admin导入失败: {e}")
            return False

        print(f"应用: {settings.INSTALLED_APPS}")
        return True

    except Exception as e:
        print(f"[ERROR] Django检查异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_django_migrate():
    """测试Django数据库迁移"""
    print("\n=== 测试Django数据库迁移 ===")

    academic_dir = get_resource_path("SchoolAcademicManager")

    try:
        print("检查数据库文件...")

        # 使用安全的Django初始化
        if not safe_django_setup():
            print("[ERROR] Django初始化失败")
            return False

        from django.conf import settings

        # 检查数据库文件
        db_path = os.path.join(academic_dir, 'db.sqlite3')
        if os.path.exists(db_path):
            print(f"[OK] 数据库文件存在: {db_path}")

            # 检查数据库大小
            db_size = os.path.getsize(db_path)
            print(f"数据库大小: {db_size} 字节")

            if db_size > 0:
                print("[OK] 数据库文件不为空")
                return True
            else:
                print("[WARN] 数据库文件为空")
                return False
        else:
            print("[WARN] 数据库文件不存在，可能需要迁移")
            return False

    except Exception as e:
        print(f"[ERROR] 数据库检查异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_django_runserver():
    """测试Django服务器启动"""
    print("\n=== 测试Django服务器启动 ===")

    try:
        print("测试Django应用导入...")

        # 使用安全的Django初始化
        if not safe_django_setup():
            print("[ERROR] Django初始化失败")
            return False

        from django.conf import settings

        # 测试导入应用
        try:
            from academic import models, views, admin
            print("[OK] academic应用导入成功")
        except ImportError as e:
            print(f"[ERROR] academic应用导入失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        # 测试URL配置
        try:
            from django.urls import reverse
            from SchoolAcademicManager import urls
            print("[OK] URL配置正常")
        except Exception as e:
            print(f"[WARN] URL配置问题: {e}")

        # 测试模板
        try:
            template_dirs = settings.TEMPLATES[0]['DIRS']
            print(f"模板目录: {template_dirs}")
            print("[OK] 模板配置正常")
        except Exception as e:
            print(f"[WARN] 模板配置问题: {e}")

        print("[OK] Django应用组件测试通过")
        return True

    except Exception as e:
        print(f"[ERROR] Django应用测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Django环境测试")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    
    try:
        print(f"PyInstaller临时目录: {sys._MEIPASS}")
    except:
        print("PyInstaller临时目录: 未找到（开发环境）")
    
    # 首先进行依赖检查
    print("\n=== 依赖检查 ===")
    try:
        deps_ok = print_dependency_report()
    except:
        print("依赖检查工具不可用，跳过详细检查")
        deps_ok = True

    # 运行测试
    tests = [
        ("Django导入", test_django_import),
        ("Django检查", test_django_check),
        ("数据库迁移", test_django_migrate),
        ("服务器启动", test_django_runserver)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"[ERROR] {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示结果
    print("\n" + "=" * 60)
    print("测试结果:")
    print("=" * 60)
    
    for test_name, result in results:
        status = "[OK]" if result else "[FAIL]"
        print(f"{status} {test_name}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n通过: {success_count}/{len(results)}")
    
    if success_count == len(results):
        print("\n[OK] 所有测试通过！Django应该可以正常运行")
    else:
        print("\n[ERROR] 部分测试失败，需要解决问题")
    
    print("=" * 60)
    input("按回车键退出...")

if __name__ == '__main__':
    main()
