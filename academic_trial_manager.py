#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SchoolAcademicManager 试用期管理器
专门为中学教务管理系统设计的试用期和许可证管理
"""

import os
import json
import hashlib
import base64
import winreg
import uuid
from datetime import datetime, timedelta
from cryptography.fernet import Fernet

class AcademicTrialManager:
    def __init__(self, trial_days=60):
        self.trial_days = trial_days
        self.app_name = "SchoolAcademicManager"
        self.app_version = "1.0"
        
        # 注册表路径
        self.registry_key = r"SOFTWARE\SchoolManagement\Academic"
        
        # 配置文件路径
        self.config_dir = os.path.join(os.getenv('APPDATA'), 'SchoolAcademic')
        self.trial_file = os.path.join(self.config_dir, 'trial.dat')
        self.license_file = os.path.join(self.config_dir, 'license.key')
        self.machine_file = os.path.join(self.config_dir, 'machine.id')
        
        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        
        # 生成机器唯一标识
        self.machine_id = self._get_machine_id()
        
        # 加密密钥
        self.key = self._generate_encryption_key()
        self.cipher = Fernet(self.key)
    
    def _get_machine_id(self):
        """获取机器唯一标识"""
        # 先尝试从文件读取
        if os.path.exists(self.machine_file):
            try:
                with open(self.machine_file, 'r') as f:
                    return f.read().strip()
            except:
                pass
        
        # 生成新的机器ID
        try:
            # 方法1：使用WMI获取硬件信息
            import wmi
            c = wmi.WMI()
            
            # 获取CPU信息
            cpu_info = c.Win32_Processor()[0]
            cpu_id = cpu_info.ProcessorId.strip()
            
            # 获取主板信息
            motherboard_info = c.Win32_BaseBoard()[0]
            motherboard_serial = motherboard_info.SerialNumber.strip()
            
            # 获取BIOS信息
            bios_info = c.Win32_BIOS()[0]
            bios_serial = bios_info.SerialNumber.strip()
            
            # 组合生成唯一ID
            machine_info = f"{cpu_id}-{motherboard_serial}-{bios_serial}"
            machine_id = hashlib.sha256(machine_info.encode()).hexdigest()[:32]
            
        except Exception:
            # 备用方法：使用MAC地址和计算机名
            try:
                import socket
                hostname = socket.gethostname()
                mac = uuid.getnode()
                machine_info = f"{hostname}-{mac}"
                machine_id = hashlib.sha256(machine_info.encode()).hexdigest()[:32]
            except:
                # 最后备用：随机生成
                machine_id = hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:32]
        
        # 保存机器ID到文件
        try:
            with open(self.machine_file, 'w') as f:
                f.write(machine_id)
        except:
            pass
        
        return machine_id
    
    def _generate_encryption_key(self):
        """生成加密密钥"""
        key_material = f"{self.machine_id}-{self.app_name}-{self.app_version}".encode()
        key_hash = hashlib.sha256(key_material).digest()
        return base64.urlsafe_b64encode(key_hash[:32])
    
    def _encrypt_data(self, data):
        """加密数据"""
        json_data = json.dumps(data, ensure_ascii=False)
        encrypted = self.cipher.encrypt(json_data.encode('utf-8'))
        return base64.b64encode(encrypted).decode('ascii')
    
    def _decrypt_data(self, encrypted_data):
        """解密数据"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('ascii'))
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return json.loads(decrypted.decode('utf-8'))
        except Exception:
            return None
    
    def _write_registry(self, name, value):
        """写入注册表"""
        try:
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, self.registry_key) as key:
                winreg.SetValueEx(key, name, 0, winreg.REG_SZ, str(value))
            return True
        except Exception:
            return False
    
    def _read_registry(self, name):
        """读取注册表"""
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key) as key:
                value, _ = winreg.QueryValueEx(key, name)
                return value
        except Exception:
            return None
    
    def initialize_trial(self):
        """初始化试用期"""
        now = datetime.now()
        end_date = now + timedelta(days=self.trial_days)
        
        trial_data = {
            'app_name': self.app_name,
            'app_version': self.app_version,
            'machine_id': self.machine_id,
            'start_date': now.isoformat(),
            'end_date': end_date.isoformat(),
            'trial_days': self.trial_days,
            'install_count': 1,
            'first_run': True,
            'created_at': now.isoformat()
        }
        
        # 保存到文件
        encrypted_data = self._encrypt_data(trial_data)
        try:
            with open(self.trial_file, 'w') as f:
                f.write(encrypted_data)
        except Exception:
            pass
        
        # 保存到注册表（备份）
        self._write_registry('trial_data', encrypted_data)
        self._write_registry('install_date', now.isoformat())
        self._write_registry('machine_id', self.machine_id)
        
        return trial_data
    
    def get_trial_info(self):
        """获取试用期信息"""
        # 优先从文件读取
        trial_data = None
        if os.path.exists(self.trial_file):
            try:
                with open(self.trial_file, 'r') as f:
                    encrypted_data = f.read()
                trial_data = self._decrypt_data(encrypted_data)
            except Exception:
                pass
        
        # 从注册表读取（备份）
        if not trial_data:
            encrypted_data = self._read_registry('trial_data')
            if encrypted_data:
                trial_data = self._decrypt_data(encrypted_data)
        
        # 验证数据完整性
        if trial_data:
            if trial_data.get('machine_id') != self.machine_id:
                return None
            if trial_data.get('app_name') != self.app_name:
                return None
        
        return trial_data
    
    def check_trial_status(self):
        """检查试用期状态"""
        trial_data = self.get_trial_info()
        
        if not trial_data:
            # 首次运行，初始化试用期
            trial_data = self.initialize_trial()
        
        now = datetime.now()
        try:
            end_date = datetime.fromisoformat(trial_data['end_date'])
            start_date = datetime.fromisoformat(trial_data['start_date'])
        except Exception:
            # 数据损坏，重新初始化
            trial_data = self.initialize_trial()
            end_date = datetime.fromisoformat(trial_data['end_date'])
            start_date = datetime.fromisoformat(trial_data['start_date'])
        
        if now > end_date:
            return {
                'status': 'expired',
                'message': '试用期已过期',
                'days_remaining': 0,
                'total_days': self.trial_days,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'expired_days': (now - end_date).days
            }
        
        days_remaining = (end_date - now).days + 1  # 包含当天
        days_used = (now - start_date).days
        
        return {
            'status': 'active',
            'message': f'试用期剩余 {days_remaining} 天',
            'days_remaining': days_remaining,
            'days_used': days_used,
            'total_days': self.trial_days,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        }
    
    def is_trial_valid(self):
        """检查试用期是否有效"""
        status = self.check_trial_status()
        return status['status'] == 'active'
    
    def get_machine_info(self):
        """获取机器信息"""
        return {
            'machine_id': self.machine_id,
            'app_name': self.app_name,
            'app_version': self.app_version,
            'config_dir': self.config_dir
        }

# 测试函数
def test_trial_manager():
    """测试试用期管理器"""
    print("=" * 60)
    print("🔐 SchoolAcademicManager 试用期管理器测试")
    print("=" * 60)
    
    tm = AcademicTrialManager(trial_days=60)
    
    print(f"应用名称: {tm.app_name}")
    print(f"应用版本: {tm.app_version}")
    print(f"机器ID: {tm.machine_id}")
    print(f"配置目录: {tm.config_dir}")
    print()
    
    status = tm.check_trial_status()
    print("试用期状态:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    print()
    
    print(f"试用期有效: {tm.is_trial_valid()}")
    print()
    
    machine_info = tm.get_machine_info()
    print("机器信息:")
    for key, value in machine_info.items():
        print(f"  {key}: {value}")
    
    print("=" * 60)

if __name__ == '__main__':
    test_trial_manager()
