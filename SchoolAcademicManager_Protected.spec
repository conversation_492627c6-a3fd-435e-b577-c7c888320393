# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller配置文件 - 代码保护版本
将所有Python源代码打包进exe文件中，只保留数据库文件在外部
"""
import sys
import os
from pathlib import Path

block_cipher = None

# 获取项目根目录
import os
current_dir = os.path.dirname(os.path.abspath(SPEC))
project_root = Path(current_dir) / "SchoolAcademicManager"

# 收集所有需要打包的Python模块
def collect_python_modules():
    """收集所有Python模块，排除__pycache__和.pyc文件"""
    modules = []
    
    # Django项目配置模块
    django_config_dir = project_root / "SchoolAcademicManager"
    if django_config_dir.exists():
        for py_file in django_config_dir.glob("*.py"):
            if py_file.name != "__init__.py":  # __init__.py会自动包含
                modules.append(str(py_file))
    
    # academic应用模块
    academic_dir = project_root / "academic"
    if academic_dir.exists():
        for py_file in academic_dir.rglob("*.py"):
            if "__pycache__" not in str(py_file):
                modules.append(str(py_file))
    
    return modules

# 收集数据文件（排除Python源代码和数据库）
def collect_data_files():
    """收集模板、静态文件等数据文件"""
    datas = []
    
    # 模板文件
    templates_dir = project_root / "templates"
    if templates_dir.exists():
        datas.append((str(templates_dir), 'templates'))
    
    # 静态文件
    static_dir = project_root / "static"
    if static_dir.exists():
        datas.append((str(static_dir), 'static'))
    
    # Django import_export模板
    try:
        import import_export
        import_export_templates = Path(import_export.__file__).parent / "templates"
        if import_export_templates.exists():
            datas.append((str(import_export_templates), 'import_export/templates'))
    except ImportError:
        pass
    
    # 其他必要的非Python文件
    for file_pattern in ["*.txt", "*.md", "*.json", "*.yml", "*.yaml"]:
        for file_path in project_root.glob(file_pattern):
            if file_path.name not in ["requirements.txt"]:  # 排除不需要的文件
                datas.append((str(file_path), '.'))
    
    return datas

# 收集隐藏导入
hidden_imports = [
    'django',
    'django.core.management',
    'django.core.management.commands',
    'django.core.management.commands.runserver',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'academic',
    'academic.models',
    'academic.views',
    'academic.admin',
    'academic.apps',
    'import_export',
    'colorfield',
    'tkinter',
    'tkinter.messagebox',
]

a = Analysis(
    [str(project_root / 'protected_launcher.py')],  # 主入口文件
    pathex=[str(project_root)],
    binaries=[],
    datas=collect_data_files(),
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',  # 排除不需要的大型库
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉数据库文件，确保它不被打包进exe
a.datas = [x for x in a.datas if not x[0].endswith('.sqlite3')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='中学教务管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
