#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
安装SchoolAcademicManager GUI启动器所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {package} 安装异常: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        if package == 'tkinter':
            import tkinter
        else:
            __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("📦 SchoolAcademicManager GUI启动器依赖安装工具")
    print("=" * 60)
    
    # 必需的包列表
    required_packages = [
        'cryptography',
        'wmi',
        'pyinstaller'
    ]
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    # 检查tkinter
    print("\n检查tkinter...")
    if check_package('tkinter'):
        print("✅ tkinter 已可用")
    else:
        print("❌ tkinter 不可用")
        print("tkinter通常随Python安装，如果缺失请重新安装Python")
        return False
    
    # 检查和安装其他包
    print("\n检查其他依赖包...")
    failed_packages = []
    
    for package in required_packages:
        if check_package(package):
            print(f"✅ {package} 已安装")
        else:
            print(f"⚠️ {package} 未安装，正在安装...")
            if not install_package(package):
                failed_packages.append(package)
    
    # 结果报告
    print("\n" + "=" * 60)
    if failed_packages:
        print("❌ 以下包安装失败:")
        for package in failed_packages:
            print(f"  • {package}")
        print("\n请手动安装这些包:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    else:
        print("✅ 所有依赖包已成功安装!")
        print("\n可以运行以下命令测试:")
        print("  python academic_gui_launcher.py")
        print("  python build_academic_gui.py")
        return True

if __name__ == '__main__':
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 依赖安装完成!")
    else:
        print("💥 依赖安装失败!")
    print("=" * 60)
    
    input("\n按回车键退出...")
