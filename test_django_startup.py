#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接测试Django启动 - 用于诊断Django服务器启动问题
"""

import os
import sys
import subprocess
import time
import socket

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def get_resource_path(relative_path):
    """获取资源文件路径"""
    try:
        base_path = sys._MEIPASS
        print(f"打包环境，基础路径: {base_path}")
    except Exception:
        base_path = os.path.abspath(".")
        print(f"开发环境，基础路径: {base_path}")
    
    return os.path.join(base_path, relative_path)

def test_django_direct():
    """直接测试Django启动"""
    print("=" * 60)
    print("Django启动测试")
    print("=" * 60)
    
    # 获取Django项目路径
    academic_dir = get_resource_path("SchoolAcademicManager")
    print(f"Django项目路径: {academic_dir}")
    
    if not os.path.exists(academic_dir):
        print("[ERROR] Django项目目录不存在")
        return False

    manage_py = os.path.join(academic_dir, "manage.py")
    if not os.path.exists(manage_py):
        print("[ERROR] manage.py不存在")
        return False
    
    print("[OK] Django项目文件检查通过")

    # 检查端口
    if check_port(8001):
        print("[WARN] 端口8001已被占用")
        return False

    print("[OK] 端口8001可用")
    
    try:
        print("\n开始启动Django服务器...")
        print("命令:", [sys.executable, "manage.py", "runserver", "127.0.0.1:8001", "--noreload"])
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['DJANGO_SETTINGS_MODULE'] = 'SchoolAcademicManager.settings'
        
        # 启动Django服务器
        process = subprocess.Popen([
            sys.executable, "manage.py", "runserver", "127.0.0.1:8001", "--noreload", "--verbosity=2"
        ], cwd=academic_dir,
           stdout=subprocess.PIPE,
           stderr=subprocess.STDOUT,
           universal_newlines=True,
           env=env)
        
        print("⏳ 等待服务器启动...")
        
        # 监控输出和端口
        start_time = time.time()
        output_lines = []
        
        while time.time() - start_time < 30:  # 最多等待30秒
            # 检查进程是否还在运行
            poll_result = process.poll()
            if poll_result is not None:
                print(f"[ERROR] Django进程退出，返回码: {poll_result}")

                # 读取剩余输出
                try:
                    remaining_output, _ = process.communicate(timeout=2)
                    if remaining_output:
                        print("进程输出:")
                        print(remaining_output)
                except:
                    pass

                return False
            
            # 读取输出
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    output_lines.append(line)
                    print(f"Django: {line}")
                    
                    # 检查是否有启动成功的标志
                    if "Starting development server" in line or "Quit the server" in line:
                        print("[START] Django服务器启动信息已显示")
            except:
                pass
            
            # 检查端口是否开始监听
            if check_port(8001):
                print("[OK] Django服务器启动成功！")
                print("[WEB] 访问地址: http://127.0.0.1:8001")

                # 停止服务器
                print("\n[STOP] 停止Django服务器...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except:
                    process.kill()

                print("[OK] 测试完成，Django可以正常启动")
                return True
            
            time.sleep(0.5)
        
        print("[ERROR] Django启动超时")
        print("\n收集到的输出:")
        for line in output_lines:
            print(f"  {line}")

        # 终止进程
        process.terminate()
        try:
            process.wait(timeout=5)
        except:
            process.kill()

        return False

    except Exception as e:
        print(f"[ERROR] 启动Django时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_django_direct()
    
    print("\n" + "=" * 60)
    if success:
        print("[SUCCESS] Django启动测试成功！")
    else:
        print("[FAILED] Django启动测试失败！")
    print("=" * 60)
    
    input("按回车键退出...")

if __name__ == '__main__':
    main()
