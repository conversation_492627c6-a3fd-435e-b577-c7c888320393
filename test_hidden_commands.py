#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试隐藏窗口功能
验证系统命令是否能在不显示控制台窗口的情况下执行
"""

import subprocess
import os
import time

def test_hidden_command():
    """测试隐藏窗口的命令执行"""
    print("🧪 测试隐藏窗口功能...")
    
    try:
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 测试一个简单的命令
            result = subprocess.run([
                'cmd', '/c', 'echo 测试隐藏窗口功能'
            ], capture_output=True, text=True, timeout=5,
            startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            
            if result.returncode == 0:
                print("✅ 隐藏窗口功能正常工作")
                print(f"命令输出: {result.stdout.strip()}")
                return True
            else:
                print("❌ 命令执行失败")
                return False
        else:
            print("⚠️ 非Windows系统，跳过测试")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_netstat_hidden():
    """测试隐藏的netstat命令"""
    print("\n🧪 测试隐藏的netstat命令...")
    
    try:
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            result = subprocess.run([
                'netstat', '-ano'
            ], capture_output=True, text=True, timeout=10,
            startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                listening_count = len([line for line in lines if 'LISTENING' in line])
                print(f"✅ netstat命令执行成功，找到 {listening_count} 个监听端口")
                return True
            else:
                print("❌ netstat命令执行失败")
                return False
        else:
            print("⚠️ 非Windows系统，跳过测试")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("=" * 50)
    print("🔧 隐藏窗口功能测试")
    print("=" * 50)
    
    # 测试基本的隐藏命令
    test1_result = test_hidden_command()
    
    # 测试netstat隐藏命令
    test2_result = test_netstat_hidden()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"  基本隐藏命令: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  netstat隐藏命令: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！停止系统时不会显示黑色控制台窗口。")
    else:
        print("\n⚠️ 部分测试失败，可能仍会显示控制台窗口。")
    
    print("=" * 50)
    input("按回车键退出...")

if __name__ == '__main__':
    main()
