#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建完整的分发包
将exe文件和SchoolAcademicManager目录一起打包
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_distribution():
    """创建完整的分发包"""
    print("🚀 创建中学教务管理系统分发包...")
    
    # 创建分发目录
    dist_dir = "SchoolAcademicManager_Distribution"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)
    
    print(f"📁 创建分发目录: {dist_dir}")
    
    # 复制exe文件
    exe_source = "dist/中学教务管理系统.exe"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, os.path.join(dist_dir, "中学教务管理系统.exe"))
        print("✅ 复制主程序文件")
    else:
        print("❌ 找不到exe文件，请先运行打包脚本")
        return False

    # 复制Django启动脚本
    django_bat = "start_django.bat"
    if os.path.exists(django_bat):
        shutil.copy2(django_bat, os.path.join(dist_dir, "start_django.bat"))
        print("✅ 复制Django启动脚本")
    else:
        print("⚠️ 找不到Django启动脚本，但继续打包")
    
    # 复制SchoolAcademicManager目录
    academic_source = "SchoolAcademicManager"
    academic_dest = os.path.join(dist_dir, "SchoolAcademicManager")
    if os.path.exists(academic_source):
        shutil.copytree(academic_source, academic_dest, 
                       ignore=shutil.ignore_patterns('__pycache__', '*.pyc', 'build', 'dist', 'packager'))
        print("✅ 复制Django项目目录")
    else:
        print("❌ 找不到SchoolAcademicManager目录")
        return False
    
    # 创建启动说明
    readme_content = """# 中学教务管理系统 v1.0

## 🚀 快速开始

1. **运行程序**
   双击 `中学教务管理系统.exe` 启动程序

2. **试用期**
   - 首次运行自动开始60天试用期
   - 试用期内可以使用所有功能
   - 试用期结束后需要激活许可证

3. **激活软件**
   - 点击"激活软件"按钮
   - 输入购买的许可证密钥
   - 格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX

4. **购买许可证**
   - 点击"查看机器ID"获取机器标识
   - 联系销售人员购买许可证
   - 销售热线：400-123-4567
   - 邮箱：<EMAIL>

## 📋 系统要求

- Windows 7/8/10/11 (64位)
- 至少2GB内存
- 500MB可用磁盘空间
- 网络连接（用于系统访问）

## 🔧 使用说明

1. **启动系统**
   - 在程序界面点击"启动系统"
   - 等待系统启动完成
   - 自动打开浏览器访问系统

2. **访问地址**
   - http://127.0.0.1:8001
   - 默认账户：admin
   - 默认密码：admin123

3. **停止系统**
   - 点击"停止系统"按钮
   - 或直接关闭程序窗口

## ⚠️ 注意事项

- 请勿删除SchoolAcademicManager目录
- 首次启动可能需要较长时间
- 如遇问题请联系技术支持

## 📞 技术支持

- 技术热线：400-123-4567
- 邮箱：<EMAIL>
- QQ群：123456789

---
© 2025 中学教务管理系统 v1.0
"""
    
    with open(os.path.join(dist_dir, "使用说明.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 创建使用说明文件")
    
    # 创建启动脚本
    bat_content = """@echo off
chcp 65001 >nul
echo 启动中学教务管理系统...
start "" "中学教务管理系统.exe"
"""
    
    with open(os.path.join(dist_dir, "启动系统.bat"), 'w', encoding='utf-8') as f:
        f.write(bat_content)
    print("✅ 创建启动脚本")
    
    # 创建压缩包
    zip_name = "中学教务管理系统_v1.0.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, dist_dir)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 创建压缩包: {zip_name}")
    
    # 显示结果
    print("\n" + "="*60)
    print("🎉 分发包创建完成!")
    print("="*60)
    print(f"📁 分发目录: {os.path.abspath(dist_dir)}")
    print(f"📦 压缩包: {os.path.abspath(zip_name)}")
    
    # 计算大小
    total_size = 0
    for root, dirs, files in os.walk(dist_dir):
        for file in files:
            total_size += os.path.getsize(os.path.join(root, file))
    
    print(f"📏 总大小: {total_size / (1024*1024):.1f} MB")
    
    print("\n📋 分发内容:")
    print("  • 中学教务管理系统.exe  - 主程序")
    print("  • SchoolAcademicManager/  - Django项目")
    print("  • 使用说明.txt          - 详细说明")
    print("  • 启动系统.bat          - 启动脚本")
    
    print("\n🚀 分发建议:")
    print("1. 将压缩包发送给用户")
    print("2. 用户解压到任意目录")
    print("3. 双击exe文件或bat脚本启动")
    print("4. 首次运行显示60天试用期")
    
    return True

if __name__ == '__main__':
    create_distribution()
