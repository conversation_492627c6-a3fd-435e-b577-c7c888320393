#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
强制停止所有学校管理系统
使用Python实现，避免批处理文件的编码问题
"""

import subprocess
import time
import socket
import os

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def get_pid_by_port(port):
    """通过端口获取进程ID（隐藏窗口）"""
    try:
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            result = subprocess.run([
                'netstat', '-ano'
            ], capture_output=True, text=True, timeout=10,
            startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            result = subprocess.run([
                'netstat', '-ano'
            ], capture_output=True, text=True, timeout=10)

        for line in result.stdout.split('\n'):
            if f'127.0.0.1:{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    return parts[-1]
    except:
        pass
    return None

def kill_process_by_pid(pid):
    """通过PID终止进程（隐藏窗口）"""
    try:
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            subprocess.run(['taskkill', '/f', '/pid', str(pid)],
                          capture_output=True, timeout=10,
                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            subprocess.run(['taskkill', '/f', '/pid', str(pid)],
                          capture_output=True, timeout=10)
        return True
    except:
        return False

def kill_all_python():
    """终止所有Python进程（隐藏窗口）"""
    try:
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            subprocess.run(['taskkill', '/f', '/im', 'python.exe'],
                          capture_output=True, timeout=10,
                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            subprocess.run(['taskkill', '/f', '/im', 'pythonw.exe'],
                          capture_output=True, timeout=10,
                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            subprocess.run(['taskkill', '/f', '/im', 'python.exe'],
                          capture_output=True, timeout=10)
            subprocess.run(['taskkill', '/f', '/im', 'pythonw.exe'],
                          capture_output=True, timeout=10)
        return True
    except:
        return False

def main():
    print("=" * 50)
    print("🛑 强制停止所有学校管理系统")
    print("=" * 50)
    
    ports = {
        5000: "启动器",
        8000: "图书管理系统", 
        8001: "教务管理系统",
        8002: "财务管理系统"
    }
    
    stopped_systems = []
    failed_systems = []
    
    # 方法1: 通过端口查找并终止进程
    print("\n🔄 方法1: 通过端口终止进程...")
    for port, name in ports.items():
        if check_port(port):
            print(f"正在停止 {name} (端口 {port})...")
            pid = get_pid_by_port(port)
            if pid:
                if kill_process_by_pid(pid):
                    print(f"✅ 已终止进程 {pid}")
                else:
                    print(f"❌ 终止进程 {pid} 失败")
            time.sleep(1)
    
    time.sleep(3)
    
    # 方法2: 终止所有Python进程
    print("\n🔄 方法2: 终止所有Python进程...")
    kill_all_python()
    time.sleep(3)
    
    # 方法3: 使用WMIC（隐藏窗口）
    print("\n🔄 方法3: 使用WMIC终止...")
    try:
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            subprocess.run(['wmic', 'process', 'where', 'name="python.exe"', 'delete'],
                          capture_output=True, timeout=15,
                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            subprocess.run(['wmic', 'process', 'where', 'name="pythonw.exe"', 'delete'],
                          capture_output=True, timeout=15,
                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            subprocess.run(['wmic', 'process', 'where', 'name="python.exe"', 'delete'],
                          capture_output=True, timeout=15)
            subprocess.run(['wmic', 'process', 'where', 'name="pythonw.exe"', 'delete'],
                          capture_output=True, timeout=15)
    except:
        pass
    
    time.sleep(3)
    
    # 验证结果
    print("\n🔍 验证停止结果...")
    print("-" * 40)
    
    for port, name in ports.items():
        if not check_port(port):
            print(f"✅ {name} 已停止")
            stopped_systems.append(name)
        else:
            print(f"❌ {name} 仍在运行")
            failed_systems.append(name)
    
    print("-" * 40)
    
    if len(stopped_systems) >= 3:
        print(f"\n🎉 成功停止 {len(stopped_systems)} 个系统!")
        if failed_systems:
            print(f"⚠️ 仍有 {len(failed_systems)} 个系统在运行: {', '.join(failed_systems)}")
    else:
        print(f"\n⚠️ 只停止了 {len(stopped_systems)} 个系统")
        if failed_systems:
            print(f"❌ 以下系统仍在运行: {', '.join(failed_systems)}")
            print("\n💡 建议:")
            print("  1. 重启计算机")
            print("  2. 使用任务管理器手动结束进程")
            print("  3. 检查是否有其他程序占用端口")
    
    print("\n" + "=" * 50)
    input("按回车键退出...")

if __name__ == '__main__':
    main()
