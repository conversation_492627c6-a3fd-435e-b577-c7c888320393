#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
单机授权系统 - 确保软件只能在一台机器上安装和运行
防止盗版和多机器使用
"""

import os
import sys
import hashlib
import json
import platform
import subprocess
import uuid
from datetime import datetime, timedelta
import base64
import threading
import time

class SingleMachineLicense:
    def __init__(self, license_file="machine_license.dat", server_url=None):
        self.license_file = license_file
        self.server_url = server_url or "https://your-license-server.com/api"  # 您的许可证服务器
        self.machine_id = None
        self.license_data = None
        self.activation_lock = threading.Lock()
        
    def get_machine_fingerprint(self):
        """获取机器硬件指纹 - 更严格的版本"""
        try:
            fingerprint_data = []
            
            # 1. CPU信息 - 更详细
            try:
                if platform.system() == "Windows":
                    # CPU ID
                    result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        cpu_id = result.stdout.strip().split('\n')[-1].strip()
                        if cpu_id and cpu_id != "ProcessorId":
                            fingerprint_data.append(f"CPU_ID:{cpu_id}")

                    # CPU名称
                    result = subprocess.run(['wmic', 'cpu', 'get', 'Name'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        cpu_name = result.stdout.strip().split('\n')[-1].strip()
                        if cpu_name and cpu_name != "Name":
                            fingerprint_data.append(f"CPU_NAME:{cpu_name}")
            except:
                pass
            
            # 2. 主板信息 - 更详细
            try:
                if platform.system() == "Windows":
                    # 主板序列号
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'SerialNumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        board_serial = result.stdout.strip().split('\n')[-1].strip()
                        if board_serial and board_serial != "SerialNumber":
                            fingerprint_data.append(f"BOARD_SERIAL:{board_serial}")

                    # 主板制造商和型号
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'Manufacturer,Product'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) > 1:
                            board_info = lines[-1].strip()
                            if board_info and "Manufacturer" not in board_info:
                                fingerprint_data.append(f"BOARD_INFO:{board_info}")
            except:
                pass
            
            # 3. 硬盘信息 - 所有硬盘
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'SerialNumber,Model'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        disk_count = 0
                        for line in lines[1:]:  # 跳过标题行
                            disk_info = line.strip()
                            if disk_info and "SerialNumber" not in disk_info and disk_info:
                                fingerprint_data.append(f"DISK_{disk_count}:{disk_info}")
                                disk_count += 1
                                if disk_count >= 3:  # 最多记录3个硬盘
                                    break
            except:
                pass
            
            # 4. 内存信息
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'memorychip', 'get', 'SerialNumber,Capacity'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        memory_count = 0
                        for line in lines[1:]:
                            memory_info = line.strip()
                            if memory_info and "SerialNumber" not in memory_info and memory_info:
                                fingerprint_data.append(f"MEMORY_{memory_count}:{memory_info}")
                                memory_count += 1
                                if memory_count >= 2:  # 最多记录2个内存条
                                    break
            except:
                pass
            
            # 5. 网络适配器MAC地址 - 所有物理网卡
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['getmac', '/fo', 'csv', '/v'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        mac_count = 0
                        for line in lines[1:]:  # 跳过标题行
                            if 'Physical' in line and 'Enabled' in line:
                                parts = line.split(',')
                                if len(parts) >= 3:
                                    mac = parts[2].strip('"')
                                    if mac and mac != "N/A":
                                        fingerprint_data.append(f"MAC_{mac_count}:{mac}")
                                        mac_count += 1
                                        if mac_count >= 2:  # 最多记录2个网卡
                                            break
            except:
                # 备用方法
                try:
                    mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                                   for elements in range(0,2*6,2)][::-1])
                    fingerprint_data.append(f"MAC_0:{mac}")
                except:
                    pass
            
            # 6. 系统信息 - 更详细
            try:
                system_info = f"{platform.system()}:{platform.release()}:{platform.version()}:{platform.machine()}"
                fingerprint_data.append(f"SYSTEM:{system_info}")
                
                # Windows版本信息
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'os', 'get', 'SerialNumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        os_serial = result.stdout.strip().split('\n')[-1].strip()
                        if os_serial and os_serial != "SerialNumber":
                            fingerprint_data.append(f"OS_SERIAL:{os_serial}")
            except:
                pass
            
            # 7. BIOS信息
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'bios', 'get', 'SerialNumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        bios_serial = result.stdout.strip().split('\n')[-1].strip()
                        if bios_serial and bios_serial != "SerialNumber":
                            fingerprint_data.append(f"BIOS:{bios_serial}")
            except:
                pass
            
            # 如果没有获取到足够的硬件信息，使用备用方法
            if len(fingerprint_data) < 3:
                fingerprint_data.append(f"FALLBACK_NODE:{platform.node()}")
                fingerprint_data.append(f"FALLBACK_USER:{os.getenv('USERNAME', 'user')}")
                fingerprint_data.append(f"FALLBACK_UUID:{str(uuid.uuid4())}")
            
            # 生成最终指纹 - 使用更强的哈希
            fingerprint_str = "|".join(sorted(fingerprint_data))
            machine_id = hashlib.sha256(fingerprint_str.encode()).hexdigest()
            
            return machine_id, fingerprint_data
            
        except Exception as e:
            # 如果所有方法都失败，生成一个基于系统的备用ID
            fallback_data = f"{platform.system()}:{platform.node()}:{os.getenv('USERNAME', 'user')}:{str(uuid.uuid4())}"
            machine_id = hashlib.sha256(fallback_data.encode()).hexdigest()
            return machine_id, [f"FALLBACK:{fallback_data}"]
    
    def generate_license_key(self, machine_id, days_valid=30, product_name="SchoolAcademicManager"):
        """生成许可证密钥"""
        license_data = {
            "machine_id": machine_id,
            "product": product_name,
            "issued_date": datetime.now().isoformat(),
            "expiry_date": (datetime.now() + timedelta(days=days_valid)).isoformat(),
            "version": "2.0",
            "license_type": "SINGLE_MACHINE",
            "activation_count": 0,
            "max_activations": 1,  # 只允许一次激活
            "first_activation": None,
            "last_check": None
        }
        
        # 对许可证数据进行加密
        license_json = json.dumps(license_data, sort_keys=True)
        encoded_license = base64.b64encode(license_json.encode()).decode()
        
        # 生成强校验和
        checksum = hashlib.sha256(encoded_license.encode()).hexdigest()[:16]
        final_license = f"{encoded_license}.{checksum}"
        
        return final_license
    
    def activate_license(self, license_key):
        """激活许可证 - 首次安装时调用"""
        with self.activation_lock:
            try:
                # 解析许可证
                if '.' not in license_key:
                    return False, "许可证格式错误"
                
                encoded_license, checksum = license_key.rsplit('.', 1)
                expected_checksum = hashlib.sha256(encoded_license.encode()).hexdigest()[:16]
                
                if checksum != expected_checksum:
                    return False, "许可证校验失败"
                
                # 解码许可证
                license_json = base64.b64decode(encoded_license.encode()).decode()
                license_data = json.loads(license_json)
                
                # 获取当前机器指纹
                current_machine_id, _ = self.get_machine_fingerprint()
                
                # 验证机器ID
                if license_data.get("machine_id") != current_machine_id:
                    return False, f"此许可证不适用于当前机器\n许可证机器ID: {license_data.get('machine_id')[:16]}...\n当前机器ID: {current_machine_id[:16]}..."
                
                # 检查是否已经激活过
                if license_data.get("activation_count", 0) >= license_data.get("max_activations", 1):
                    return False, "此许可证已经在其他机器上激活，无法重复使用"
                
                # 验证过期时间
                try:
                    expiry_date = datetime.fromisoformat(license_data.get("expiry_date"))
                    if datetime.now() > expiry_date:
                        return False, f"许可证已过期\n过期时间: {expiry_date.strftime('%Y-%m-%d %H:%M:%S')}"
                except:
                    return False, "许可证日期格式错误"
                
                # 激活许可证
                license_data["activation_count"] = license_data.get("activation_count", 0) + 1
                license_data["first_activation"] = datetime.now().isoformat()
                license_data["last_check"] = datetime.now().isoformat()
                
                # 保存激活后的许可证
                new_license_json = json.dumps(license_data, sort_keys=True)
                new_encoded_license = base64.b64encode(new_license_json.encode()).decode()
                new_checksum = hashlib.sha256(new_encoded_license.encode()).hexdigest()[:16]
                final_license = f"{new_encoded_license}.{new_checksum}"
                
                # 保存到本地
                with open(self.license_file, 'w') as f:
                    f.write(final_license)
                
                self.license_data = license_data
                return True, "许可证激活成功"
                
            except Exception as e:
                return False, f"激活失败: {e}"
    
    def validate_license(self):
        """验证许可证 - 每次启动时调用"""
        try:
            # 检查许可证文件是否存在
            if not os.path.exists(self.license_file):
                return False, "未找到许可证文件，请先激活软件"
            
            # 读取许可证
            with open(self.license_file, 'r') as f:
                license_content = f.read().strip()
            
            # 验证格式
            if '.' not in license_content:
                return False, "许可证文件损坏"
            
            encoded_license, checksum = license_content.rsplit('.', 1)
            expected_checksum = hashlib.sha256(encoded_license.encode()).hexdigest()[:16]
            
            if checksum != expected_checksum:
                return False, "许可证文件被篡改"
            
            # 解码许可证
            license_json = base64.b64decode(encoded_license.encode()).decode()
            license_data = json.loads(license_json)
            
            # 获取当前机器指纹
            current_machine_id, _ = self.get_machine_fingerprint()
            
            # 验证机器ID
            if license_data.get("machine_id") != current_machine_id:
                return False, "许可证与当前机器不匹配，可能被非法复制"
            
            # 验证激活状态
            if license_data.get("activation_count", 0) == 0:
                return False, "许可证未激活"
            
            if license_data.get("activation_count", 0) > license_data.get("max_activations", 1):
                return False, "许可证激活次数异常"
            
            # 验证过期时间
            try:
                expiry_date = datetime.fromisoformat(license_data.get("expiry_date"))
                if datetime.now() > expiry_date:
                    return False, f"许可证已过期\n过期时间: {expiry_date.strftime('%Y-%m-%d %H:%M:%S')}"
            except:
                return False, "许可证日期格式错误"
            
            # 更新最后检查时间
            license_data["last_check"] = datetime.now().isoformat()
            
            # 保存更新后的许可证
            new_license_json = json.dumps(license_data, sort_keys=True)
            new_encoded_license = base64.b64encode(new_license_json.encode()).decode()
            new_checksum = hashlib.sha256(new_encoded_license.encode()).hexdigest()[:16]
            final_license = f"{new_encoded_license}.{new_checksum}"
            
            with open(self.license_file, 'w') as f:
                f.write(final_license)
            
            self.license_data = license_data
            return True, "许可证验证通过"
            
        except Exception as e:
            return False, f"许可证验证失败: {e}"
    
    def get_license_info(self):
        """获取许可证信息"""
        if not self.license_data:
            return None
        
        try:
            issued_date = datetime.fromisoformat(self.license_data.get("issued_date"))
            expiry_date = datetime.fromisoformat(self.license_data.get("expiry_date"))
            first_activation = self.license_data.get("first_activation")
            if first_activation:
                first_activation = datetime.fromisoformat(first_activation)
            
            days_left = (expiry_date - datetime.now()).days
            
            info = {
                "machine_id": self.license_data.get("machine_id"),
                "product": self.license_data.get("product"),
                "issued_date": issued_date.strftime("%Y-%m-%d"),
                "expiry_date": expiry_date.strftime("%Y-%m-%d"),
                "days_left": max(0, days_left),
                "activation_count": self.license_data.get("activation_count", 0),
                "max_activations": self.license_data.get("max_activations", 1),
                "first_activation": first_activation.strftime("%Y-%m-%d %H:%M:%S") if first_activation else None,
                "license_type": self.license_data.get("license_type", "UNKNOWN")
            }
            
            return info
            
        except Exception as e:
            print(f"获取许可证信息失败: {e}")
            return None
    
    def is_activated(self):
        """检查是否已激活"""
        return self.license_data is not None and self.license_data.get("activation_count", 0) > 0

def create_single_machine_license(machine_id, days=365, product_name="SchoolAcademicManager"):
    """创建单机许可证的工具函数"""
    license_system = SingleMachineLicense()
    
    print("=" * 60)
    print("单机许可证生成工具")
    print("=" * 60)
    print(f"目标机器ID: {machine_id}")
    print(f"产品名称: {product_name}")
    print(f"有效期: {days} 天")
    print(f"许可证类型: 单机授权")
    print()
    
    license_key = license_system.generate_license_key(machine_id, days, product_name)
    
    filename = f"license_{machine_id[:16]}.key"
    try:
        with open(filename, 'w') as f:
            f.write(license_key)
        
        print(f"✅ 许可证已生成并保存到: {filename}")
        print()
        print("使用说明:")
        print("1. 将许可证文件发送给用户")
        print("2. 用户首次运行软件时输入许可证密钥进行激活")
        print("3. 激活后软件只能在该机器上运行")
        print("4. 许可证无法在其他机器上使用")
        print("=" * 60)
        return license_key
        
    except Exception as e:
        print(f"❌ 保存许可证失败: {e}")
        return None

if __name__ == '__main__':
    # 测试单机许可证系统
    license_system = SingleMachineLicense()
    machine_id, fingerprint = license_system.get_machine_fingerprint()
    
    print("当前机器信息:")
    print(f"机器ID: {machine_id}")
    print("硬件指纹:")
    for fp in fingerprint:
        print(f"  {fp}")
    
    # 生成测试许可证
    create_single_machine_license(machine_id, days=365)
