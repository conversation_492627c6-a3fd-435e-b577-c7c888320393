#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Django服务器启动器
解决中文路径编码问题
"""

import os
import sys
import subprocess
import time

def main():
    print("=" * 50)
    print("中学教务管理系统 - Django服务器启动器")
    print("=" * 50)
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    django_dir = os.path.join(current_dir, "SchoolAcademicManager")
    manage_py = os.path.join(django_dir, "manage.py")
    
    print(f"当前目录: {current_dir}")
    print(f"Django目录: {django_dir}")
    print(f"manage.py路径: {manage_py}")
    
    # 检查文件是否存在
    if not os.path.exists(manage_py):
        print(f"错误: 找不到manage.py文件")
        print(f"预期路径: {manage_py}")
        input("按回车键退出...")
        return
    
    # 切换到Django目录
    os.chdir(django_dir)
    print(f"工作目录已切换到: {os.getcwd()}")
    
    print("\n正在启动Django服务器...")
    print("服务器地址: http://127.0.0.1:8001")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        # 启动Django服务器
        subprocess.run([
            sys.executable, "manage.py", 
            "runserver", "127.0.0.1:8001", 
            "--noreload"
        ])
    except KeyboardInterrupt:
        print("\n\n服务器已停止")
    except Exception as e:
        print(f"\n启动服务器时出错: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
