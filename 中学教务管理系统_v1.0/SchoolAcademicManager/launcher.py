import subprocess
import sys
import os
from tkinter import Tk, Button, Label, messagebox
from trial_checker import check_trial

# 检查试用期
check_trial()

# 获取当前目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

def start_server():
    try:
        # 判断是否为打包后的 exe
        if getattr(sys, 'frozen', False):
            python_exe = sys.executable
            script = os.path.join(BASE_DIR, 'run_server.py')
            args = [python_exe, script]
        else:
            python_exe = sys.executable
            script = os.path.join(BASE_DIR, 'run_server.py')
            args = [python_exe, script]
        subprocess.Popen(
            args,
            cwd=BASE_DIR,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        messagebox.showinfo("已启动", "服务已在后台启动，浏览器访问 http://127.0.0.1:8000")
        root.destroy()
    except Exception as e:
        messagebox.showerror("启动失败", f"启动服务时出错: {e}")
        root.destroy()

if __name__ == "__main__":
    root = Tk()
    root.title("School Academic Manager Launcher")
    root.geometry("380x160")
    Label(root, text="School Academic Manager\n点击下方按钮启动服务", font=("微软雅黑", 13)).pack(pady=18)
    Button(root, text="启动服务 (后台)", font=("微软雅黑", 12), width=20, command=start_server).pack(pady=8)
    root.mainloop()
