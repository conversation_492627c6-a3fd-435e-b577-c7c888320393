{% extends 'base.html' %}

{% block title %}{{ student.name }} - 学生详情 - 学校教务管理系统{% endblock %}
{% block page_title %}学生详情{% endblock %}

{% block content %}
<!-- 返回按钮 -->
<div class="mb-3">
    <a href="{% url 'academic:student_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回学生列表
    </a>
    <a href="/admin/academic/student/{{ student.id }}/change/" class="btn btn-primary">
        <i class="fas fa-edit me-1"></i>编辑学生信息
    </a>
</div>

<!-- 学生基本信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="30%"><strong>学号:</strong></td>
                                <td>{{ student.student_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>姓名:</strong></td>
                                <td>{{ student.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>性别:</strong></td>
                                <td>{{ student.get_gender_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>出生日期:</strong></td>
                                <td>{{ student.birth_date }}</td>
                            </tr>
                            <tr>
                                <td><strong>身份证号:</strong></td>
                                <td>{{ student.id_card }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="30%"><strong>联系电话:</strong></td>
                                <td>{{ student.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>邮箱:</strong></td>
                                <td>{{ student.email|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>家庭住址:</strong></td>
                                <td>{{ student.address|default:"-" }}</td>
                            </tr>
                            <tr>
                                <td><strong>入学日期:</strong></td>
                                <td>{{ student.enrollment_date }}</td>
                            </tr>
                            <tr>
                                <td><strong>学籍状态:</strong></td>
                                <td>
                                    <span class="badge bg-{% if student.status == 'enrolled' %}success{% elif student.status == 'graduated' %}info{% elif student.status == 'suspended' %}warning{% else %}danger{% endif %}">
                                        {{ student.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>学籍信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>年级:</strong></td>
                        <td>{{ student.class_info.grade.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>班级类型:</strong></td>
                        <td>{{ student.class_info.class_type.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>班级:</strong></td>
                        <td>{{ student.class_info.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>班主任:</strong></td>
                        <td>{{ student.class_info.head_teacher.user.get_full_name|default:student.class_info.head_teacher.user.username|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>教室位置:</strong></td>
                        <td>{{ student.class_info.classroom_location|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>住校情况:</strong></td>
                        <td>{% if student.is_boarding %}住校{% else %}走读{% endif %}</td>
                    </tr>
                </table>
                
                <hr>
                <h6>家长信息</h6>
                <table class="table table-borderless table-sm">
                    {% if student.father_name %}
                    <tr>
                        <td><strong>父亲:</strong></td>
                        <td>{{ student.father_name }} ({{ student.father_phone|default:"-" }})</td>
                    </tr>
                    <tr>
                        <td><strong>父亲工作:</strong></td>
                        <td>{{ student.father_work|default:"-" }}</td>
                    </tr>
                    {% endif %}
                    {% if student.mother_name %}
                    <tr>
                        <td><strong>母亲:</strong></td>
                        <td>{{ student.mother_name }} ({{ student.mother_phone|default:"-" }})</td>
                    </tr>
                    <tr>
                        <td><strong>母亲工作:</strong></td>
                        <td>{{ student.mother_work|default:"-" }}</td>
                    </tr>
                    {% endif %}
                    {% if student.guardian_name %}
                    <tr>
                        <td><strong>监护人:</strong></td>
                        <td>{{ student.guardian_name }} ({{ student.guardian_relation|default:"-" }})</td>
                    </tr>
                    <tr>
                        <td><strong>监护人电话:</strong></td>
                        <td>{{ student.guardian_phone|default:"-" }}</td>
                    </tr>
                    {% endif %}
                </table>

                {% if student.health_condition %}
                <hr>
                <h6>健康状况</h6>
                <p class="small">{{ student.health_condition }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 选课信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-book-open me-2"></i>选课信息
                    <span class="badge bg-primary ms-2">{{ enrollments.count }} 门课程</span>
                </h5>
            </div>
            <div class="card-body">
                {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>课程代码</th>
                                    <th>课程名称</th>
                                    <th>学期</th>
                                    <th>任课教师</th>
                                    <th>学分</th>
                                    <th>选课时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ enrollment.course_offering.course.code }}</td>
                                    <td>{{ enrollment.course_offering.course.name }}</td>
                                    <td>{{ enrollment.course_offering.semester.name }}</td>
                                    <td>{{ enrollment.course_offering.teacher.user.get_full_name|default:enrollment.course_offering.teacher.user.username }}</td>
                                    <td>{{ enrollment.course_offering.course.credits }}</td>
                                    <td>{{ enrollment.enrollment_date|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <span class="badge bg-{% if enrollment.status == 'enrolled' %}success{% elif enrollment.status == 'dropped' %}danger{% else %}warning{% endif %}">
                                            {{ enrollment.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-book-open fa-2x text-muted mb-2"></i>
                        <p class="text-muted">暂无选课记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 成绩信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>成绩信息
                    <span class="badge bg-primary ms-2">{{ grades.count }} 条记录</span>
                </h5>
            </div>
            <div class="card-body">
                {% if grades %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>课程</th>
                                    <th>考试类型</th>
                                    <th>分数</th>
                                    <th>等级</th>
                                    <th>是否通过</th>
                                    <th>考试日期</th>
                                    <th>录入时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for grade in grades %}
                                <tr>
                                    <td>{{ grade.course_offering.course.name }}</td>
                                    <td>{{ grade.exam_type.name }}</td>
                                    <td>
                                        {% if grade.score %}
                                            <strong>{{ grade.score }}</strong>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if grade.letter_grade %}
                                            <span class="badge bg-{% if grade.letter_grade == 'A' %}success{% elif grade.letter_grade == 'B' %}info{% elif grade.letter_grade == 'C' %}warning{% elif grade.letter_grade == 'D' %}secondary{% else %}danger{% endif %}">
                                                {{ grade.letter_grade }}
                                            </span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if grade.is_passed %}success{% else %}danger{% endif %}">
                                            {% if grade.is_passed %}通过{% else %}未通过{% endif %}
                                        </span>
                                    </td>
                                    <td>{{ grade.exam_date|default:"-" }}</td>
                                    <td>{{ grade.recorded_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                        <p class="text-muted">暂无成绩记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 考勤信息 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>最近考勤记录
                    <span class="badge bg-primary ms-2">最近20条</span>
                </h5>
            </div>
            <div class="card-body">
                {% if attendances %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>课程</th>
                                    <th>日期</th>
                                    <th>状态</th>
                                    <th>备注</th>
                                    <th>记录人</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in attendances %}
                                <tr>
                                    <td>{{ attendance.course_offering.course.name }}</td>
                                    <td>{{ attendance.date }}</td>
                                    <td>
                                        <span class="badge bg-{% if attendance.status == 'present' %}success{% elif attendance.status == 'late' %}warning{% elif attendance.status == 'leave' %}info{% else %}danger{% endif %}">
                                            {{ attendance.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ attendance.notes|default:"-" }}</td>
                                    <td>{{ attendance.recorded_by.get_full_name|default:attendance.recorded_by.username }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-check fa-2x text-muted mb-2"></i>
                        <p class="text-muted">暂无考勤记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
