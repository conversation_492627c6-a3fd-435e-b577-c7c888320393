{% extends 'base.html' %}

{% block title %}教师管理 - 学校教务管理系统{% endblock %}
{% block page_title %}教师管理{% endblock %}

{% block content %}
<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="工号、姓名或用户名">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">院系</label>
                <select class="form-select" id="department" name="department">
                    <option value="">全部院系</option>
                    {% for department in departments %}
                        <option value="{{ department.id }}" {% if department.id|stringformat:"s" == selected_department %}selected{% endif %}>
                            {{ department.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="title" class="form-label">职称</label>
                <select class="form-select" id="title" name="title">
                    <option value="">全部职称</option>
                    {% for value, display in title_choices %}
                        <option value="{{ value }}" {% if value == selected_title %}selected{% endif %}>
                            {{ display }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{% url 'academic:teacher_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                    <a href="/admin/academic/teacher/add/" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加教师
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 教师列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chalkboard-teacher me-2"></i>教师列表
            <span class="badge bg-primary ms-2">共 {{ page_obj.paginator.count }} 人</span>
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>工号</th>
                            <th>姓名</th>
                            <th>院系</th>
                            <th>职称</th>
                            <th>聘用类型</th>
                            <th>联系方式</th>
                            <th>办公室</th>
                            <th>入职日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for teacher in page_obj %}
                        <tr>
                            <td>{{ teacher.employee_id }}</td>
                            <td>
                                <strong>{{ teacher.user.get_full_name|default:teacher.user.username }}</strong>
                                <br>
                                <small class="text-muted">{{ teacher.user.username }}</small>
                            </td>
                            <td>{{ teacher.department.name }}</td>
                            <td>
                                <span class="badge bg-{% if teacher.title == 'professor' %}danger{% elif teacher.title == 'associate_prof' %}warning{% elif teacher.title == 'lecturer' %}info{% else %}secondary{% endif %}">
                                    {{ teacher.get_title_display }}
                                </span>
                            </td>
                            <td>{{ teacher.get_employment_type_display }}</td>
                            <td>
                                {% if teacher.phone %}
                                    <i class="fas fa-phone me-1"></i>{{ teacher.phone }}<br>
                                {% endif %}
                                {% if teacher.email %}
                                    <i class="fas fa-envelope me-1"></i>{{ teacher.email }}
                                {% endif %}
                            </td>
                            <td>{{ teacher.office|default:"-" }}</td>
                            <td>{{ teacher.hire_date }}</td>
                            <td>
                                <span class="badge bg-{% if teacher.is_active %}success{% else %}danger{% endif %}">
                                    {% if teacher.is_active %}在职{% else %}离职{% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="/admin/academic/teacher/{{ teacher.id }}/change/" 
                                       class="btn btn-outline-secondary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-info" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#teacherModal{{ teacher.id }}" 
                                            title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- 教师详情模态框 -->
                        <div class="modal fade" id="teacherModal{{ teacher.id }}" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">教师详情 - {{ teacher.user.get_full_name|default:teacher.user.username }}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>基本信息</h6>
                                                <table class="table table-sm">
                                                    <tr><td>工号:</td><td>{{ teacher.employee_id }}</td></tr>
                                                    <tr><td>姓名:</td><td>{{ teacher.user.get_full_name|default:teacher.user.username }}</td></tr>
                                                    <tr><td>用户名:</td><td>{{ teacher.user.username }}</td></tr>
                                                    <tr><td>院系:</td><td>{{ teacher.department.name }}</td></tr>
                                                    <tr><td>职称:</td><td>{{ teacher.get_title_display }}</td></tr>
                                                    <tr><td>聘用类型:</td><td>{{ teacher.get_employment_type_display }}</td></tr>
                                                </table>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>联系信息</h6>
                                                <table class="table table-sm">
                                                    <tr><td>电话:</td><td>{{ teacher.phone|default:"-" }}</td></tr>
                                                    <tr><td>邮箱:</td><td>{{ teacher.email|default:"-" }}</td></tr>
                                                    <tr><td>办公室:</td><td>{{ teacher.office|default:"-" }}</td></tr>
                                                    <tr><td>入职日期:</td><td>{{ teacher.hire_date }}</td></tr>
                                                    <tr><td>状态:</td><td>{% if teacher.is_active %}在职{% else %}离职{% endif %}</td></tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                        <a href="/admin/academic/teacher/{{ teacher.id }}/change/" class="btn btn-primary">编辑</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="教师列表分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_title %}&title={{ selected_title }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_title %}&title={{ selected_title }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_title %}&title={{ selected_title }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_title %}&title={{ selected_title }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                <p class="text-muted">没有找到符合条件的教师</p>
                <a href="/admin/academic/teacher/add/" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>添加第一个教师
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
