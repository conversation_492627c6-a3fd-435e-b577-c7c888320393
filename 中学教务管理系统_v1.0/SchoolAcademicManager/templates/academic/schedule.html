{% extends 'base.html' %}

{% block title %}课程表 - 学校教务管理系统{% endblock %}
{% block page_title %}课程表{% endblock %}

{% block content %}
<!-- 当前学期信息 -->
{% if current_semester %}
<div class="alert alert-info mb-4">
    <h5><i class="fas fa-calendar-alt me-2"></i>当前学期课程表</h5>
    <p class="mb-0">{{ current_semester.name }} ({{ current_semester.start_date }} 至 {{ current_semester.end_date }})</p>
</div>
{% endif %}

<!-- 课程表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>课程安排表
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th width="10%">时间/星期</th>
                        {% for weekday_value, weekday_display in weekdays %}
                            <th width="15%" class="text-center">{{ weekday_display }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in schedule_matrix %}
                    <tr>
                        <td class="table-secondary text-center">
                            <strong>{{ row.time_slot }}</strong>
                        </td>
                        {% for course_info in row.courses %}
                        <td class="p-2">
                            {% if course_info %}
                                <div class="schedule-item bg-light p-2 rounded">
                                    <div class="fw-bold text-primary">{{ course_info.course.name }}</div>
                                    <div class="small text-muted">{{ course_info.course.code }}</div>
                                    <div class="small">
                                        <i class="fas fa-user me-1"></i>{{ course_info.teacher.user.get_full_name|default:course_info.teacher.user.username }}
                                    </div>
                                    <div class="small">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ course_info.classroom.name }}
                                    </div>
                                    <div class="small text-success">{{ course_info.weeks }}</div>
                                </div>
                            {% else %}
                                <div class="text-center text-muted small">-</div>
                            {% endif %}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="mt-4">
    <div class="row">
        <div class="col-md-6">
            <a href="/admin/academic/schedule/add/" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>添加课程安排
            </a>
            <a href="/admin/academic/schedule/" class="btn btn-outline-primary">
                <i class="fas fa-cog me-1"></i>管理课程表
            </a>
        </div>
        <div class="col-md-6 text-end">
            <button onclick="window.print()" class="btn btn-outline-secondary">
                <i class="fas fa-print me-1"></i>打印课程表
            </button>
        </div>
    </div>
</div>

<!-- 图例 -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-info-circle me-2"></i>说明
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>时间安排</h6>
                <ul class="list-unstyled small">
                    <li><strong>第1-2节:</strong> 08:00-09:40</li>
                    <li><strong>第3-4节:</strong> 10:00-11:40</li>
                    <li><strong>第5-6节:</strong> 14:00-15:40</li>
                    <li><strong>第7-8节:</strong> 16:00-17:40</li>
                    <li><strong>第9-10节:</strong> 19:00-20:40</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>课程信息</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-book text-primary me-1"></i>课程名称和代码</li>
                    <li><i class="fas fa-user text-secondary me-1"></i>任课教师</li>
                    <li><i class="fas fa-map-marker-alt text-info me-1"></i>上课教室</li>
                    <li><i class="fas fa-calendar text-success me-1"></i>上课周次</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.schedule-item {
    min-height: 80px;
    border: 1px solid #dee2e6;
}

.schedule-item:hover {
    background-color: #e9ecef !important;
    cursor: pointer;
}

@media print {
    .btn, .card-header, .alert {
        display: none !important;
    }
    
    .schedule-item {
        font-size: 10px !important;
        min-height: 60px;
    }
    
    table {
        font-size: 12px;
    }
}
</style>

<script>
// 添加过滤器支持
document.addEventListener('DOMContentLoaded', function() {
    // 这里可以添加JavaScript代码来支持模板过滤器
    // 由于Django模板的限制，我们需要在视图中处理数据结构
});
</script>
{% endblock %}
