"""Trial period enforcement for SchoolAcademicManager.

This module stores the first-run date in the user's home directory and blocks
startup once the configured number of days has elapsed.
"""
from __future__ import annotations

import datetime as _dt
import json as _json
import os as _os
import sys as _sys
from pathlib import Path as _Path

# How many days the application can be used without activation
_DAYS = 60

# Where we store activation / first-run info. Placed in user-specific home dir
_FILE: _Path = _Path.home() / ".sam_trial.json"


def _load_start_date() -> _dt.date | None:
    """Return saved start date, or None if file not present / invalid."""
    try:
        if not _FILE.exists():
            return None
        data = _json.loads(_FILE.read_text())
        return _dt.date.fromisoformat(data["start"])
    except Exception:
        # Corrupted file – ignore and treat as first run
        return None


def _save_start_date(date_: _dt.date) -> None:
    try:
        _FILE.write_text(_json.dumps({"start": date_.isoformat()}))
    except Exception:
        # If we fail to save, just continue – user gets unlimited trial but app still runs
        pass


def check() -> None:
    """Call this early during app startup to enforce trial period."""
    today = _dt.date.today()
    start = _load_start_date()

    if start is None:
        # First run – record date and allow execution
        start = today
        _save_start_date(start)

    # Days between today and start date
    diff_days = (today - start).days
    if diff_days >= _DAYS:
        _sys.stderr.write("试用期已结束，请联系管理员注册授权。\n")
        _sys.exit(1)

