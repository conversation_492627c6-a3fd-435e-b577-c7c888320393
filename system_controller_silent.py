#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学校管理系统控制器 - 静默版本
提供系统启动、停止、状态检查的Web API（无控制台输出）
"""

import os
import sys
import subprocess
import time
import threading
import webbrowser
import psutil
import socket
import logging
from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS

# 配置日志（写入文件而不是控制台）
logging.basicConfig(
    filename='launcher.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='a'
)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 禁用Flask的控制台日志
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)

# 系统配置
SYSTEMS = {
    'book': {
        'name': '图书管理系统',
        'port': 8000,
        'path': 'BookManager',
        'command': ['python', 'manage.py', 'runserver', '127.0.0.1:8000'],
        'process': None,
        'url': 'http://127.0.0.1:8000'
    },
    'academic': {
        'name': '中学教务管理系统',
        'port': 8001,
        'path': 'SchoolAcademicManager',
        'command': ['python', 'manage.py', 'runserver', '127.0.0.1:8001'],
        'process': None,
        'url': 'http://127.0.0.1:8001'
    },
    'finance': {
        'name': '学校财务管理系统',
        'port': 8002,
        'path': 'SchoolFinanceManager',
        'command': ['python', 'manage.py', 'runserver', '127.0.0.1:8002'],
        'process': None,
        'url': 'http://127.0.0.1:8002'
    }
}

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def get_system_status():
    """获取所有系统的运行状态"""
    status = {}
    for key, system in SYSTEMS.items():
        port_active = check_port(system['port'])
        status[key] = {
            'name': system['name'],
            'port': system['port'],
            'running': port_active,
            'url': system['url']
        }
    return status

@app.route('/')
def index():
    """主页面"""
    try:
        with open('system_launcher.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "启动器页面文件未找到，请确保 system_launcher.html 文件存在。"

@app.route('/api/status')
def api_status():
    """获取系统状态API"""
    return jsonify(get_system_status())

@app.route('/api/start/<system_id>')
def api_start(system_id):
    """启动系统API"""
    if system_id not in SYSTEMS:
        return jsonify({'success': False, 'message': '系统不存在'})
    
    system = SYSTEMS[system_id]
    
    # 检查是否已经运行
    if check_port(system['port']):
        return jsonify({'success': False, 'message': f"{system['name']}已经在运行"})
    
    try:
        # 切换到系统目录
        original_dir = os.getcwd()
        system_dir = os.path.join(original_dir, system['path'])
        
        if not os.path.exists(system_dir):
            return jsonify({'success': False, 'message': f"系统目录不存在: {system_dir}"})
        
        # 启动系统进程（完全隐藏）
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            process = subprocess.Popen(
                system['command'],
                cwd=system_dir,
                startupinfo=startupinfo,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:  # Linux/Mac系统
            process = subprocess.Popen(
                system['command'],
                cwd=system_dir,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        
        SYSTEMS[system_id]['process'] = process
        logging.info(f"启动系统: {system['name']}")
        
        # 等待系统启动
        time.sleep(3)
        
        # 检查是否启动成功
        if check_port(system['port']):
            return jsonify({
                'success': True, 
                'message': f"{system['name']}启动成功",
                'url': system['url']
            })
        else:
            return jsonify({'success': False, 'message': f"{system['name']}启动失败"})
            
    except Exception as e:
        logging.error(f"启动系统失败: {system['name']}, 错误: {str(e)}")
        return jsonify({'success': False, 'message': f"启动失败: {str(e)}"})

@app.route('/api/stop/<system_id>')
def api_stop(system_id):
    """停止系统API - 简化版本，避免psutil兼容性问题"""
    if system_id not in SYSTEMS:
        return jsonify({'success': False, 'message': '系统不存在'})

    system = SYSTEMS[system_id]

    try:
        # 方法1: 直接使用系统命令通过端口终止进程（隐藏窗口）
        try:
            # 使用netstat查找进程ID，然后用taskkill终止
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

                result = subprocess.run([
                    'powershell', '-Command',
                    f'$pid = (Get-NetTCPConnection -LocalPort {system["port"]} -ErrorAction SilentlyContinue).OwningProcess; if ($pid) {{ Stop-Process -Id $pid -Force }}'
                ], capture_output=True, text=True, timeout=15,
                startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run([
                    'powershell', '-Command',
                    f'$pid = (Get-NetTCPConnection -LocalPort {system["port"]} -ErrorAction SilentlyContinue).OwningProcess; if ($pid) {{ Stop-Process -Id $pid -Force }}'
                ], capture_output=True, text=True, timeout=15)

            logging.info(f"PowerShell停止命令执行完成: {system['name']}")
        except Exception as e:
            logging.warning(f"PowerShell方法失败: {e}")

        # 方法2: 使用cmd命令（隐藏窗口）
        try:
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

                subprocess.run([
                    'cmd', '/c',
                    f'for /f "tokens=5" %a in (\'netstat -ano ^| findstr ":{system["port"]}" ^| findstr "LISTENING"\') do taskkill /f /pid %a'
                ], capture_output=True, timeout=10,
                startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                subprocess.run([
                    'cmd', '/c',
                    f'for /f "tokens=5" %a in (\'netstat -ano ^| findstr ":{system["port"]}" ^| findstr "LISTENING"\') do taskkill /f /pid %a'
                ], capture_output=True, timeout=10)

            logging.info(f"CMD停止命令执行完成: {system['name']}")
        except Exception as e:
            logging.warning(f"CMD方法失败: {e}")

        # 方法3: 如果有进程引用，直接终止
        if SYSTEMS[system_id]['process'] and SYSTEMS[system_id]['process'].poll() is None:
            try:
                SYSTEMS[system_id]['process'].terminate()
                SYSTEMS[system_id]['process'].wait(timeout=5)
                logging.info(f"通过进程引用停止系统: {system['name']}")
            except:
                pass

        # 方法4: 简单的psutil方法（只检查命令行，不检查连接）
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and any(f"127.0.0.1:{system['port']}" in arg for arg in cmdline):
                        proc.terminate()
                        proc.wait(timeout=5)
                        logging.info(f"通过psutil停止系统: {system['name']}")
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue
        except Exception as e:
            logging.warning(f"psutil方法失败: {e}")

        # 清理进程引用
        SYSTEMS[system_id]['process'] = None

        # 等待端口释放
        time.sleep(2)

        # 检查是否成功停止
        if not check_port(system['port']):
            logging.info(f"成功停止系统: {system['name']}")
            return jsonify({'success': True, 'message': f"{system['name']}已停止"})
        else:
            logging.warning(f"系统可能仍在运行: {system['name']}")
            return jsonify({'success': False, 'message': f"{system['name']}停止可能失败，建议使用强制停止"})

    except Exception as e:
        logging.error(f"停止系统异常: {system['name']}, 错误: {str(e)}")
        return jsonify({'success': False, 'message': f"停止失败: {str(e)}"})

@app.route('/api/open/<system_id>')
def api_open(system_id):
    """打开系统页面"""
    if system_id not in SYSTEMS:
        return jsonify({'success': False, 'message': '系统不存在'})

    system = SYSTEMS[system_id]

    if not check_port(system['port']):
        return jsonify({'success': False, 'message': f"{system['name']}未运行"})

    try:
        webbrowser.open(system['url'])
        return jsonify({'success': True, 'message': f"已打开{system['name']}"})
    except Exception as e:
        return jsonify({'success': False, 'message': f"打开失败: {str(e)}"})

@app.route('/api/force_stop', methods=['POST'])
def api_force_stop():
    """强制停止所有系统"""
    try:
        # 执行Python强制停止脚本（隐藏窗口）
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            result = subprocess.run([
                'python', 'kill_systems.py'
            ], capture_output=True, text=True, timeout=30, cwd=os.getcwd(),
            startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            result = subprocess.run([
                'python', 'kill_systems.py'
            ], capture_output=True, text=True, timeout=30, cwd=os.getcwd())

        logging.info("执行强制停止脚本")

        # 清理所有进程引用
        for system_id in SYSTEMS:
            SYSTEMS[system_id]['process'] = None

        return jsonify({
            'success': True,
            'message': '强制停止命令已执行，请稍后刷新页面检查状态'
        })

    except Exception as e:
        logging.error(f"强制停止失败: {str(e)}")
        return jsonify({'success': False, 'message': f"强制停止失败: {str(e)}"})

@app.route('/api/diagnose')
def api_diagnose():
    """系统诊断API"""
    try:
        diagnosis = {
            'systems': {},
            'processes': [],
            'recommendations': []
        }

        # 检查每个系统的详细状态
        for system_id, system in SYSTEMS.items():
            port_active = check_port(system['port'])
            process_info = None

            # 查找对应的进程信息
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and any(f"127.0.0.1:{system['port']}" in arg for arg in cmdline):
                        process_info = {
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'create_time': proc.info['create_time'],
                            'cmdline': ' '.join(cmdline)
                        }
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            diagnosis['systems'][system_id] = {
                'name': system['name'],
                'port': system['port'],
                'running': port_active,
                'process': process_info,
                'url': system['url']
            }

        # 获取所有Python进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower():
                    diagnosis['processes'].append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': ' '.join(proc.info.get('cmdline', []))
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # 生成建议
        running_count = sum(1 for sys in diagnosis['systems'].values() if sys['running'])
        if running_count == 0:
            diagnosis['recommendations'].append("所有系统都未运行，建议使用启动器启动需要的系统")
        elif running_count < len(SYSTEMS):
            diagnosis['recommendations'].append("部分系统未运行，可以根据需要启动其他系统")
        else:
            diagnosis['recommendations'].append("所有系统都在正常运行")

        return jsonify(diagnosis)

    except Exception as e:
        logging.error(f"诊断失败: {str(e)}")
        return jsonify({'error': f"诊断失败: {str(e)}"})

def cleanup_on_exit():
    """程序退出时清理进程"""
    for system_id, system in SYSTEMS.items():
        if system['process'] and system['process'].poll() is None:
            try:
                system['process'].terminate()
                system['process'].wait(timeout=5)
            except:
                pass

if __name__ == '__main__':
    import atexit
    atexit.register(cleanup_on_exit)
    
    # 记录启动信息到日志文件
    logging.info("学校管理系统控制器启动")
    logging.info("控制器地址: http://127.0.0.1:5000")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        # 静默运行Flask应用
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        logging.info("控制器已停止")
        cleanup_on_exit()
