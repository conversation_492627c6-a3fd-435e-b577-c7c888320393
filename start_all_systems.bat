@echo off
chcp 65001
echo ========================================
echo    学校管理系统集合启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 正在启动所有学校管理系统...
echo.

REM 创建日志目录
if not exist "logs" mkdir logs

echo [1/4] 启动图书管理系统 (端口: 8000)...
start "图书管理系统" cmd /k "cd BookManager && python manage.py runserver 127.0.0.1:8000"
timeout /t 3 >nul

echo [2/4] 启动财务管理系统 (端口: 8002)...
start "财务管理系统" cmd /k "cd SchoolFinanceManager && python manage.py runserver 127.0.0.1:8002"
timeout /t 3 >nul

echo [3/4] 启动教务管理系统 (端口: 8001)...
start "教务管理系统" cmd /k "cd SchoolAcademicManager && python manage.py runserver 127.0.0.1:8001"
timeout /t 3 >nul

echo [4/4] 启动系统导航页面...
timeout /t 5 >nul

REM 打开系统导航页面
start "" "index.html"

echo.
echo ========================================
echo    所有系统启动完成！
echo ========================================
echo.
echo 系统访问地址：
echo ┌─────────────────────────────────────┐
echo │  系统导航页面: index.html           │
echo │                                     │
echo │  图书管理系统: http://127.0.0.1:8000│
echo │  教务管理系统: http://127.0.0.1:8001│
echo │  财务管理系统: http://127.0.0.1:8002│
echo └─────────────────────────────────────┘
echo.
echo 默认管理员账户: admin / admin123
echo.
echo 注意事项：
echo - 请保持所有命令行窗口打开
echo - 关闭任一窗口将停止对应系统
echo - 按 Ctrl+C 可停止单个系统
echo.
echo 按任意键退出启动脚本...
pause >nul
