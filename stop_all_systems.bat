@echo off
chcp 65001
echo ========================================
echo    停止所有学校管理系统
echo ========================================
echo.

echo 正在停止所有Django服务器...

REM 停止所有Python进程（Django服务器）
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1

REM 停止占用端口的进程
echo 正在释放端口...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8002') do taskkill /f /pid %%a >nul 2>&1

echo.
echo ✓ 所有系统已停止
echo.
echo 端口状态检查：
netstat -an | findstr "8000 8001 8002" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo ✓ 所有端口已释放
) else (
    echo ⚠ 部分端口可能仍被占用，请手动检查
)

echo.
echo 按任意键退出...
pause >nul
