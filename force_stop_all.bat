@echo off
chcp 65001
title 强制停止所有系统

echo ========================================
echo    🛑 强制停止所有学校管理系统
echo ========================================
echo.

echo 正在强制停止所有系统...
echo.

REM 方法1: 直接通过端口查找并终止进程
echo 🔄 方法1: 通过端口直接终止进程...

for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":8000" ^| findstr "LISTENING"') do (
    echo 终止图书管理系统进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":8001" ^| findstr "LISTENING"') do (
    echo 终止教务管理系统进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":8002" ^| findstr "LISTENING"') do (
    echo 终止财务管理系统进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":5000" ^| findstr "LISTENING"') do (
    echo 终止启动器进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

timeout /t 2 >nul

REM 方法2: 终止所有Python进程
echo 🔄 方法2: 终止所有Python进程...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1

timeout /t 2 >nul

REM 方法3: 使用WMIC强制终止
echo 🔄 方法3: 使用WMIC强制终止...
wmic process where "name='python.exe'" delete >nul 2>&1
wmic process where "name='pythonw.exe'" delete >nul 2>&1

timeout /t 2 >nul

REM 方法4: 使用PowerShell强制终止
echo 🔄 方法4: 使用PowerShell强制终止...
powershell -Command "Get-Process python* -ErrorAction SilentlyContinue | Stop-Process -Force" >nul 2>&1

timeout /t 3 >nul

echo.
echo ✅ 强制停止完成！
echo.

REM 验证结果
echo 🔍 验证停止结果...
echo.

set /a stopped_count=0

netstat -ano | findstr ":8000" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo ✅ 图书管理系统已停止
    set /a stopped_count+=1
) else (
    echo ❌ 图书管理系统仍在运行
)

netstat -ano | findstr ":8001" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo ✅ 教务管理系统已停止
    set /a stopped_count+=1
) else (
    echo ❌ 教务管理系统仍在运行
)

netstat -ano | findstr ":8002" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo ✅ 财务管理系统已停止
    set /a stopped_count+=1
) else (
    echo ❌ 财务管理系统仍在运行
)

netstat -ano | findstr ":5000" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo ✅ 启动器已停止
    set /a stopped_count+=1
) else (
    echo ❌ 启动器仍在运行
)

echo.
if %stopped_count% geq 3 (
    echo 🎉 大部分系统已成功停止！
) else (
    echo ⚠️ 部分系统可能仍在运行
    echo.
    echo 💡 如果仍有系统运行，请尝试：
    echo   1. 重启计算机
    echo   2. 检查是否有其他程序占用端口
    echo   3. 使用任务管理器手动结束进程
)

echo.
echo ========================================
echo 按任意键退出...
pause >nul
