#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
试用期机器绑定系统 - 确保试用期也只能在一台机器上使用
"""

import os
import sys
import hashlib
import json
import platform
import subprocess
import uuid
from datetime import datetime, timedelta
import base64

class TrialMachineBinding:
    def __init__(self, trial_file="trial_machine.dat", trial_days=60):
        self.trial_file = trial_file
        self.trial_days = trial_days
        self.machine_id = None
        self.trial_data = None
        
    def get_machine_fingerprint(self):
        """获取机器硬件指纹 - 与正式版相同的算法"""
        try:
            fingerprint_data = []
            
            # 1. CPU信息
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        cpu_id = result.stdout.strip().split('\n')[-1].strip()
                        if cpu_id and cpu_id != "ProcessorId":
                            fingerprint_data.append(f"CPU_ID:{cpu_id}")
                    
                    result = subprocess.run(['wmic', 'cpu', 'get', 'Name'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        cpu_name = result.stdout.strip().split('\n')[-1].strip()
                        if cpu_name and cpu_name != "Name":
                            fingerprint_data.append(f"CPU_NAME:{cpu_name}")
            except:
                pass
            
            # 2. 主板信息
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'SerialNumber'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        board_serial = result.stdout.strip().split('\n')[-1].strip()
                        if board_serial and board_serial != "SerialNumber":
                            fingerprint_data.append(f"BOARD_SERIAL:{board_serial}")
            except:
                pass
            
            # 3. 硬盘信息
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'SerialNumber'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:
                            disk_serial = line.strip()
                            if disk_serial and disk_serial != "SerialNumber" and disk_serial:
                                fingerprint_data.append(f"DISK:{disk_serial}")
                                break
            except:
                pass
            
            # 4. MAC地址
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                fingerprint_data.append(f"MAC:{mac}")
            except:
                pass
            
            # 5. 系统信息
            try:
                system_info = f"{platform.system()}:{platform.release()}:{platform.machine()}"
                fingerprint_data.append(f"SYSTEM:{system_info}")
            except:
                pass
            
            # 6. 用户名
            try:
                username = os.getenv('USERNAME') or os.getenv('USER') or 'unknown'
                fingerprint_data.append(f"USER:{username}")
            except:
                pass
            
            # 如果没有获取到足够信息，使用备用方法
            if len(fingerprint_data) < 3:
                fingerprint_data.append(f"FALLBACK_NODE:{platform.node()}")
                fingerprint_data.append(f"FALLBACK_UUID:{str(uuid.uuid4())}")
            
            # 生成最终指纹
            fingerprint_str = "|".join(sorted(fingerprint_data))
            machine_id = hashlib.sha256(fingerprint_str.encode()).hexdigest()
            
            return machine_id, fingerprint_data
            
        except Exception as e:
            # 备用方法
            fallback_data = f"{platform.system()}:{platform.node()}:{os.getenv('USERNAME', 'user')}"
            machine_id = hashlib.sha256(fallback_data.encode()).hexdigest()
            return machine_id, [f"FALLBACK:{fallback_data}"]
    
    def start_trial(self):
        """开始试用期 - 首次运行时调用"""
        try:
            # 获取当前机器指纹
            current_machine_id, fingerprint = self.get_machine_fingerprint()
            self.machine_id = current_machine_id
            
            # 检查是否已经有试用记录
            if os.path.exists(self.trial_file):
                existing_trial = self.load_trial_data()
                if existing_trial:
                    # 验证机器ID
                    if existing_trial.get("machine_id") != current_machine_id:
                        return False, f"试用期已在其他机器上开始\n当前机器ID: {current_machine_id[:16]}...\n试用机器ID: {existing_trial.get('machine_id', 'Unknown')[:16]}...\n\n此软件的试用期只能在一台机器上使用"
                    
                    # 已经在当前机器上开始试用
                    self.trial_data = existing_trial
                    return True, "试用期已在当前机器上激活"
            
            # 创建新的试用记录
            trial_data = {
                "machine_id": current_machine_id,
                "start_date": datetime.now().isoformat(),
                "end_date": (datetime.now() + timedelta(days=self.trial_days)).isoformat(),
                "trial_days": self.trial_days,
                "version": "1.0",
                "product": "SchoolAcademicManager",
                "trial_type": "MACHINE_BOUND",
                "fingerprint": fingerprint[:5]  # 保存部分指纹用于验证
            }
            
            # 保存试用数据
            if self.save_trial_data(trial_data):
                self.trial_data = trial_data
                return True, f"试用期已开始，有效期 {self.trial_days} 天"
            else:
                return False, "无法创建试用记录"
                
        except Exception as e:
            return False, f"开始试用期失败: {e}"
    
    def validate_trial(self):
        """验证试用期状态"""
        try:
            # 获取当前机器指纹
            current_machine_id, _ = self.get_machine_fingerprint()
            self.machine_id = current_machine_id
            
            # 加载试用数据
            trial_data = self.load_trial_data()
            if not trial_data:
                return False, "未找到试用记录，请重新安装软件开始试用"
            
            self.trial_data = trial_data
            
            # 验证机器ID
            if trial_data.get("machine_id") != current_machine_id:
                return False, f"试用期与当前机器不匹配\n\n此软件的试用期只能在一台机器上使用\n如需在新机器上试用，请重新下载安装"
            
            # 验证试用期是否过期
            try:
                end_date = datetime.fromisoformat(trial_data.get("end_date"))
                if datetime.now() > end_date:
                    return False, f"试用期已过期\n过期时间: {end_date.strftime('%Y-%m-%d %H:%M:%S')}\n\n请购买正式版本继续使用"
            except:
                return False, "试用期数据格式错误"
            
            return True, "试用期验证通过"
            
        except Exception as e:
            return False, f"试用期验证失败: {e}"
    
    def get_trial_info(self):
        """获取试用期信息"""
        if not self.trial_data:
            return None
        
        try:
            start_date = datetime.fromisoformat(self.trial_data.get("start_date"))
            end_date = datetime.fromisoformat(self.trial_data.get("end_date"))
            days_left = (end_date - datetime.now()).days
            total_days = self.trial_data.get("trial_days", self.trial_days)
            days_used = total_days - max(0, days_left)
            
            info = {
                "machine_id": self.trial_data.get("machine_id"),
                "start_date": start_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d"),
                "days_left": max(0, days_left),
                "days_used": days_used,
                "total_days": total_days,
                "trial_type": self.trial_data.get("trial_type", "MACHINE_BOUND"),
                "product": self.trial_data.get("product", "Unknown")
            }
            
            return info
            
        except Exception as e:
            print(f"获取试用期信息失败: {e}")
            return None
    
    def save_trial_data(self, trial_data):
        """保存试用数据"""
        try:
            # 对试用数据进行编码
            trial_json = json.dumps(trial_data, sort_keys=True)
            encoded_trial = base64.b64encode(trial_json.encode()).decode()
            
            # 添加校验和
            checksum = hashlib.sha256(encoded_trial.encode()).hexdigest()[:16]
            final_trial = f"{encoded_trial}.{checksum}"
            
            with open(self.trial_file, 'w') as f:
                f.write(final_trial)
            
            return True
        except Exception as e:
            print(f"保存试用数据失败: {e}")
            return False
    
    def load_trial_data(self):
        """加载试用数据"""
        try:
            if not os.path.exists(self.trial_file):
                return None
            
            with open(self.trial_file, 'r') as f:
                trial_content = f.read().strip()
            
            # 验证校验和
            if '.' not in trial_content:
                return None
            
            encoded_trial, checksum = trial_content.rsplit('.', 1)
            expected_checksum = hashlib.sha256(encoded_trial.encode()).hexdigest()[:16]
            
            if checksum != expected_checksum:
                return None
            
            # 解码试用数据
            trial_json = base64.b64decode(encoded_trial.encode()).decode()
            trial_data = json.loads(trial_json)
            
            return trial_data
            
        except Exception as e:
            print(f"加载试用数据失败: {e}")
            return None
    
    def is_trial_active(self):
        """检查试用期是否有效"""
        valid, _ = self.validate_trial()
        return valid
    
    def get_machine_id(self):
        """获取当前机器ID"""
        if not self.machine_id:
            self.machine_id, _ = self.get_machine_fingerprint()
        return self.machine_id

def test_trial_system():
    """测试试用期系统"""
    print("=" * 60)
    print("试用期机器绑定系统测试")
    print("=" * 60)
    
    trial_system = TrialMachineBinding(trial_days=60)
    
    # 获取机器信息
    machine_id, fingerprint = trial_system.get_machine_fingerprint()
    print(f"当前机器ID: {machine_id}")
    print("硬件指纹:")
    for fp in fingerprint:
        print(f"  {fp}")
    print()
    
    # 开始试用期
    success, message = trial_system.start_trial()
    print(f"开始试用期: {'成功' if success else '失败'}")
    print(f"消息: {message}")
    print()
    
    # 验证试用期
    valid, validation_message = trial_system.validate_trial()
    print(f"试用期验证: {'通过' if valid else '失败'}")
    print(f"验证消息: {validation_message}")
    print()
    
    # 获取试用期信息
    if valid:
        trial_info = trial_system.get_trial_info()
        if trial_info:
            print("试用期信息:")
            print(f"  开始日期: {trial_info['start_date']}")
            print(f"  结束日期: {trial_info['end_date']}")
            print(f"  剩余天数: {trial_info['days_left']}")
            print(f"  已使用天数: {trial_info['days_used']}")
            print(f"  总天数: {trial_info['total_days']}")
            print(f"  试用类型: {trial_info['trial_type']}")
    
    print("=" * 60)

if __name__ == '__main__':
    test_trial_system()
