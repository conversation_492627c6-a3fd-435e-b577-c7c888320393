#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试启动器 - 用于诊断问题
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

def test_imports():
    """测试导入"""
    try:
        print("测试基本导入...")
        import hashlib
        import json
        import datetime
        from pathlib import Path
        print("✓ 基本模块导入成功")
        
        print("测试Django导入...")
        import django
        print(f"✓ Django版本: {django.get_version()}")
        
        print("测试Django设置...")
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')
        django.setup()
        print("✓ Django设置成功")
        
        print("测试Django管理命令...")
        from django.core.management import execute_from_command_line
        print("✓ Django管理命令导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        traceback.print_exc()
        return False

def create_simple_gui():
    """创建简单的GUI"""
    try:
        root = tk.Tk()
        root.title("测试启动器")
        root.geometry("400x300")
        
        # 获取exe文件所在目录
        if getattr(sys, 'frozen', False):
            base_dir = Path(sys.executable).parent
            mode = "打包模式"
        else:
            base_dir = Path(__file__).parent
            mode = "开发模式"
        
        tk.Label(root, text="中学教务管理系统测试", 
                font=("Microsoft YaHei", 14, "bold")).pack(pady=20)
        
        tk.Label(root, text=f"运行模式: {mode}", 
                font=("Microsoft YaHei", 10)).pack(pady=5)
        
        tk.Label(root, text=f"基础目录: {base_dir}", 
                font=("Microsoft YaHei", 9)).pack(pady=5)
        
        def test_django():
            if test_imports():
                messagebox.showinfo("成功", "Django导入和设置成功！")
            else:
                messagebox.showerror("失败", "Django导入或设置失败，请查看控制台输出")
        
        def start_django():
            try:
                import django
                from django.core.management import execute_from_command_line
                
                os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')
                django.setup()
                
                # 启动Django服务器
                import threading
                def run_server():
                    execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8001', '--noreload'])
                
                threading.Thread(target=run_server, daemon=True).start()
                messagebox.showinfo("启动", "Django服务器已在后台启动")
                
            except Exception as e:
                messagebox.showerror("错误", f"启动Django失败: {e}")
                traceback.print_exc()
        
        tk.Button(root, text="测试Django导入", command=test_django,
                 font=("Microsoft YaHei", 10)).pack(pady=10)
        
        tk.Button(root, text="启动Django服务器", command=start_django,
                 font=("Microsoft YaHei", 10)).pack(pady=10)
        
        tk.Button(root, text="退出", command=root.destroy,
                 font=("Microsoft YaHei", 10)).pack(pady=10)
        
        return root
    except Exception as e:
        print(f"创建GUI失败: {e}")
        traceback.print_exc()
        return None

def main():
    print("=" * 50)
    print("中学教务管理系统测试启动器")
    print("=" * 50)
    
    try:
        # 测试基本导入
        print("1. 测试基本导入...")
        if not test_imports():
            print("基本导入失败，程序退出")
            input("按回车键退出...")
            return
        
        # 创建GUI
        print("2. 创建GUI界面...")
        root = create_simple_gui()
        if root is None:
            print("GUI创建失败，程序退出")
            input("按回车键退出...")
            return
        
        print("3. 启动GUI...")
        root.mainloop()
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
