@echo off
chcp 65001
title 运行所有Django测试

echo ========================================
echo    🧪 Django问题诊断测试套件
echo ========================================
echo.

echo 📋 可用的测试程序:
echo.
echo [1] Django环境测试 - 测试Django基本功能
echo [2] 调试版启动器 - 完整的系统启动器（修复版）
echo [3] 退出
echo.

:menu
set /p choice=请选择要运行的测试 (1-3): 

if "%choice%"=="1" goto django_test
if "%choice%"=="2" goto debug_launcher
if "%choice%"=="3" goto exit
echo 无效选择，请重新输入
goto menu

:django_test
echo.
echo 🧪 运行Django环境测试...
echo 这个测试会检查Django的各个组件是否正常工作
echo.
if exist "dist\Django测试程序.exe" (
    start "" "dist\Django测试程序.exe"
    echo ✅ Django测试程序已启动
    echo 💡 请查看测试结果，所有测试都应该通过
) else (
    echo ❌ Django测试程序不存在
    echo 请先运行 build_django_test.py 进行打包
)
echo.
pause
goto menu

:debug_launcher
echo.
echo 🐛 运行调试版启动器...
echo 这个版本修复了编码问题，提供更详细的错误信息
echo.
if exist "dist\中学教务管理系统_调试版.exe" (
    start "" "dist\中学教务管理系统_调试版.exe"
    echo ✅ 调试版启动器已启动
    echo.
    echo 💡 使用说明:
    echo   1. 程序会自动检查环境
    echo   2. 点击"启动系统"测试Django启动
    echo   3. 查看日志窗口中的详细信息
    echo   4. 如果有错误，日志会显示具体原因
) else (
    echo ❌ 调试版启动器不存在
    echo 请先运行 build_debug_exe.py 进行打包
)
echo.
pause
goto menu

:exit
echo.
echo 👋 测试完成，再见！
timeout /t 2 >nul
exit

echo.
echo ========================================
echo 💡 故障排除提示:
echo.
echo 如果Django环境测试失败:
echo   • 检查是否缺少依赖包
echo   • 确认Django项目文件完整
echo   • 查看具体的错误信息
echo.
echo 如果启动器测试失败:
echo   • 查看调试版的详细日志
echo   • 检查端口8001是否被占用
echo   • 确认数据库文件是否正常
echo.
echo ========================================
pause
