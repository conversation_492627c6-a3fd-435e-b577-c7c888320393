#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中学教务管理系统打包脚本
使用PyInstaller将系统打包成Windows可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """检查打包所需的依赖"""
    required_packages = {
        'cryptography': 'cryptography',
        'wmi': 'wmi'
    }

    missing_packages = []
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    # 检查PyInstaller命令是否可用
    try:
        result = subprocess.run(['pyinstaller', '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            missing_packages.append('pyinstaller')
    except:
        missing_packages.append('pyinstaller')

    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ 所有依赖包已安装")
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['academic_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('SchoolAcademicManager', 'SchoolAcademicManager'),
        ('trial_manager.py', '.'),
        ('django_utils.py', '.'),
    ],
    hiddenimports=[
        'django',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.core.management',
        'django.core.management.commands',
        'django.core.management.commands.runserver',
        'django.apps',
        'django.apps.registry',
        'django.conf',
        'django.urls',
        'django.utils.log',
        'import_export',
        'import_export.admin',
        'import_export.resources',
        'import_export.fields',
        'import_export.widgets',
        'import_export.formats',
        'import_export.formats.base_formats',
        'colorfield',
        'colorfield.fields',
        'openpyxl',
        'xlwt',
        'xlrd',
        'academic',
        'academic.models',
        'academic.views',
        'academic.admin',
        'academic.apps',
        'academic.urls',
        'SchoolAcademicManager',
        'SchoolAcademicManager.settings',
        'SchoolAcademicManager.urls',
        'SchoolAcademicManager.wsgi',
        'cryptography',
        'wmi',
        'winreg',
        'django_utils',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='中学教务管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
    version='version_info.txt'
)
'''
    
    with open('academic_system.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建PyInstaller规格文件")

def create_version_info():
    """创建版本信息文件"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'学校管理系统开发团队'),
        StringStruct(u'FileDescription', u'中学教务管理系统'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'SchoolAcademicManager'),
        StringStruct(u'LegalCopyright', u'© 2024 学校管理系统开发团队'),
        StringStruct(u'OriginalFilename', u'中学教务管理系统.exe'),
        StringStruct(u'ProductName', u'中学教务管理系统'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ 已创建版本信息文件")

def create_icon():
    """创建图标文件（如果不存在）"""
    if not os.path.exists('academic.ico'):
        print("⚠️ 未找到图标文件 academic.ico，将使用默认图标")
        # 这里可以创建一个简单的图标或使用默认图标
        return False
    return True

def prepare_django_project():
    """准备Django项目"""
    academic_dir = Path("SchoolAcademicManager")
    if not academic_dir.exists():
        print("❌ 未找到SchoolAcademicManager目录")
        return False
    
    # 检查必要文件
    required_files = [
        "manage.py",
        "SchoolAcademicManager/settings.py",
        "academic/models.py"
    ]
    
    for file_path in required_files:
        if not (academic_dir / file_path).exists():
            print(f"❌ 未找到必要文件: {file_path}")
            return False
    
    print("✅ Django项目检查通过")
    return True

def build_executable():
    """构建可执行文件"""
    print("\n🚀 开始构建可执行文件...")
    
    try:
        # 运行PyInstaller
        result = subprocess.run([
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'academic_system.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            return True
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def create_installer():
    """创建安装程序"""
    installer_script = '''
; 中学教务管理系统安装脚本
; 使用Inno Setup创建

[Setup]
AppName=中学教务管理系统
AppVersion=1.0
AppPublisher=学校管理系统开发团队
AppPublisherURL=http://www.schoolmanagement.com
DefaultDirName={autopf}\\SchoolAcademicManager
DefaultGroupName=学校管理系统
OutputDir=installer
OutputBaseFilename=中学教务管理系统_v1.0_安装程序
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "dist\\中学教务管理系统.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\\中学教务管理系统"; Filename: "{app}\\中学教务管理系统.exe"
Name: "{group}\\{cm:UninstallProgram,中学教务管理系统}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\中学教务管理系统"; Filename: "{app}\\中学教务管理系统.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\\中学教务管理系统.exe"; Description: "{cm:LaunchProgram,中学教务管理系统}"; Flags: nowait postinstall skipifsilent
'''
    
    with open('installer_script.iss', 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print("✅ 已创建安装程序脚本 (installer_script.iss)")
    print("💡 请使用Inno Setup编译此脚本以创建安装程序")

def create_readme():
    """创建说明文件"""
    readme_content = '''# 中学教务管理系统 v1.0

## 系统简介

中学教务管理系统是一款专为中学设计的教务管理软件，提供完整的学生、教师、课程和成绩管理功能。

## 主要功能

- **学生信息管理**: 学生档案、班级分配、学籍管理
- **教师管理**: 教师信息、任课安排、工作量统计
- **课程管理**: 课程设置、课表安排、教室分配
- **成绩管理**: 成绩录入、统计分析、报表生成
- **考勤管理**: 学生考勤、教师考勤、请假管理

## 试用说明

- **试用期限**: 60天
- **功能限制**: 试用期内功能完整，无限制
- **数据保护**: 试用期数据可完整保留至正式版

## 系统要求

- **操作系统**: Windows 7/8/10/11 (64位)
- **内存**: 最低2GB，推荐4GB以上
- **硬盘空间**: 最低500MB可用空间
- **网络**: 无需联网即可使用

## 安装说明

1. 双击安装程序
2. 按照向导提示完成安装
3. 首次运行会自动初始化数据库
4. 使用默认管理员账户登录: admin / admin123

## 技术支持

- **官方网站**: http://www.schoolmanagement.com
- **技术支持**: <EMAIL>
- **用户手册**: 软件内置帮助文档

## 许可证

本软件为商业软件，试用期60天。如需继续使用，请购买正式许可证。

---

© 2024 学校管理系统开发团队 版权所有
'''
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已创建README.md文件")

def main():
    """主函数"""
    print("=" * 60)
    print("🏫 中学教务管理系统打包工具")
    print("=" * 60)
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 检查Django项目
    if not prepare_django_project():
        return
    
    # 创建必要文件
    create_spec_file()
    create_version_info()
    create_icon()
    create_readme()
    
    # 构建可执行文件
    if build_executable():
        print("\n🎉 打包完成！")
        print(f"📁 可执行文件位置: dist/中学教务管理系统.exe")
        
        # 创建安装程序脚本
        create_installer()
        
        print("\n📋 后续步骤:")
        print("1. 测试 dist/中学教务管理系统.exe")
        print("2. 使用Inno Setup编译 installer_script.iss 创建安装程序")
        print("3. 分发安装程序给用户")
        
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == '__main__':
    main()
