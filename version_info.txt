# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'学校管理系统开发团队'),
        StringStruct(u'FileDescription', u'中学教务管理系统'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'SchoolAcademicManager'),
        StringStruct(u'LegalCopyright', u'© 2024 学校管理系统开发团队'),
        StringStruct(u'OriginalFilename', u'中学教务管理系统.exe'),
        StringStruct(u'ProductName', u'中学教务管理系统'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
