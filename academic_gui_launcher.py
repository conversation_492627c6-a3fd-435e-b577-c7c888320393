#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SchoolAcademicManager GUI启动器
包含试用期管理、许可证激活和系统启动功能
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import webbrowser
import time
import socket
import subprocess
from academic_trial_manager import AcademicTrialManager
from academic_license_validator import AcademicLicenseValidator

class AcademicGUILauncher:
    def __init__(self):
        self.trial_manager = AcademicTrialManager(trial_days=60)
        self.license_validator = AcademicLicenseValidator(self.trial_manager)
        self.server_process = None
        self.server_running = False
        self.root = None
        
    def create_gui(self):
        """创建图形界面"""
        self.root = tk.Tk()
        self.root.title("中学教务管理系统 v1.0")
        self.root.geometry("700x600")
        self.root.resizable(False, False)
        
        # 设置窗口图标（如果有的话）
        try:
            # 可以添加图标文件
            pass
        except:
            pass
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题区域
        self.create_header(main_frame)
        
        # 授权信息区域
        self.create_license_section(main_frame)
        
        # 系统控制区域
        self.create_control_section(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
        
        # 底部按钮
        self.create_footer_buttons(main_frame)
        
        # 初始化状态
        self.update_status()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 居中显示窗口
        self.center_window()
        
        return self.root
    
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 主标题
        title_label = ttk.Label(header_frame, text="中学教务管理系统", 
                               font=("Microsoft YaHei", 20, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 5))
        
        # 副标题
        subtitle_label = ttk.Label(header_frame, text="SchoolAcademicManager v1.0 - 专业版", 
                                  font=("Microsoft YaHei", 11))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # 分隔线
        separator = ttk.Separator(header_frame, orient='horizontal')
        separator.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def create_license_section(self, parent):
        """创建授权信息区域"""
        license_frame = ttk.LabelFrame(parent, text="授权信息", padding="15")
        license_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 状态显示
        self.status_label = ttk.Label(license_frame, text="正在检查授权状态...", 
                                     font=("Microsoft YaHei", 11, "bold"))
        self.status_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 详细信息
        self.detail_label = ttk.Label(license_frame, text="", 
                                     font=("Microsoft YaHei", 9))
        self.detail_label.grid(row=1, column=0, columnspan=3, pady=(0, 15))
        
        # 进度条（用于试用期显示）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(license_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 进度标签
        self.progress_label = ttk.Label(license_frame, text="", 
                                       font=("Microsoft YaHei", 8))
        self.progress_label.grid(row=3, column=0, columnspan=3, pady=(0, 15))
        
        # 按钮区域
        button_frame = ttk.Frame(license_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(0, 10))
        
        self.activate_btn = ttk.Button(button_frame, text="激活软件", 
                                      command=self.show_activation_dialog)
        self.activate_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.machine_info_btn = ttk.Button(button_frame, text="查看机器信息", 
                                          command=self.show_machine_info)
        self.machine_info_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.test_license_btn = ttk.Button(button_frame, text="生成测试许可证", 
                                          command=self.generate_test_license)
        self.test_license_btn.grid(row=0, column=2)
    
    def create_control_section(self, parent):
        """创建系统控制区域"""
        control_frame = ttk.LabelFrame(parent, text="系统控制", padding="15")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 系统状态
        self.system_status_label = ttk.Label(control_frame, text="系统状态：未启动", 
                                            font=("Microsoft YaHei", 10))
        self.system_status_label.grid(row=0, column=0, columnspan=3, pady=(0, 15))
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=1, column=0, columnspan=3)
        
        self.start_btn = ttk.Button(button_frame, text="启动系统", 
                                   command=self.start_system, state="disabled",
                                   width=15)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(button_frame, text="停止系统", 
                                  command=self.stop_system, state="disabled",
                                  width=15)
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.open_btn = ttk.Button(button_frame, text="打开系统", 
                                  command=self.open_system, state="disabled",
                                  width=15)
        self.open_btn.grid(row=0, column=2)
        
        # 系统信息
        info_frame = ttk.Frame(control_frame)
        info_frame.grid(row=2, column=0, columnspan=3, pady=(15, 0))
        
        ttk.Label(info_frame, text="访问地址：http://127.0.0.1:8001", 
                 font=("Microsoft YaHei", 9)).grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text="默认账户：admin / admin123", 
                 font=("Microsoft YaHei", 9)).grid(row=1, column=0, sticky=tk.W)
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(parent, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W, padding="5")
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def create_footer_buttons(self, parent):
        """创建底部按钮"""
        footer_frame = ttk.Frame(parent)
        footer_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(15, 0))
        
        # 左侧按钮
        ttk.Button(footer_frame, text="帮助", 
                  command=self.show_help).grid(row=0, column=0, sticky=tk.W)
        
        # 右侧按钮
        right_frame = ttk.Frame(footer_frame)
        right_frame.grid(row=0, column=1, sticky=tk.E)
        
        ttk.Button(right_frame, text="关于", 
                  command=self.show_about).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(right_frame, text="退出", 
                  command=self.on_closing).grid(row=0, column=1)
        
        # 配置列权重
        footer_frame.columnconfigure(1, weight=1)
    
    def center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def update_status(self):
        """更新状态显示"""
        # 检查许可证状态
        license_status = self.license_validator.check_license_status()
        
        if license_status['licensed']:
            # 已激活许可证
            self.status_label.config(text="✅ 软件已激活", foreground="green")
            self.detail_label.config(text=f"许可证类型：{license_status['license_type']} | "
                                         f"到期日期：{license_status['expire_date']}")
            
            # 隐藏进度条
            self.progress_bar.grid_remove()
            self.progress_label.grid_remove()
            
            self.start_btn.config(state="normal")
            self.activate_btn.config(text="重新激活")
            
        else:
            # 检查试用期
            trial_status = self.trial_manager.check_trial_status()
            
            if trial_status['status'] == 'active':
                # 试用期有效
                days_remaining = trial_status['days_remaining']
                days_used = trial_status['days_used']
                total_days = trial_status['total_days']
                
                self.status_label.config(text=f"⏰ 试用期 (剩余 {days_remaining} 天)", 
                                        foreground="orange")
                self.detail_label.config(text=f"试用期：{trial_status['start_date']} 至 {trial_status['end_date']}")
                
                # 显示进度条
                self.progress_bar.grid()
                self.progress_label.grid()
                
                progress = (days_used / total_days) * 100
                self.progress_var.set(progress)
                self.progress_label.config(text=f"已使用 {days_used}/{total_days} 天 ({progress:.1f}%)")
                
                self.start_btn.config(state="normal")
                self.activate_btn.config(text="激活软件")
                
            else:
                # 试用期已过期
                self.status_label.config(text="❌ 试用期已过期", foreground="red")
                self.detail_label.config(text="请购买正式版本或输入有效的许可证密钥")
                
                # 显示满进度条
                self.progress_bar.grid()
                self.progress_label.grid()
                self.progress_var.set(100)
                self.progress_label.config(text="试用期已结束")
                
                self.start_btn.config(state="disabled")
                self.activate_btn.config(text="激活软件")
    
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False

    def show_activation_dialog(self):
        """显示激活对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("软件激活")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (250)
        y = (dialog.winfo_screenheight() // 2) - (200)
        dialog.geometry(f"500x400+{x}+{y}")

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="软件激活",
                 font=("Microsoft YaHei", 14, "bold")).pack(pady=(0, 20))

        # 说明
        info_text = """请输入您购买的许可证密钥来激活软件。

许可证密钥格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX

如果您还没有许可证，可以：
1. 联系销售人员购买正式版本
2. 使用下方的"生成测试许可证"按钮获取测试密钥"""

        ttk.Label(main_frame, text=info_text,
                 font=("Microsoft YaHei", 9), justify=tk.LEFT).pack(pady=(0, 20))

        # 输入框
        ttk.Label(main_frame, text="许可证密钥：").pack(anchor=tk.W)
        self.license_entry = ttk.Entry(main_frame, font=("Courier", 11), width=30)
        self.license_entry.pack(fill=tk.X, pady=(5, 20))
        self.license_entry.focus()

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="生成测试许可证",
                  command=lambda: self.insert_test_license(dialog)).pack(side=tk.LEFT)

        ttk.Button(button_frame, text="取消",
                  command=dialog.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="激活",
                  command=lambda: self.activate_license(dialog)).pack(side=tk.RIGHT, padx=(10, 0))

        # 绑定回车键
        dialog.bind('<Return>', lambda e: self.activate_license(dialog))

    def insert_test_license(self, dialog):
        """插入测试许可证"""
        test_license = self.license_validator.generate_license_key('STANDARD')
        self.license_entry.delete(0, tk.END)
        self.license_entry.insert(0, test_license)
        messagebox.showinfo("测试许可证", f"已生成测试许可证：\n{test_license}\n\n注意：这仅用于测试目的")

    def activate_license(self, dialog):
        """激活许可证"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            messagebox.showerror("错误", "请输入许可证密钥")
            return

        self.status_var.set("正在验证许可证...")
        dialog.destroy()

        # 在后台线程中验证
        def verify_license():
            success, message = self.license_validator.activate_license(license_key)
            self.root.after(0, lambda: self.on_license_result(success, message))

        threading.Thread(target=verify_license, daemon=True).start()

    def on_license_result(self, success, message):
        """许可证验证结果"""
        if success:
            messagebox.showinfo("激活成功", message)
            self.update_status()
        else:
            messagebox.showerror("激活失败", message)

        self.status_var.set("就绪")

    def generate_test_license(self):
        """生成测试许可证"""
        license_types = ['BASIC', 'STANDARD', 'PROFESSIONAL', 'ENTERPRISE', 'LIFETIME']

        # 选择许可证类型
        choice_dialog = tk.Toplevel(self.root)
        choice_dialog.title("选择许可证类型")
        choice_dialog.geometry("300x250")
        choice_dialog.resizable(False, False)
        choice_dialog.transient(self.root)
        choice_dialog.grab_set()

        # 居中显示
        choice_dialog.update_idletasks()
        x = (choice_dialog.winfo_screenwidth() // 2) - (150)
        y = (choice_dialog.winfo_screenheight() // 2) - (125)
        choice_dialog.geometry(f"300x250+{x}+{y}")

        frame = ttk.Frame(choice_dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="选择许可证类型：",
                 font=("Microsoft YaHei", 11, "bold")).pack(pady=(0, 15))

        selected_type = tk.StringVar(value='STANDARD')

        for license_type in license_types:
            info = self.license_validator.license_types[license_type]
            text = f"{info['name']} ({info['days']}天)"
            ttk.Radiobutton(frame, text=text, variable=selected_type,
                           value=license_type).pack(anchor=tk.W, pady=2)

        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        def generate_and_show():
            license_type = selected_type.get()
            test_license = self.license_validator.generate_license_key(license_type)
            choice_dialog.destroy()

            # 显示生成的许可证
            result_dialog = tk.Toplevel(self.root)
            result_dialog.title("测试许可证")
            result_dialog.geometry("450x300")
            result_dialog.resizable(False, False)
            result_dialog.transient(self.root)

            result_frame = ttk.Frame(result_dialog, padding="20")
            result_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(result_frame, text="生成的测试许可证",
                     font=("Microsoft YaHei", 12, "bold")).pack(pady=(0, 15))

            # 许可证显示
            license_frame = ttk.Frame(result_frame, relief=tk.SUNKEN, padding="10")
            license_frame.pack(fill=tk.X, pady=(0, 15))

            ttk.Label(license_frame, text=test_license,
                     font=("Courier", 14, "bold"), foreground="blue").pack()

            info_text = f"""许可证类型：{self.license_validator.license_types[license_type]['name']}
有效期：{self.license_validator.license_types[license_type]['days']} 天
机器ID：{self.trial_manager.machine_id[:16]}...

注意：此许可证仅用于测试目的"""

            ttk.Label(result_frame, text=info_text,
                     font=("Microsoft YaHei", 9), justify=tk.LEFT).pack(pady=(0, 15))

            # 按钮
            btn_frame = ttk.Frame(result_frame)
            btn_frame.pack(fill=tk.X)

            def copy_license():
                self.root.clipboard_clear()
                self.root.clipboard_append(test_license)
                messagebox.showinfo("成功", "许可证已复制到剪贴板")

            ttk.Button(btn_frame, text="复制许可证",
                      command=copy_license).pack(side=tk.LEFT)
            ttk.Button(btn_frame, text="关闭",
                      command=result_dialog.destroy).pack(side=tk.RIGHT)

        ttk.Button(button_frame, text="生成",
                  command=generate_and_show).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="取消",
                  command=choice_dialog.destroy).pack(side=tk.RIGHT, padx=(0, 10))

    def show_machine_info(self):
        """显示机器信息"""
        machine_info = self.trial_manager.get_machine_info()

        info_dialog = tk.Toplevel(self.root)
        info_dialog.title("机器信息")
        info_dialog.geometry("500x400")
        info_dialog.resizable(False, False)
        info_dialog.transient(self.root)

        # 居中显示
        info_dialog.update_idletasks()
        x = (info_dialog.winfo_screenwidth() // 2) - (250)
        y = (info_dialog.winfo_screenheight() // 2) - (200)
        info_dialog.geometry(f"500x400+{x}+{y}")

        frame = ttk.Frame(info_dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="机器信息",
                 font=("Microsoft YaHei", 14, "bold")).pack(pady=(0, 20))

        # 信息显示
        info_text = f"""机器标识：
{machine_info['machine_id']}

应用信息：
应用名称：{machine_info['app_name']}
应用版本：{machine_info['app_version']}
配置目录：{machine_info['config_dir']}

说明：
• 机器标识是根据硬件信息生成的唯一ID
• 购买许可证时需要提供此机器标识
• 更换主要硬件后机器标识可能会改变
• 请将机器标识发送给软件提供商获取许可证"""

        from tkinter import scrolledtext
        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD,
                                               font=("Courier", 9), height=15)
        text_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        text_widget.insert(tk.END, info_text)
        text_widget.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X)

        def copy_machine_id():
            self.root.clipboard_clear()
            self.root.clipboard_append(machine_info['machine_id'])
            messagebox.showinfo("成功", "机器标识已复制到剪贴板")

        ttk.Button(button_frame, text="复制机器标识",
                  command=copy_machine_id).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="关闭",
                  command=info_dialog.destroy).pack(side=tk.RIGHT)

    def start_system(self):
        """启动系统"""
        # 检查授权
        if not self.license_validator.can_run():
            messagebox.showerror("无法启动", "试用期已过期或许可证无效，请激活软件")
            return

        # 检查端口
        if self.check_port(8001):
            messagebox.showwarning("端口占用", "端口8001已被占用，请先停止占用该端口的程序")
            return

        self.status_var.set("正在启动系统...")
        self.start_btn.config(state="disabled")

        # 在后台线程中启动
        threading.Thread(target=self.start_django_server, daemon=True).start()

    def start_django_server(self):
        """启动Django服务器"""
        try:
            # 获取资源路径
            def get_resource_path(relative_path):
                try:
                    base_path = sys._MEIPASS
                except Exception:
                    base_path = os.path.abspath(".")
                return os.path.join(base_path, relative_path)

            # 查找SchoolAcademicManager目录
            academic_dir = None
            possible_paths = [
                get_resource_path("SchoolAcademicManager"),
                "SchoolAcademicManager",
                os.path.join(os.path.dirname(__file__), "SchoolAcademicManager"),
                os.path.join(os.getcwd(), "SchoolAcademicManager")
            ]

            # 调试信息
            debug_info = "正在查找SchoolAcademicManager目录...\n"
            for path in possible_paths:
                exists = os.path.exists(path)
                has_manage = os.path.exists(os.path.join(path, "manage.py")) if exists else False
                debug_info += f"路径: {path}\n  存在: {exists}\n  有manage.py: {has_manage}\n"

                if exists and has_manage:
                    academic_dir = os.path.abspath(path)
                    break

            if not academic_dir:
                # 尝试直接使用内置Django启动
                self.start_embedded_django()
                return

            # 使用subprocess启动Django
            self.start_subprocess_django(academic_dir)

        except Exception as e:
            self.server_running = False
            error_msg = str(e)
            self.root.after(0, lambda msg=error_msg: self.on_server_error(msg))

    def start_embedded_django(self):
        """使用内嵌方式启动Django"""
        try:
            # 设置Django环境
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')

            # 添加路径
            academic_path = None
            try:
                academic_path = sys._MEIPASS + "/SchoolAcademicManager"
            except:
                academic_path = "SchoolAcademicManager"

            if academic_path and academic_path not in sys.path:
                sys.path.insert(0, academic_path)

            # 导入Django
            import django
            from django.conf import settings
            from django.core.management import execute_from_command_line

            # 初始化Django
            if not settings.configured:
                django.setup()

            # 在新线程中启动Django服务器
            def run_server():
                try:
                    execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8001', '--noreload'])
                except Exception as e:
                    error_msg = f"Django内嵌启动失败: {str(e)}"
                    self.root.after(0, lambda msg=error_msg: self.on_server_error(msg))

            import threading
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            # 等待服务器启动
            max_wait = 15
            for i in range(max_wait):
                time.sleep(1)
                if self.check_port(8001):
                    self.server_running = True
                    self.root.after(0, self.on_server_started)
                    return

            raise Exception("Django内嵌启动超时")

        except Exception as e:
            error_msg = f"Django内嵌启动失败: {str(e)}"
            self.root.after(0, lambda msg=error_msg: self.on_server_error(msg))

    def start_subprocess_django(self, academic_dir):
        """使用子进程启动Django"""
        try:
            # 检查manage.py文件
            manage_py = os.path.join(academic_dir, "manage.py")
            if not os.path.exists(manage_py):
                raise Exception(f"找不到manage.py文件: {manage_py}")

            # 启动Django服务器
            python_exe = sys.executable
            cmd = [python_exe, "manage.py", "runserver", "127.0.0.1:8001", "--noreload"]

            # 设置环境变量
            env = os.environ.copy()
            env['DJANGO_SETTINGS_MODULE'] = 'SchoolAcademicManager.settings'
            env['PYTHONPATH'] = academic_dir

            self.server_process = subprocess.Popen(
                cmd,
                cwd=academic_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            # 等待服务器启动
            max_wait = 20
            for i in range(max_wait):
                time.sleep(1)

                # 检查进程是否还在运行
                if self.server_process.poll() is not None:
                    # 获取错误输出
                    try:
                        stdout, stderr = self.server_process.communicate(timeout=1)
                        error_msg = f"Django进程退出，返回码: {self.server_process.returncode}\n"
                        if stderr:
                            error_msg += f"错误输出: {stderr}\n"
                        if stdout:
                            error_msg += f"标准输出: {stdout}"
                    except:
                        error_msg = f"Django进程退出，返回码: {self.server_process.returncode}"
                    raise Exception(error_msg)

                # 检查端口是否开始监听
                if self.check_port(8001):
                    self.server_running = True
                    self.root.after(0, self.on_server_started)
                    return

            # 启动超时
            raise Exception("Django服务器启动超时，端口8001未响应")

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda msg=error_msg: self.on_server_error(msg))

    def on_server_started(self):
        """服务器启动成功"""
        self.status_var.set("系统已启动 - http://127.0.0.1:8001")
        self.system_status_label.config(text="系统状态：运行中", foreground="green")
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.open_btn.config(state="normal")

        # 自动打开浏览器
        webbrowser.open("http://127.0.0.1:8001")

    def on_server_error(self, error):
        """服务器启动失败"""
        self.status_var.set("启动失败")
        self.system_status_label.config(text="系统状态：启动失败", foreground="red")
        self.start_btn.config(state="normal")

        error_msg = f"""系统启动失败：

错误信息：{error}

可能的解决方案：
1. 检查端口8001是否被其他程序占用
2. 确保SchoolAcademicManager目录存在
3. 检查Python环境是否正常
4. 重新启动程序

如果问题持续存在，请联系技术支持。"""

        messagebox.showerror("启动失败", error_msg)

    def stop_system(self):
        """停止系统"""
        self.server_running = False

        if self.server_process:
            try:
                self.server_process.terminate()
                time.sleep(2)
                if self.server_process.poll() is None:
                    self.server_process.kill()
                self.server_process = None
            except:
                pass

        self.status_var.set("系统已停止")
        self.system_status_label.config(text="系统状态：已停止", foreground="gray")
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.open_btn.config(state="disabled")

    def open_system(self):
        """打开系统页面"""
        if self.check_port(8001):
            webbrowser.open("http://127.0.0.1:8001")
        else:
            messagebox.showwarning("系统未运行", "系统未运行，请先启动系统")

    def show_help(self):
        """显示帮助信息"""
        help_dialog = tk.Toplevel(self.root)
        help_dialog.title("帮助")
        help_dialog.geometry("600x500")
        help_dialog.resizable(False, False)
        help_dialog.transient(self.root)

        # 居中显示
        help_dialog.update_idletasks()
        x = (help_dialog.winfo_screenwidth() // 2) - (300)
        y = (help_dialog.winfo_screenheight() // 2) - (250)
        help_dialog.geometry(f"600x500+{x}+{y}")

        frame = ttk.Frame(help_dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="使用帮助",
                 font=("Microsoft YaHei", 14, "bold")).pack(pady=(0, 20))

        help_text = """中学教务管理系统使用指南

1. 软件激活
   • 首次使用有60天试用期
   • 试用期结束后需要购买许可证激活
   • 点击"激活软件"按钮输入许可证密钥
   • 可以使用"生成测试许可证"获取测试密钥

2. 系统启动
   • 确保软件已激活或在试用期内
   • 点击"启动系统"按钮启动Django服务器
   • 系统会自动在浏览器中打开
   • 默认访问地址：http://127.0.0.1:8001

3. 系统登录
   • 默认管理员账户：admin
   • 默认密码：admin123
   • 首次登录后请及时修改密码

4. 主要功能
   • 学生信息管理：录入、编辑、查询学生档案
   • 教师管理：教师信息、任课安排
   • 班级管理：班级设置、学生分配
   • 课程管理：课程设置、课表安排
   • 成绩管理：成绩录入、统计分析
   • 考勤管理：学生考勤、请假管理

5. 常见问题
   • 端口8001被占用：关闭占用端口的程序
   • 启动失败：检查SchoolAcademicManager目录是否存在
   • 试用期问题：基于硬件ID，重装系统不会重置
   • 许可证问题：联系技术支持获取帮助

6. 技术支持
   • 邮箱：<EMAIL>
   • 电话：400-123-4567
   • 在线QQ：123456789"""

        from tkinter import scrolledtext
        text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD,
                                               font=("Microsoft YaHei", 9))
        text_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

        ttk.Button(frame, text="关闭",
                  command=help_dialog.destroy).pack()

    def show_about(self):
        """显示关于对话框"""
        about_text = """中学教务管理系统 v1.0

功能特色：
• 学生信息管理
• 教师班级管理
• 课程安排管理
• 成绩考勤管理
• 数据统计分析
• 报表生成导出

技术特点：
• 基于Django框架开发
• SQLite数据库存储
• 响应式Web界面
• 支持多用户并发

试用期：60天
许可证：单机版/网络版可选

© 2024 学校管理系统开发团队
技术支持：<EMAIL>"""

        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """关闭程序"""
        if self.server_running:
            if messagebox.askquestion("确认退出", "系统正在运行，是否要停止系统并退出？") == 'yes':
                self.stop_system()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """运行启动器"""
        root = self.create_gui()
        root.mainloop()

def main():
    """主函数"""
    try:
        launcher = AcademicGUILauncher()
        launcher.run()
    except Exception as e:
        import traceback
        error_msg = f"程序启动失败：\n\n{str(e)}\n\n详细错误信息：\n{traceback.format_exc()}"

        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动失败", error_msg)
            root.destroy()
        except:
            # 如果GUI也无法显示，输出到控制台
            print(error_msg)

if __name__ == '__main__':
    main()
