# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['fixed_academic_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('academic_trial_manager.py', '.'),
        ('academic_license_validator.py', '.'),
        ('fixed_academic_launcher.py', '.'),
        ('start_django.bat', '.'),
    ],
    hiddenimports=[
        'academic_trial_manager',
        'academic_license_validator',
        'cryptography.fernet',
        'wmi',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'tkinter.scrolledtext',
        'django',
        'django.core.management',
        'django.core.management.commands',
        'django.core.management.commands.runserver',
        'django.core.wsgi',
        'django.conf',
        'django.apps',
        'django.contrib',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.db',
        'django.db.backends',
        'django.db.backends.sqlite3',
        'django.template',
        'django.urls',
        'django.utils',
        'django.views',
        'django.middleware',
        'django.http',
        'django.shortcuts',
        'django.forms',
        'django.contrib.auth.models',
        'django.contrib.auth.views',
        'django.contrib.auth.forms',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='中学教务管理系统',
    debug=False,  # 关闭调试模式
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 关闭控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='school_icon.ico' if os.path.exists('school_icon.ico') else None,
)
