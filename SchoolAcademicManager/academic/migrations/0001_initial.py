# Generated by Django 5.2.1 on 2025-06-25 01:34

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ClassType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='班级类型名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='类型代码')),
                ('type_category', models.CharField(choices=[('regular', '普通班'), ('science', '理科班'), ('liberal_arts', '文科班'), ('art', '艺术班'), ('sports', '体育班'), ('international', '国际班'), ('experimental', '实验班')], default='regular', max_length=20, verbose_name='类型分类')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '班级类型',
                'verbose_name_plural': '班级类型',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='教研组名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='教研组代码')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '教研组',
                'verbose_name_plural': '教研组',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='ExamType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='考试类型')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='类型代码')),
                ('weight', models.DecimalField(decimal_places=2, default=1.0, max_digits=3, verbose_name='权重')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '考试类型',
                'verbose_name_plural': '考试类型',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Grade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='年级名称')),
                ('grade_level', models.CharField(choices=[('grade7', '初一'), ('grade8', '初二'), ('grade9', '初三'), ('grade10', '高一'), ('grade11', '高二'), ('grade12', '高三')], max_length=10, verbose_name='年级层次')),
                ('year', models.IntegerField(verbose_name='入学年份')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '年级',
                'verbose_name_plural': '年级',
                'ordering': ['grade_level', '-year'],
            },
        ),
        migrations.CreateModel(
            name='Classroom',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='教室名称')),
                ('building', models.CharField(max_length=50, verbose_name='楼栋')),
                ('floor', models.IntegerField(verbose_name='楼层')),
                ('room_number', models.CharField(max_length=20, verbose_name='房间号')),
                ('room_type', models.CharField(choices=[('lecture', '普通教室'), ('lab', '实验室'), ('computer', '机房'), ('multimedia', '多媒体教室'), ('auditorium', '阶梯教室')], default='lecture', max_length=20, verbose_name='教室类型')),
                ('capacity', models.IntegerField(verbose_name='容量')),
                ('equipment', models.TextField(blank=True, verbose_name='设备')),
                ('is_available', models.BooleanField(default=True, verbose_name='是否可用')),
            ],
            options={
                'verbose_name': '教室',
                'verbose_name_plural': '教室',
                'ordering': ['building', 'floor', 'room_number'],
                'unique_together': {('building', 'room_number')},
            },
        ),
        migrations.CreateModel(
            name='Class',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='班级名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='班级代码')),
                ('max_students', models.IntegerField(default=50, verbose_name='最大学生数')),
                ('classroom_location', models.CharField(blank=True, max_length=50, verbose_name='教室位置')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('class_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.classtype', verbose_name='班级类型')),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.grade', verbose_name='年级')),
            ],
            options={
                'verbose_name': '班级',
                'verbose_name_plural': '班级',
                'ordering': ['grade', 'code'],
            },
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='课程名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='课程代码')),
                ('course_type', models.CharField(choices=[('main', '主科'), ('science', '理科'), ('liberal_arts', '文科'), ('art', '艺术'), ('pe', '体育'), ('tech', '技术'), ('elective', '选修')], default='main', max_length=20, verbose_name='课程类型')),
                ('grade_level', models.CharField(choices=[('junior', '初中'), ('senior', '高中'), ('both', '初高中')], default='both', max_length=10, verbose_name='适用年级')),
                ('weekly_hours', models.IntegerField(default=4, verbose_name='周课时')),
                ('description', models.TextField(blank=True, verbose_name='课程描述')),
                ('textbook', models.CharField(blank=True, max_length=200, verbose_name='使用教材')),
                ('is_exam_subject', models.BooleanField(default=True, verbose_name='是否考试科目')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.department', verbose_name='所属教研组')),
            ],
            options={
                'verbose_name': '课程',
                'verbose_name_plural': '课程',
                'ordering': ['course_type', 'code'],
            },
        ),
        migrations.CreateModel(
            name='Semester',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='学期名称')),
                ('year', models.IntegerField(verbose_name='学年')),
                ('semester_type', models.CharField(choices=[('spring', '春季学期'), ('summer', '夏季学期'), ('fall', '秋季学期'), ('winter', '冬季学期')], max_length=10, verbose_name='学期类型')),
                ('start_date', models.DateField(verbose_name='开始日期')),
                ('end_date', models.DateField(verbose_name='结束日期')),
                ('is_current', models.BooleanField(default=False, verbose_name='是否当前学期')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '学期',
                'verbose_name_plural': '学期',
                'ordering': ['-year', '-start_date'],
                'unique_together': {('year', 'semester_type')},
            },
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.CharField(max_length=20, unique=True, verbose_name='学号')),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('gender', models.CharField(choices=[('M', '男'), ('F', '女')], max_length=1, verbose_name='性别')),
                ('birth_date', models.DateField(verbose_name='出生日期')),
                ('id_card', models.CharField(max_length=18, unique=True, verbose_name='身份证号')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('address', models.TextField(blank=True, verbose_name='家庭住址')),
                ('enrollment_date', models.DateField(default=datetime.date.today, verbose_name='入学日期')),
                ('status', models.CharField(choices=[('enrolled', '在读'), ('suspended', '休学'), ('graduated', '毕业'), ('dropped', '退学'), ('transferred', '转学')], default='enrolled', max_length=20, verbose_name='学籍状态')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在校')),
                ('student_number', models.CharField(blank=True, max_length=30, verbose_name='学籍号')),
                ('father_name', models.CharField(blank=True, max_length=50, verbose_name='父亲姓名')),
                ('father_phone', models.CharField(blank=True, max_length=20, verbose_name='父亲电话')),
                ('father_work', models.CharField(blank=True, max_length=100, verbose_name='父亲工作')),
                ('mother_name', models.CharField(blank=True, max_length=50, verbose_name='母亲姓名')),
                ('mother_phone', models.CharField(blank=True, max_length=20, verbose_name='母亲电话')),
                ('mother_work', models.CharField(blank=True, max_length=100, verbose_name='母亲工作')),
                ('guardian_name', models.CharField(blank=True, max_length=50, verbose_name='监护人姓名')),
                ('guardian_phone', models.CharField(blank=True, max_length=20, verbose_name='监护人电话')),
                ('guardian_relation', models.CharField(blank=True, max_length=20, verbose_name='与学生关系')),
                ('home_address', models.TextField(blank=True, verbose_name='家庭详细地址')),
                ('is_boarding', models.BooleanField(default=False, verbose_name='是否住校')),
                ('health_condition', models.TextField(blank=True, verbose_name='健康状况')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('class_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='students', to='academic.class', verbose_name='班级')),
            ],
            options={
                'verbose_name': '学生',
                'verbose_name_plural': '学生',
                'ordering': ['student_id'],
            },
        ),
        migrations.CreateModel(
            name='Teacher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='工号')),
                ('title', models.CharField(choices=[('assistant', '助教'), ('lecturer', '讲师'), ('associate_prof', '副教授'), ('professor', '教授')], default='lecturer', max_length=20, verbose_name='职称')),
                ('employment_type', models.CharField(choices=[('full_time', '全职'), ('part_time', '兼职'), ('visiting', '客座')], default='full_time', max_length=20, verbose_name='聘用类型')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('office', models.CharField(blank=True, max_length=50, verbose_name='办公室')),
                ('hire_date', models.DateField(default=datetime.date.today, verbose_name='入职日期')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在职')),
                ('subjects', models.CharField(blank=True, help_text='如：语文、数学', max_length=200, verbose_name='任教科目')),
                ('is_head_teacher', models.BooleanField(default=False, verbose_name='是否班主任')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.department', verbose_name='所属教研组')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户账号')),
            ],
            options={
                'verbose_name': '教师',
                'verbose_name_plural': '教师',
                'ordering': ['employee_id'],
            },
        ),
        migrations.AddField(
            model_name='grade',
            name='grade_director',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='directed_grades', to='academic.teacher', verbose_name='年级主任'),
        ),
        migrations.AddField(
            model_name='department',
            name='head_teacher',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to='academic.teacher', verbose_name='教研组长'),
        ),
        migrations.CreateModel(
            name='CourseOffering',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('max_students', models.IntegerField(default=50, verbose_name='最大选课人数')),
                ('classroom', models.CharField(blank=True, max_length=50, verbose_name='教室')),
                ('schedule_info', models.TextField(help_text='如：周一1-2节，周三3-4节', verbose_name='上课时间')),
                ('assessment_method', models.TextField(blank=True, verbose_name='考核方式')),
                ('final_exam_weight', models.DecimalField(decimal_places=2, default=0.7, max_digits=3, verbose_name='期末考试权重')),
                ('midterm_weight', models.DecimalField(decimal_places=2, default=0.2, max_digits=3, verbose_name='期中考试权重')),
                ('homework_weight', models.DecimalField(decimal_places=2, default=0.1, max_digits=3, verbose_name='平时成绩权重')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('classes', models.ManyToManyField(to='academic.class', verbose_name='授课班级')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.course', verbose_name='课程')),
                ('semester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.semester', verbose_name='学期')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.teacher', verbose_name='任课教师')),
            ],
            options={
                'verbose_name': '开课信息',
                'verbose_name_plural': '开课信息',
                'ordering': ['semester', 'course'],
                'unique_together': {('course', 'semester', 'teacher')},
            },
        ),
        migrations.AddField(
            model_name='class',
            name='head_teacher',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='head_classes', to='academic.teacher', verbose_name='班主任'),
        ),
        migrations.CreateModel(
            name='Schedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weekday', models.IntegerField(choices=[(1, '周一'), (2, '周二'), (3, '周三'), (4, '周四'), (5, '周五'), (6, '周六'), (7, '周日')], verbose_name='星期')),
                ('time_slot', models.IntegerField(choices=[(1, '第1节'), (2, '第2节'), (3, '第3节'), (4, '第4节'), (5, '第5节'), (6, '第6节'), (7, '第7节'), (8, '第8节'), (9, '第9节'), (10, '第10节')], verbose_name='节次')),
                ('weeks', models.CharField(help_text='如：1-16周，1-8,10-16周', max_length=50, verbose_name='周次')),
                ('classroom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.classroom', verbose_name='教室')),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.courseoffering', verbose_name='开课信息')),
            ],
            options={
                'verbose_name': '课程表',
                'verbose_name_plural': '课程表',
                'ordering': ['weekday', 'time_slot'],
                'unique_together': {('classroom', 'weekday', 'time_slot')},
            },
        ),
        migrations.CreateModel(
            name='GradeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='分数')),
                ('letter_grade', models.CharField(blank=True, max_length=5, verbose_name='等级')),
                ('is_passed', models.BooleanField(default=False, verbose_name='是否通过')),
                ('exam_date', models.DateField(blank=True, null=True, verbose_name='考试日期')),
                ('recorded_at', models.DateTimeField(auto_now_add=True, verbose_name='录入时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.courseoffering', verbose_name='开课信息')),
                ('exam_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.examtype', verbose_name='考试类型')),
                ('recorded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='录入人')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.student', verbose_name='学生')),
            ],
            options={
                'verbose_name': '成绩记录',
                'verbose_name_plural': '成绩记录',
                'ordering': ['-exam_date', 'student'],
                'unique_together': {('student', 'course_offering', 'exam_type')},
            },
        ),
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enrollment_date', models.DateTimeField(auto_now_add=True, verbose_name='选课时间')),
                ('status', models.CharField(choices=[('enrolled', '已选课'), ('dropped', '已退课'), ('completed', '已完成')], default='enrolled', max_length=20, verbose_name='状态')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否有效')),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='academic.courseoffering', verbose_name='开课信息')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.student', verbose_name='学生')),
            ],
            options={
                'verbose_name': '选课记录',
                'verbose_name_plural': '选课记录',
                'ordering': ['-enrollment_date'],
                'unique_together': {('student', 'course_offering')},
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='日期')),
                ('status', models.CharField(choices=[('present', '出勤'), ('absent', '缺勤'), ('late', '迟到'), ('leave', '请假')], default='present', max_length=10, verbose_name='状态')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('recorded_at', models.DateTimeField(auto_now_add=True, verbose_name='记录时间')),
                ('recorded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='记录人')),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.courseoffering', verbose_name='课程')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academic.student', verbose_name='学生')),
            ],
            options={
                'verbose_name': '考勤记录',
                'verbose_name_plural': '考勤记录',
                'ordering': ['-date', 'student'],
                'unique_together': {('student', 'course_offering', 'date')},
            },
        ),
    ]
