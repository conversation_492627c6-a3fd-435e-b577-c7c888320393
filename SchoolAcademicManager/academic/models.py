from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from datetime import date, timedelta
from decimal import Decimal


class Department(models.Model):
    """教研组/学科组模型"""
    name = models.CharField(_("教研组名称"), max_length=100)
    code = models.CharField(_("教研组代码"), max_length=20, unique=True)
    description = models.TextField(_("描述"), blank=True)
    head_teacher = models.ForeignKey('Teacher', on_delete=models.SET_NULL, null=True, blank=True,
                                   verbose_name=_("教研组长"), related_name='headed_departments')
    is_active = models.BooleanField(_("是否启用"), default=True)
    created_at = models.DateTimeField(_("创建时间"), auto_now_add=True)

    class Meta:
        verbose_name = _("教研组")
        verbose_name_plural = _("教研组")
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Teacher(models.Model):
    """教师模型"""
    TITLE_CHOICES = [
        ('assistant', _('助教')),
        ('lecturer', _('讲师')),
        ('associate_prof', _('副教授')),
        ('professor', _('教授')),
    ]
    
    EMPLOYMENT_TYPE_CHOICES = [
        ('full_time', _('全职')),
        ('part_time', _('兼职')),
        ('visiting', _('客座')),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name=_("用户账号"))
    employee_id = models.CharField(_("工号"), max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name=_("所属教研组"))
    title = models.CharField(_("职称"), max_length=20, choices=TITLE_CHOICES, default='lecturer')
    employment_type = models.CharField(_("聘用类型"), max_length=20, choices=EMPLOYMENT_TYPE_CHOICES, default='full_time')
    phone = models.CharField(_("联系电话"), max_length=20, blank=True)
    email = models.EmailField(_("邮箱"), blank=True)
    office = models.CharField(_("办公室"), max_length=50, blank=True)
    hire_date = models.DateField(_("入职日期"), default=date.today)
    is_active = models.BooleanField(_("是否在职"), default=True)

    # 中学特有字段
    subjects = models.CharField(_("任教科目"), max_length=200, blank=True, help_text=_("如：语文、数学"))
    is_head_teacher = models.BooleanField(_("是否班主任"), default=False)
    
    class Meta:
        verbose_name = _("教师")
        verbose_name_plural = _("教师")
        ordering = ['employee_id']
    
    def __str__(self):
        return f"{self.employee_id} - {self.user.get_full_name() or self.user.username}"


class Grade(models.Model):
    """年级模型"""
    GRADE_LEVEL_CHOICES = [
        ('grade7', _('初一')),
        ('grade8', _('初二')),
        ('grade9', _('初三')),
        ('grade10', _('高一')),
        ('grade11', _('高二')),
        ('grade12', _('高三')),
    ]

    name = models.CharField(_("年级名称"), max_length=50)  # 如：2024级初一
    grade_level = models.CharField(_("年级层次"), max_length=10, choices=GRADE_LEVEL_CHOICES)
    year = models.IntegerField(_("入学年份"))
    grade_director = models.ForeignKey('Teacher', on_delete=models.SET_NULL, null=True, blank=True,
                                     verbose_name=_("年级主任"), related_name='directed_grades')
    is_active = models.BooleanField(_("是否启用"), default=True)

    class Meta:
        verbose_name = _("年级")
        verbose_name_plural = _("年级")
        ordering = ['grade_level', '-year']

    def __str__(self):
        return self.name


class ClassType(models.Model):
    """班级类型模型（文理科等）"""
    TYPE_CHOICES = [
        ('regular', _('普通班')),
        ('science', _('理科班')),
        ('liberal_arts', _('文科班')),
        ('art', _('艺术班')),
        ('sports', _('体育班')),
        ('international', _('国际班')),
        ('experimental', _('实验班')),
    ]

    name = models.CharField(_("班级类型名称"), max_length=50)
    code = models.CharField(_("类型代码"), max_length=20, unique=True)
    type_category = models.CharField(_("类型分类"), max_length=20, choices=TYPE_CHOICES, default='regular')
    description = models.TextField(_("描述"), blank=True)
    is_active = models.BooleanField(_("是否启用"), default=True)

    class Meta:
        verbose_name = _("班级类型")
        verbose_name_plural = _("班级类型")
        ordering = ['code']

    def __str__(self):
        return self.name


class Class(models.Model):
    """班级模型"""
    name = models.CharField(_("班级名称"), max_length=50)
    code = models.CharField(_("班级代码"), max_length=20, unique=True)
    grade = models.ForeignKey(Grade, on_delete=models.CASCADE, verbose_name=_("年级"))
    class_type = models.ForeignKey(ClassType, on_delete=models.CASCADE, verbose_name=_("班级类型"))
    head_teacher = models.ForeignKey(Teacher, on_delete=models.SET_NULL, null=True, blank=True,
                                   verbose_name=_("班主任"), related_name='head_classes')
    max_students = models.IntegerField(_("最大学生数"), default=50)
    classroom_location = models.CharField(_("教室位置"), max_length=50, blank=True)
    is_active = models.BooleanField(_("是否启用"), default=True)
    created_at = models.DateTimeField(_("创建时间"), auto_now_add=True)

    class Meta:
        verbose_name = _("班级")
        verbose_name_plural = _("班级")
        ordering = ['grade', 'code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def current_student_count(self):
        """当前学生数量"""
        return self.students.filter(is_active=True).count()


class Student(models.Model):
    """学生模型"""
    STATUS_CHOICES = [
        ('enrolled', _('在读')),
        ('suspended', _('休学')),
        ('graduated', _('毕业')),
        ('dropped', _('退学')),
        ('transferred', _('转学')),
    ]
    
    GENDER_CHOICES = [
        ('M', _('男')),
        ('F', _('女')),
    ]
    
    student_id = models.CharField(_("学号"), max_length=20, unique=True)
    name = models.CharField(_("姓名"), max_length=50)
    gender = models.CharField(_("性别"), max_length=1, choices=GENDER_CHOICES)
    birth_date = models.DateField(_("出生日期"))
    id_card = models.CharField(_("身份证号"), max_length=18, unique=True)
    phone = models.CharField(_("联系电话"), max_length=20, blank=True)
    email = models.EmailField(_("邮箱"), blank=True)
    address = models.TextField(_("家庭住址"), blank=True)
    
    # 学籍信息
    class_info = models.ForeignKey(Class, on_delete=models.CASCADE, verbose_name=_("班级"), related_name='students')
    enrollment_date = models.DateField(_("入学日期"), default=date.today)
    status = models.CharField(_("学籍状态"), max_length=20, choices=STATUS_CHOICES, default='enrolled')
    is_active = models.BooleanField(_("是否在校"), default=True)
    student_number = models.CharField(_("学籍号"), max_length=30, blank=True)

    # 家长信息（中学更重视家长联系）
    father_name = models.CharField(_("父亲姓名"), max_length=50, blank=True)
    father_phone = models.CharField(_("父亲电话"), max_length=20, blank=True)
    father_work = models.CharField(_("父亲工作"), max_length=100, blank=True)

    mother_name = models.CharField(_("母亲姓名"), max_length=50, blank=True)
    mother_phone = models.CharField(_("母亲电话"), max_length=20, blank=True)
    mother_work = models.CharField(_("母亲工作"), max_length=100, blank=True)

    guardian_name = models.CharField(_("监护人姓名"), max_length=50, blank=True)
    guardian_phone = models.CharField(_("监护人电话"), max_length=20, blank=True)
    guardian_relation = models.CharField(_("与学生关系"), max_length=20, blank=True)

    # 中学特有信息
    home_address = models.TextField(_("家庭详细地址"), blank=True)
    is_boarding = models.BooleanField(_("是否住校"), default=False)
    health_condition = models.TextField(_("健康状况"), blank=True)
    
    created_at = models.DateTimeField(_("创建时间"), auto_now_add=True)
    updated_at = models.DateTimeField(_("更新时间"), auto_now=True)
    
    class Meta:
        verbose_name = _("学生")
        verbose_name_plural = _("学生")
        ordering = ['student_id']
    
    def __str__(self):
        return f"{self.student_id} - {self.name}"


class Course(models.Model):
    """课程模型"""
    COURSE_TYPE_CHOICES = [
        ('main', _('主科')),  # 语数英
        ('science', _('理科')),  # 物理化学生物
        ('liberal_arts', _('文科')),  # 政史地
        ('art', _('艺术')),  # 音乐美术
        ('pe', _('体育')),
        ('tech', _('技术')),  # 信息技术、通用技术
        ('elective', _('选修')),
    ]

    GRADE_LEVEL_CHOICES = [
        ('junior', _('初中')),
        ('senior', _('高中')),
        ('both', _('初高中')),
    ]

    name = models.CharField(_("课程名称"), max_length=100)
    code = models.CharField(_("课程代码"), max_length=20, unique=True)
    course_type = models.CharField(_("课程类型"), max_length=20, choices=COURSE_TYPE_CHOICES, default='main')
    grade_level = models.CharField(_("适用年级"), max_length=10, choices=GRADE_LEVEL_CHOICES, default='both')
    weekly_hours = models.IntegerField(_("周课时"), default=4)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name=_("所属教研组"))
    description = models.TextField(_("课程描述"), blank=True)
    textbook = models.CharField(_("使用教材"), max_length=200, blank=True)
    is_exam_subject = models.BooleanField(_("是否考试科目"), default=True)
    is_active = models.BooleanField(_("是否启用"), default=True)

    class Meta:
        verbose_name = _("课程")
        verbose_name_plural = _("课程")
        ordering = ['course_type', 'code']

    def __str__(self):
        return f"{self.name}"


class Semester(models.Model):
    """学期模型"""
    SEMESTER_TYPE_CHOICES = [
        ('spring', _('春季学期')),
        ('summer', _('夏季学期')),
        ('fall', _('秋季学期')),
        ('winter', _('冬季学期')),
    ]

    name = models.CharField(_("学期名称"), max_length=50)  # 如：2024-2025学年春季学期
    year = models.IntegerField(_("学年"))  # 如：2024
    semester_type = models.CharField(_("学期类型"), max_length=10, choices=SEMESTER_TYPE_CHOICES)
    start_date = models.DateField(_("开始日期"))
    end_date = models.DateField(_("结束日期"))
    is_current = models.BooleanField(_("是否当前学期"), default=False)
    is_active = models.BooleanField(_("是否启用"), default=True)

    class Meta:
        verbose_name = _("学期")
        verbose_name_plural = _("学期")
        ordering = ['-year', '-start_date']
        unique_together = ['year', 'semester_type']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # 确保只有一个当前学期
            Semester.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class CourseOffering(models.Model):
    """开课信息模型"""
    course = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name=_("课程"))
    semester = models.ForeignKey(Semester, on_delete=models.CASCADE, verbose_name=_("学期"))
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, verbose_name=_("任课教师"))
    classes = models.ManyToManyField(Class, verbose_name=_("授课班级"))

    # 课程安排
    max_students = models.IntegerField(_("最大选课人数"), default=50)
    classroom = models.CharField(_("教室"), max_length=50, blank=True)
    schedule_info = models.TextField(_("上课时间"), help_text=_("如：周一1-2节，周三3-4节"))

    # 考核方式
    assessment_method = models.TextField(_("考核方式"), blank=True)
    final_exam_weight = models.DecimalField(_("期末考试权重"), max_digits=3, decimal_places=2, default=0.70)
    midterm_weight = models.DecimalField(_("期中考试权重"), max_digits=3, decimal_places=2, default=0.20)
    homework_weight = models.DecimalField(_("平时成绩权重"), max_digits=3, decimal_places=2, default=0.10)

    is_active = models.BooleanField(_("是否启用"), default=True)
    created_at = models.DateTimeField(_("创建时间"), auto_now_add=True)

    class Meta:
        verbose_name = _("开课信息")
        verbose_name_plural = _("开课信息")
        ordering = ['semester', 'course']
        unique_together = ['course', 'semester', 'teacher']

    def __str__(self):
        return f"{self.semester} - {self.course} - {self.teacher}"

    @property
    def enrolled_count(self):
        """已选课人数"""
        return self.enrollments.filter(is_active=True).count()


class Enrollment(models.Model):
    """选课记录模型"""
    STATUS_CHOICES = [
        ('enrolled', _('已选课')),
        ('dropped', _('已退课')),
        ('completed', _('已完成')),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, verbose_name=_("学生"))
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE,
                                      verbose_name=_("开课信息"), related_name='enrollments')
    enrollment_date = models.DateTimeField(_("选课时间"), auto_now_add=True)
    status = models.CharField(_("状态"), max_length=20, choices=STATUS_CHOICES, default='enrolled')
    is_active = models.BooleanField(_("是否有效"), default=True)

    class Meta:
        verbose_name = _("选课记录")
        verbose_name_plural = _("选课记录")
        ordering = ['-enrollment_date']
        unique_together = ['student', 'course_offering']

    def __str__(self):
        return f"{self.student} - {self.course_offering.course}"


class ExamType(models.Model):
    """考试类型模型"""
    name = models.CharField(_("考试类型"), max_length=50)
    code = models.CharField(_("类型代码"), max_length=20, unique=True)
    weight = models.DecimalField(_("权重"), max_digits=3, decimal_places=2, default=1.0)
    description = models.TextField(_("描述"), blank=True)
    is_active = models.BooleanField(_("是否启用"), default=True)

    class Meta:
        verbose_name = _("考试类型")
        verbose_name_plural = _("考试类型")
        ordering = ['code']

    def __str__(self):
        return self.name


class GradeRecord(models.Model):
    """成绩记录模型"""
    student = models.ForeignKey(Student, on_delete=models.CASCADE, verbose_name=_("学生"))
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, verbose_name=_("开课信息"))
    exam_type = models.ForeignKey(ExamType, on_delete=models.CASCADE, verbose_name=_("考试类型"))
    score = models.DecimalField(_("分数"), max_digits=5, decimal_places=2, null=True, blank=True)
    letter_grade = models.CharField(_("等级"), max_length=5, blank=True)  # A, B, C, D, F
    is_passed = models.BooleanField(_("是否通过"), default=False)
    exam_date = models.DateField(_("考试日期"), null=True, blank=True)
    recorded_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("录入人"))
    recorded_at = models.DateTimeField(_("录入时间"), auto_now_add=True)
    updated_at = models.DateTimeField(_("更新时间"), auto_now=True)

    class Meta:
        verbose_name = _("成绩记录")
        verbose_name_plural = _("成绩记录")
        ordering = ['-exam_date', 'student']
        unique_together = ['student', 'course_offering', 'exam_type']

    def __str__(self):
        return f"{self.student} - {self.course_offering.course} - {self.exam_type}: {self.score}"

    def save(self, *args, **kwargs):
        # 自动计算等级和是否通过
        if self.score is not None:
            if self.score >= 90:
                self.letter_grade = 'A'
                self.is_passed = True
            elif self.score >= 80:
                self.letter_grade = 'B'
                self.is_passed = True
            elif self.score >= 70:
                self.letter_grade = 'C'
                self.is_passed = True
            elif self.score >= 60:
                self.letter_grade = 'D'
                self.is_passed = True
            else:
                self.letter_grade = 'F'
                self.is_passed = False
        super().save(*args, **kwargs)


class Attendance(models.Model):
    """考勤记录模型"""
    STATUS_CHOICES = [
        ('present', _('出勤')),
        ('absent', _('缺勤')),
        ('late', _('迟到')),
        ('leave', _('请假')),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, verbose_name=_("学生"))
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, verbose_name=_("课程"))
    date = models.DateField(_("日期"))
    status = models.CharField(_("状态"), max_length=10, choices=STATUS_CHOICES, default='present')
    notes = models.TextField(_("备注"), blank=True)
    recorded_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("记录人"))
    recorded_at = models.DateTimeField(_("记录时间"), auto_now_add=True)

    class Meta:
        verbose_name = _("考勤记录")
        verbose_name_plural = _("考勤记录")
        ordering = ['-date', 'student']
        unique_together = ['student', 'course_offering', 'date']

    def __str__(self):
        return f"{self.student} - {self.course_offering.course} - {self.date}: {self.get_status_display()}"


class Classroom(models.Model):
    """教室模型"""
    ROOM_TYPE_CHOICES = [
        ('lecture', _('普通教室')),
        ('lab', _('实验室')),
        ('computer', _('机房')),
        ('multimedia', _('多媒体教室')),
        ('auditorium', _('阶梯教室')),
    ]

    name = models.CharField(_("教室名称"), max_length=50)
    building = models.CharField(_("楼栋"), max_length=50)
    floor = models.IntegerField(_("楼层"))
    room_number = models.CharField(_("房间号"), max_length=20)
    room_type = models.CharField(_("教室类型"), max_length=20, choices=ROOM_TYPE_CHOICES, default='lecture')
    capacity = models.IntegerField(_("容量"))
    equipment = models.TextField(_("设备"), blank=True)
    is_available = models.BooleanField(_("是否可用"), default=True)

    class Meta:
        verbose_name = _("教室")
        verbose_name_plural = _("教室")
        ordering = ['building', 'floor', 'room_number']
        unique_together = ['building', 'room_number']

    def __str__(self):
        return f"{self.building}-{self.room_number}"


class Schedule(models.Model):
    """课程表模型"""
    WEEKDAY_CHOICES = [
        (1, _('周一')),
        (2, _('周二')),
        (3, _('周三')),
        (4, _('周四')),
        (5, _('周五')),
        (6, _('周六')),
        (7, _('周日')),
    ]

    TIME_SLOT_CHOICES = [
        (1, _('第1节')),
        (2, _('第2节')),
        (3, _('第3节')),
        (4, _('第4节')),
        (5, _('第5节')),
        (6, _('第6节')),
        (7, _('第7节')),
        (8, _('第8节')),
        (9, _('第9节')),
        (10, _('第10节')),
    ]

    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, verbose_name=_("开课信息"))
    classroom = models.ForeignKey(Classroom, on_delete=models.CASCADE, verbose_name=_("教室"))
    weekday = models.IntegerField(_("星期"), choices=WEEKDAY_CHOICES)
    time_slot = models.IntegerField(_("节次"), choices=TIME_SLOT_CHOICES)
    weeks = models.CharField(_("周次"), max_length=50, help_text=_("如：1-16周，1-8,10-16周"))

    class Meta:
        verbose_name = _("课程表")
        verbose_name_plural = _("课程表")
        ordering = ['weekday', 'time_slot']
        unique_together = ['classroom', 'weekday', 'time_slot']

    def __str__(self):
        return f"{self.course_offering} - {self.get_weekday_display()}{self.get_time_slot_display()} - {self.classroom}"
