from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from import_export.admin import ImportExportModelAdmin
from .models import (
    Department, Teacher, Grade, ClassType, Class, Student, Course,
    Semester, CourseOffering, Enrollment, ExamType, GradeRecord,
    Attendance, Classroom, Schedule
)


@admin.register(Department)
class DepartmentAdmin(ImportExportModelAdmin):
    list_display = ['code', 'name', 'head_teacher', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code']
    ordering = ['code']


@admin.register(Teacher)
class TeacherAdmin(ImportExportModelAdmin):
    list_display = ['employee_id', 'get_full_name', 'department', 'subjects', 'title', 'is_head_teacher', 'is_active']
    list_filter = ['department', 'title', 'employment_type', 'is_head_teacher', 'is_active', 'hire_date']
    search_fields = ['employee_id', 'user__first_name', 'user__last_name', 'user__username', 'subjects']
    ordering = ['employee_id']

    def get_full_name(self, obj):
        return obj.user.get_full_name() or obj.user.username
    get_full_name.short_description = _('姓名')


@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    list_display = ['name', 'grade_level', 'year', 'grade_director', 'is_active']
    list_filter = ['grade_level', 'year', 'is_active']
    ordering = ['grade_level', '-year']


@admin.register(ClassType)
class ClassTypeAdmin(ImportExportModelAdmin):
    list_display = ['code', 'name', 'type_category', 'is_active']
    list_filter = ['type_category', 'is_active']
    search_fields = ['name', 'code']
    ordering = ['code']


@admin.register(Class)
class ClassAdmin(ImportExportModelAdmin):
    list_display = ['code', 'name', 'grade', 'class_type', 'head_teacher', 'current_student_count', 'max_students', 'classroom_location', 'is_active']
    list_filter = ['grade', 'class_type', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'classroom_location']
    ordering = ['grade', 'code']


@admin.register(Student)
class StudentAdmin(ImportExportModelAdmin):
    list_display = ['student_id', 'name', 'gender', 'class_info', 'status', 'is_boarding', 'enrollment_date', 'is_active']
    list_filter = ['gender', 'class_info__grade', 'class_info__class_type', 'status', 'is_boarding', 'is_active', 'enrollment_date']
    search_fields = ['student_id', 'name', 'id_card', 'phone', 'student_number']
    ordering = ['student_id']

    fieldsets = (
        (_('基本信息'), {
            'fields': ('student_id', 'name', 'gender', 'birth_date', 'id_card', 'phone', 'email', 'address')
        }),
        (_('学籍信息'), {
            'fields': ('class_info', 'enrollment_date', 'status', 'student_number', 'is_active', 'is_boarding')
        }),
        (_('家长信息'), {
            'fields': ('father_name', 'father_phone', 'father_work', 'mother_name', 'mother_phone', 'mother_work')
        }),
        (_('监护人信息'), {
            'fields': ('guardian_name', 'guardian_phone', 'guardian_relation')
        }),
        (_('其他信息'), {
            'fields': ('home_address', 'health_condition')
        }),
    )


@admin.register(Course)
class CourseAdmin(ImportExportModelAdmin):
    list_display = ['name', 'code', 'course_type', 'grade_level', 'weekly_hours', 'department', 'is_exam_subject', 'is_active']
    list_filter = ['course_type', 'grade_level', 'department', 'is_exam_subject', 'is_active']
    search_fields = ['name', 'code', 'textbook']
    ordering = ['course_type', 'code']


@admin.register(Semester)
class SemesterAdmin(admin.ModelAdmin):
    list_display = ['name', 'year', 'semester_type', 'start_date', 'end_date', 'is_current', 'is_active']
    list_filter = ['year', 'semester_type', 'is_current', 'is_active']
    ordering = ['-year', '-start_date']


@admin.register(CourseOffering)
class CourseOfferingAdmin(ImportExportModelAdmin):
    list_display = ['course', 'semester', 'teacher', 'classroom', 'enrolled_count', 'max_students', 'is_active']
    list_filter = ['semester', 'course__department', 'is_active', 'created_at']
    search_fields = ['course__name', 'course__code', 'teacher__user__first_name', 'teacher__user__last_name']
    ordering = ['semester', 'course']
    filter_horizontal = ['classes']


@admin.register(Enrollment)
class EnrollmentAdmin(ImportExportModelAdmin):
    list_display = ['student', 'get_course_name', 'get_semester', 'enrollment_date', 'status', 'is_active']
    list_filter = ['course_offering__semester', 'status', 'is_active', 'enrollment_date']
    search_fields = ['student__student_id', 'student__name', 'course_offering__course__name']
    ordering = ['-enrollment_date']
    
    def get_course_name(self, obj):
        return obj.course_offering.course.name
    get_course_name.short_description = _('课程')
    
    def get_semester(self, obj):
        return obj.course_offering.semester.name
    get_semester.short_description = _('学期')


@admin.register(ExamType)
class ExamTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'weight', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'code']
    ordering = ['code']


@admin.register(GradeRecord)
class GradeRecordAdmin(ImportExportModelAdmin):
    list_display = ['student', 'get_course_name', 'exam_type', 'score', 'letter_grade', 'is_passed', 'exam_date']
    list_filter = ['exam_type', 'is_passed', 'letter_grade', 'exam_date', 'course_offering__semester']
    search_fields = ['student__student_id', 'student__name', 'course_offering__course__name']
    ordering = ['-exam_date', 'student']
    
    def get_course_name(self, obj):
        return obj.course_offering.course.name
    get_course_name.short_description = _('课程')


@admin.register(Attendance)
class AttendanceAdmin(ImportExportModelAdmin):
    list_display = ['student', 'get_course_name', 'date', 'status', 'recorded_by']
    list_filter = ['status', 'date', 'course_offering__semester']
    search_fields = ['student__student_id', 'student__name', 'course_offering__course__name']
    ordering = ['-date', 'student']
    
    def get_course_name(self, obj):
        return obj.course_offering.course.name
    get_course_name.short_description = _('课程')


@admin.register(Classroom)
class ClassroomAdmin(ImportExportModelAdmin):
    list_display = ['name', 'building', 'floor', 'room_number', 'room_type', 'capacity', 'is_available']
    list_filter = ['building', 'floor', 'room_type', 'is_available']
    search_fields = ['name', 'building', 'room_number']
    ordering = ['building', 'floor', 'room_number']


@admin.register(Schedule)
class ScheduleAdmin(ImportExportModelAdmin):
    list_display = ['course_offering', 'classroom', 'weekday', 'time_slot', 'weeks']
    list_filter = ['weekday', 'time_slot', 'course_offering__semester']
    search_fields = ['course_offering__course__name', 'classroom__name']
    ordering = ['weekday', 'time_slot']
