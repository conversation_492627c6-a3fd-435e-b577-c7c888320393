import os
import sys

# 修复 stdout 问题
if not sys.stdout:
    import io
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()

# 试用期检查
import trial_checker
trial_checker.check_trial_period(60)

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "SchoolAcademicManager.settings")

from django.core.management import execute_from_command_line

# 禁用热重载
execute_from_command_line(["manage.py", "runserver", "127.0.0.1:8000", "--noreload"])
