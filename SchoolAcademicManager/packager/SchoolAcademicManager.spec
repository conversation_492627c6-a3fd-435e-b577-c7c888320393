# -*- mode: python ; coding: utf-8 -*-
import sys
import os

block_cipher = None

a = Analysis([
    'launcher.py',
],
    pathex=[os.path.abspath(os.path.dirname(__file__))],
    binaries=[
        ('C:/Users/<USER>/AppData/Local/Programs/Python/Python312/DLLs/tcl86t.dll', '.'),
        ('C:/Users/<USER>/AppData/Local/Programs/Python/Python312/DLLs/tk86t.dll', '.'),
    ],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Lib/site-packages/import_export/templates', 'import_export/templates'),
        ('run_server.py', '.'),
        ('manage.py', '.'),
        ('SchoolAcademicManager', 'SchoolAcademicManager'),
        ('trial_checker.py', '.'),
        ('C:/Users/<USER>/AppData/Local/Programs/Python/Python312/tcl/tcl8.6', 'tcl/tcl8.6'),
        ('C:/Users/<USER>/AppData/Local/Programs/Python/Python312/tcl/tk8.6', 'tcl/tk8.6'),
    ],
    hiddenimports=['django'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SchoolAcademicManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
)
