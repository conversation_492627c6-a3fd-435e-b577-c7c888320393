#!/usr/bin/env python
"""
初始化教务管理系统数据
"""
import os
import sys
import django
from datetime import date, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')
django.setup()

from django.contrib.auth.models import User
from academic.models import (
    Department, Teacher, Grade, ClassType, Class, Student, Course,
    Semester, CourseOffering, ExamType, Classroom
)


def create_initial_data():
    """创建初始数据"""
    print("开始创建初始数据...")
    
    # 创建管理员用户
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='系统',
            last_name='管理员'
        )
        print("✓ 创建管理员用户: admin/admin123")
    else:
        admin_user = User.objects.get(username='admin')
        print("✓ 管理员用户已存在")
    
    # 创建教研组
    departments_data = [
        {'name': '语文教研组', 'code': 'CHINESE'},
        {'name': '数学教研组', 'code': 'MATH'},
        {'name': '英语教研组', 'code': 'ENGLISH'},
        {'name': '物理教研组', 'code': 'PHYSICS'},
        {'name': '化学教研组', 'code': 'CHEMISTRY'},
        {'name': '生物教研组', 'code': 'BIOLOGY'},
        {'name': '政治教研组', 'code': 'POLITICS'},
        {'name': '历史教研组', 'code': 'HISTORY'},
        {'name': '地理教研组', 'code': 'GEOGRAPHY'},
        {'name': '体育教研组', 'code': 'PE'},
        {'name': '艺术教研组', 'code': 'ART'},
        {'name': '技术教研组', 'code': 'TECH'},
    ]

    for dept_data in departments_data:
        dept, created = Department.objects.get_or_create(
            code=dept_data['code'],
            defaults={'name': dept_data['name']}
        )
        if created:
            print(f"✓ 创建教研组: {dept.name}")
    
    # 创建年级
    current_year = date.today().year
    grades_data = [
        {'name': f'{current_year-2}级初一', 'grade_level': 'grade7', 'year': current_year-2},
        {'name': f'{current_year-3}级初二', 'grade_level': 'grade8', 'year': current_year-3},
        {'name': f'{current_year-4}级初三', 'grade_level': 'grade9', 'year': current_year-4},
        {'name': f'{current_year-2}级高一', 'grade_level': 'grade10', 'year': current_year-2},
        {'name': f'{current_year-3}级高二', 'grade_level': 'grade11', 'year': current_year-3},
        {'name': f'{current_year-4}级高三', 'grade_level': 'grade12', 'year': current_year-4},
    ]

    for grade_data in grades_data:
        grade, created = Grade.objects.get_or_create(
            grade_level=grade_data['grade_level'],
            year=grade_data['year'],
            defaults={'name': grade_data['name']}
        )
        if created:
            print(f"✓ 创建年级: {grade.name}")
    
    # 创建班级类型
    class_types_data = [
        {'name': '普通班', 'code': 'REGULAR', 'type_category': 'regular'},
        {'name': '理科班', 'code': 'SCIENCE', 'type_category': 'science'},
        {'name': '文科班', 'code': 'LIBERAL', 'type_category': 'liberal_arts'},
        {'name': '实验班', 'code': 'EXPERIMENTAL', 'type_category': 'experimental'},
        {'name': '艺术班', 'code': 'ART', 'type_category': 'art'},
        {'name': '体育班', 'code': 'SPORTS', 'type_category': 'sports'},
    ]

    for class_type_data in class_types_data:
        class_type, created = ClassType.objects.get_or_create(
            code=class_type_data['code'],
            defaults={
                'name': class_type_data['name'],
                'type_category': class_type_data['type_category']
            }
        )
        if created:
            print(f"✓ 创建班级类型: {class_type.name}")
    
    # 创建教师用户和教师信息
    chinese_dept = Department.objects.get(code='CHINESE')
    math_dept = Department.objects.get(code='MATH')
    english_dept = Department.objects.get(code='ENGLISH')

    teachers_data = [
        {'username': 'teacher1', 'first_name': '张', 'last_name': '老师', 'employee_id': 'T001', 'title': 'lecturer', 'department': chinese_dept, 'subjects': '语文'},
        {'username': 'teacher2', 'first_name': '李', 'last_name': '老师', 'employee_id': 'T002', 'title': 'lecturer', 'department': math_dept, 'subjects': '数学'},
        {'username': 'teacher3', 'first_name': '王', 'last_name': '老师', 'employee_id': 'T003', 'title': 'lecturer', 'department': english_dept, 'subjects': '英语'},
        {'username': 'teacher4', 'first_name': '刘', 'last_name': '老师', 'employee_id': 'T004', 'title': 'lecturer', 'department': chinese_dept, 'subjects': '语文', 'is_head_teacher': True},
        {'username': 'teacher5', 'first_name': '陈', 'last_name': '老师', 'employee_id': 'T005', 'title': 'lecturer', 'department': math_dept, 'subjects': '数学', 'is_head_teacher': True},
    ]

    for teacher_data in teachers_data:
        user, created = User.objects.get_or_create(
            username=teacher_data['username'],
            defaults={
                'first_name': teacher_data['first_name'],
                'last_name': teacher_data['last_name'],
                'email': f"{teacher_data['username']}@school.edu",
                'password': 'pbkdf2_sha256$600000$dummy$dummy'  # 默认密码: teacher123
            }
        )
        if created:
            user.set_password('teacher123')
            user.save()

        teacher, created = Teacher.objects.get_or_create(
            employee_id=teacher_data['employee_id'],
            defaults={
                'user': user,
                'department': teacher_data['department'],
                'title': teacher_data['title'],
                'subjects': teacher_data['subjects'],
                'is_head_teacher': teacher_data.get('is_head_teacher', False)
            }
        )
        if created:
            print(f"✓ 创建教师: {teacher.user.get_full_name()} ({teacher.employee_id}) - {teacher.subjects}")
    
    # 创建班级
    regular_type = ClassType.objects.get(code='REGULAR')
    science_type = ClassType.objects.get(code='SCIENCE')
    liberal_type = ClassType.objects.get(code='LIBERAL')

    grade_7 = Grade.objects.get(grade_level='grade7')
    grade_10 = Grade.objects.get(grade_level='grade10')
    grade_11 = Grade.objects.get(grade_level='grade11')

    head_teacher1 = Teacher.objects.get(employee_id='T004')
    head_teacher2 = Teacher.objects.get(employee_id='T005')

    classes_data = [
        {'name': '初一(1)班', 'code': 'G7-1', 'grade': grade_7, 'class_type': regular_type, 'head_teacher': head_teacher1, 'classroom_location': '教学楼A101'},
        {'name': '初一(2)班', 'code': 'G7-2', 'grade': grade_7, 'class_type': regular_type, 'head_teacher': head_teacher2, 'classroom_location': '教学楼A102'},
        {'name': '高一理科班', 'code': 'G10-S1', 'grade': grade_10, 'class_type': science_type, 'classroom_location': '教学楼B201'},
        {'name': '高二文科班', 'code': 'G11-L1', 'grade': grade_11, 'class_type': liberal_type, 'classroom_location': '教学楼B301'},
    ]

    for class_data in classes_data:
        class_obj, created = Class.objects.get_or_create(
            code=class_data['code'],
            defaults={
                'name': class_data['name'],
                'grade': class_data['grade'],
                'class_type': class_data['class_type'],
                'head_teacher': class_data.get('head_teacher'),
                'classroom_location': class_data['classroom_location']
            }
        )
        if created:
            print(f"✓ 创建班级: {class_obj.name}")
    
    # 创建课程
    physics_dept = Department.objects.get(code='PHYSICS')
    chemistry_dept = Department.objects.get(code='CHEMISTRY')

    courses_data = [
        # 主科
        {'name': '语文', 'code': 'CHINESE', 'course_type': 'main', 'grade_level': 'both', 'weekly_hours': 5, 'department': chinese_dept, 'textbook': '人教版语文'},
        {'name': '数学', 'code': 'MATH', 'course_type': 'main', 'grade_level': 'both', 'weekly_hours': 5, 'department': math_dept, 'textbook': '人教版数学'},
        {'name': '英语', 'code': 'ENGLISH', 'course_type': 'main', 'grade_level': 'both', 'weekly_hours': 4, 'department': english_dept, 'textbook': '人教版英语'},

        # 理科
        {'name': '物理', 'code': 'PHYSICS', 'course_type': 'science', 'grade_level': 'both', 'weekly_hours': 3, 'department': physics_dept, 'textbook': '人教版物理'},
        {'name': '化学', 'code': 'CHEMISTRY', 'course_type': 'science', 'grade_level': 'both', 'weekly_hours': 3, 'department': chemistry_dept, 'textbook': '人教版化学'},

        # 文科
        {'name': '历史', 'code': 'HISTORY', 'course_type': 'liberal_arts', 'grade_level': 'both', 'weekly_hours': 2, 'department': Department.objects.get(code='HISTORY'), 'textbook': '人教版历史'},
        {'name': '地理', 'code': 'GEOGRAPHY', 'course_type': 'liberal_arts', 'grade_level': 'both', 'weekly_hours': 2, 'department': Department.objects.get(code='GEOGRAPHY'), 'textbook': '人教版地理'},

        # 其他
        {'name': '体育', 'code': 'PE', 'course_type': 'pe', 'grade_level': 'both', 'weekly_hours': 2, 'department': Department.objects.get(code='PE'), 'is_exam_subject': False},
        {'name': '音乐', 'code': 'MUSIC', 'course_type': 'art', 'grade_level': 'both', 'weekly_hours': 1, 'department': Department.objects.get(code='ART'), 'is_exam_subject': False},
    ]

    for course_data in courses_data:
        course, created = Course.objects.get_or_create(
            code=course_data['code'],
            defaults={
                'name': course_data['name'],
                'course_type': course_data['course_type'],
                'grade_level': course_data['grade_level'],
                'weekly_hours': course_data['weekly_hours'],
                'department': course_data['department'],
                'textbook': course_data.get('textbook', ''),
                'is_exam_subject': course_data.get('is_exam_subject', True)
            }
        )
        if created:
            print(f"✓ 创建课程: {course.name}")
    
    # 创建学期
    semester, created = Semester.objects.get_or_create(
        year=current_year,
        semester_type='fall',
        defaults={
            'name': f'{current_year}-{current_year+1}学年秋季学期',
            'start_date': date(current_year, 9, 1),
            'end_date': date(current_year+1, 1, 15),
            'is_current': True
        }
    )
    if created:
        print(f"✓ 创建学期: {semester.name}")
    
    # 创建考试类型
    exam_types_data = [
        {'name': '平时成绩', 'code': 'REGULAR', 'weight': 0.3},
        {'name': '期中考试', 'code': 'MIDTERM', 'weight': 0.3},
        {'name': '期末考试', 'code': 'FINAL', 'weight': 0.4},
    ]
    
    for exam_data in exam_types_data:
        exam_type, created = ExamType.objects.get_or_create(
            code=exam_data['code'],
            defaults={
                'name': exam_data['name'],
                'weight': exam_data['weight']
            }
        )
        if created:
            print(f"✓ 创建考试类型: {exam_type.name}")
    
    # 创建教室
    classrooms_data = [
        {'name': '教学楼A101', 'building': '教学楼A', 'floor': 1, 'room_number': '101', 'capacity': 50},
        {'name': '教学楼A102', 'building': '教学楼A', 'floor': 1, 'room_number': '102', 'capacity': 50},
        {'name': '教学楼A201', 'building': '教学楼A', 'floor': 2, 'room_number': '201', 'capacity': 60},
        {'name': '实验楼B301', 'building': '实验楼B', 'floor': 3, 'room_number': '301', 'capacity': 30, 'room_type': 'lab'},
    ]
    
    for classroom_data in classrooms_data:
        classroom, created = Classroom.objects.get_or_create(
            building=classroom_data['building'],
            room_number=classroom_data['room_number'],
            defaults={
                'name': classroom_data['name'],
                'floor': classroom_data['floor'],
                'capacity': classroom_data['capacity'],
                'room_type': classroom_data.get('room_type', 'lecture')
            }
        )
        if created:
            print(f"✓ 创建教室: {classroom.name}")
    
    print("\n初始数据创建完成！")
    print("\n系统信息:")
    print("- 管理后台: http://127.0.0.1:8001/admin")
    print("- 管理员账户: admin/admin123")
    print("- 教师账户: teacher1/teacher123, teacher2/teacher123, teacher3/teacher123")
    print("\n请运行 start_server.bat 启动系统")


if __name__ == '__main__':
    create_initial_data()
