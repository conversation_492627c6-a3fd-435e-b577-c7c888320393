@echo off
chcp 65001
echo 启动学校教务管理系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 安装依赖
echo 正在安装依赖包...
pip install -r requirements.txt

REM 数据库迁移
echo 正在进行数据库迁移...
python manage.py makemigrations
python manage.py migrate

REM 创建超级用户（如果不存在）
echo 正在检查管理员账户...
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('已创建管理员账户: admin/admin123')
else:
    print('管理员账户已存在')
"

REM 启动服务器
echo.
echo 正在启动教务管理系统服务器...
echo 服务器地址: http://127.0.0.1:8001
echo 管理后台: http://127.0.0.1:8001/admin
echo 默认管理员账户: admin/admin123
echo.
echo 按 Ctrl+C 停止服务器
echo.

python manage.py runserver 127.0.0.1:8001
