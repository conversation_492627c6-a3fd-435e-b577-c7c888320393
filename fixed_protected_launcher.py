#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复版代码保护启动器
解决UI显示问题和Django启动问题
"""

import subprocess
import sys
import os
import threading
import time
import webbrowser
import socket
import hashlib
import json
import datetime
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog

# 内置试用期管理类
class TrialManager:
    def __init__(self, trial_days=60):
        self.trial_days = trial_days
        self.trial_file = Path.home() / ".school_academic_trial.json"
        self.machine_id = self._get_machine_id()
    
    def _get_machine_id(self):
        """获取机器唯一标识"""
        import platform
        import uuid
        
        # 收集机器信息
        machine_info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'node': platform.node(),
            'mac': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
        }
        
        # 生成机器ID
        info_str = ''.join(str(v) for v in machine_info.values())
        return hashlib.sha256(info_str.encode()).hexdigest()[:32].upper()
    
    def check_trial_status(self):
        """检查试用期状态"""
        today = datetime.date.today()
        
        if self.trial_file.exists():
            try:
                with open(self.trial_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                start_date = datetime.date.fromisoformat(data.get("start_date"))
                days_used = (today - start_date).days
                days_remaining = max(0, self.trial_days - days_used)
                
                return {
                    'status': 'active' if days_remaining > 0 else 'expired',
                    'start_date': start_date.isoformat(),
                    'end_date': (start_date + datetime.timedelta(days=self.trial_days)).isoformat(),
                    'days_used': days_used,
                    'days_remaining': days_remaining,
                    'total_days': self.trial_days
                }
            except:
                pass
        
        # 首次运行
        with open(self.trial_file, 'w', encoding='utf-8') as f:
            json.dump({"start_date": today.isoformat()}, f)
        
        return {
            'status': 'active',
            'start_date': today.isoformat(),
            'end_date': (today + datetime.timedelta(days=self.trial_days)).isoformat(),
            'days_used': 0,
            'days_remaining': self.trial_days,
            'total_days': self.trial_days
        }

# 内置许可证验证类
class LicenseValidator:
    def __init__(self, trial_manager):
        self.trial_manager = trial_manager
        self.license_file = Path.home() / ".school_academic_license.json"
    
    def can_run(self):
        """检查是否可以运行"""
        # 检查许可证
        if self.is_licensed():
            return True
        
        # 检查试用期
        trial_status = self.trial_manager.check_trial_status()
        return trial_status['status'] == 'active'
    
    def is_licensed(self):
        """检查是否已激活许可证"""
        if not self.license_file.exists():
            return False
        
        try:
            with open(self.license_file, 'r', encoding='utf-8') as f:
                license_data = json.load(f)
            
            # 验证许可证
            return self._validate_license(license_data)
        except:
            return False
    
    def _validate_license(self, license_data):
        """验证许可证数据"""
        try:
            machine_id = license_data.get('machine_id')
            license_key = license_data.get('license_key')
            
            # 检查机器ID
            if machine_id != self.trial_manager.machine_id:
                return False
            
            # 简单的许可证验证
            expected_key = hashlib.sha256(f"ACADEMIC_{machine_id}_2025".encode()).hexdigest()[:20].upper()
            return license_key == expected_key
        except:
            return False
    
    def activate_license(self, license_key):
        """激活许可证"""
        try:
            # 简单的许可证格式验证
            if not license_key or len(license_key) < 10:
                return False, "许可证密钥格式无效"
            
            # 生成预期的许可证密钥
            expected_key = hashlib.sha256(f"ACADEMIC_{self.trial_manager.machine_id}_2025".encode()).hexdigest()[:20].upper()
            
            if license_key.replace('-', '') == expected_key:
                # 保存许可证
                license_data = {
                    'machine_id': self.trial_manager.machine_id,
                    'license_key': expected_key,
                    'activated_date': datetime.date.today().isoformat()
                }
                
                with open(self.license_file, 'w', encoding='utf-8') as f:
                    json.dump(license_data, f, ensure_ascii=False, indent=2)
                
                return True, "许可证激活成功！"
            else:
                return False, "许可证密钥无效，请检查输入是否正确"
        
        except Exception as e:
            return False, f"激活过程中发生错误: {str(e)}"

class AcademicManagerLauncher:
    def __init__(self):
        self.trial_manager = TrialManager(trial_days=60)
        self.license_validator = LicenseValidator(self.trial_manager)
        
        self.root = tk.Tk()
        self.root.title("中学教务管理系统 v1.0")
        self.root.geometry("600x550")
        self.root.resizable(True, True)
        self.root.minsize(600, 550)
        
        # 获取exe文件所在目录
        if getattr(sys, 'frozen', False):
            self.base_dir = Path(sys.executable).parent
        else:
            self.base_dir = Path(__file__).parent
        
        # 数据库文件路径（在exe外部）
        self.db_dir = self.base_dir / "data"
        self.db_path = self.db_dir / "db.sqlite3"
        
        # 确保数据目录存在
        self.db_dir.mkdir(exist_ok=True)
        
        # 服务器进程
        self.server_process = None
        self.server_running = False
        
        self.setup_ui()
        self.update_status()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root, padx=25, pady=25)
        main_frame.pack(fill="both", expand=True)

        # 标题区域
        title_frame = tk.Frame(main_frame)
        title_frame.pack(fill="x", pady=(0, 25))

        tk.Label(title_frame, text="中学教务管理系统",
                font=("Microsoft YaHei", 18, "bold")).pack(pady=5)
        tk.Label(title_frame, text="School Academic Management System v1.0",
                font=("Arial", 11), fg="gray").pack()
        
        # 试用期状态区域
        trial_frame = tk.LabelFrame(main_frame, text="授权状态", padx=20, pady=20,
                                   font=("Microsoft YaHei", 12, "bold"))
        trial_frame.pack(fill="x", pady=(0, 20))

        self.status_label = tk.Label(trial_frame, text="检查中...",
                                    font=("Microsoft YaHei", 13, "bold"))
        self.status_label.pack(anchor="w", pady=(0, 8))

        self.detail_label = tk.Label(trial_frame, text="",
                                    font=("Microsoft YaHei", 10), fg="gray")
        self.detail_label.pack(anchor="w", pady=(0, 10))

        # 进度条（试用期进度）
        self.progress_frame = tk.Frame(trial_frame)
        self.progress_frame.pack(fill="x", pady=(0, 15))

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='determinate', height=20)
        self.progress_label = tk.Label(self.progress_frame, text="",
                                      font=("Microsoft YaHei", 9), fg="gray")

        # 按钮区域
        button_frame = tk.Frame(trial_frame)
        button_frame.pack(fill="x", pady=(5, 0))

        self.activate_btn = tk.Button(button_frame, text="激活软件",
                                     command=self.activate_software,
                                     font=("Microsoft YaHei", 11),
                                     bg="#FF9800", fg="white",
                                     width=12, height=2)
        self.activate_btn.pack(side="left", padx=(0, 15))

        self.machine_id_btn = tk.Button(button_frame, text="查看机器ID",
                                       command=self.show_machine_id,
                                       font=("Microsoft YaHei", 11),
                                       bg="#2196F3", fg="white",
                                       width=12, height=2)
        self.machine_id_btn.pack(side="left")
        
        # 系统控制区域
        control_frame = tk.LabelFrame(main_frame, text="系统控制", padx=20, pady=20,
                                     font=("Microsoft YaHei", 12, "bold"))
        control_frame.pack(fill="x", pady=(0, 20))

        # 状态显示
        status_display = tk.Frame(control_frame)
        status_display.pack(fill="x", pady=(0, 15))

        tk.Label(status_display, text="状态:", font=("Microsoft YaHei", 12)).pack(side="left")
        self.status_var = tk.StringVar(value="就绪")
        self.status_display_label = tk.Label(status_display, textvariable=self.status_var,
                                            font=("Microsoft YaHei", 12, "bold"), fg="blue")
        self.status_display_label.pack(side="left", padx=(8, 0))

        # 系统按钮
        system_buttons = tk.Frame(control_frame)
        system_buttons.pack(fill="x", pady=(5, 0))

        self.start_btn = tk.Button(system_buttons, text="启动系统",
                                  command=self.start_system,
                                  font=("Microsoft YaHei", 12, "bold"),
                                  bg="#4CAF50", fg="white",
                                  width=14, height=2)
        self.start_btn.pack(side="left", padx=(0, 15))

        self.stop_btn = tk.Button(system_buttons, text="停止系统",
                                 command=self.stop_system, state="disabled",
                                 font=("Microsoft YaHei", 12, "bold"),
                                 bg="#f44336", fg="white",
                                 width=14, height=2)
        self.stop_btn.pack(side="left", padx=(0, 15))

        self.browser_btn = tk.Button(system_buttons, text="打开网页",
                                    command=self.open_browser, state="disabled",
                                    font=("Microsoft YaHei", 12, "bold"),
                                    bg="#2196F3", fg="white",
                                    width=14, height=2)
        self.browser_btn.pack(side="left")
        
        # 信息区域
        info_frame = tk.LabelFrame(main_frame, text="系统信息", padx=20, pady=20,
                                  font=("Microsoft YaHei", 12, "bold"))
        info_frame.pack(fill="x")

        info_lines = [
            "访问地址: http://127.0.0.1:8001",
            "默认账户: admin",
            "默认密码: admin123"
        ]

        for line in info_lines:
            tk.Label(info_frame, text=line, font=("Courier New", 11),
                    fg="gray", justify="left").pack(anchor="w", pady=2)
    
    def update_status(self):
        """更新授权状态显示"""
        if self.license_validator.is_licensed():
            self.status_label.config(text="✅ 已激活", fg="green")
            self.detail_label.config(text="软件已正式激活，可无限期使用")
            self.progress_bar.pack_forget()
            self.progress_label.pack_forget()
            self.start_btn.config(state="normal")
            self.activate_btn.config(text="重新激活")
        else:
            trial_status = self.trial_manager.check_trial_status()
            
            if trial_status['status'] == 'active':
                days_remaining = trial_status['days_remaining']
                days_used = trial_status['days_used']
                total_days = trial_status['total_days']
                
                self.status_label.config(text=f"⏰ 试用期 (剩余 {days_remaining} 天)", 
                                        fg="orange")
                self.detail_label.config(text=f"试用期：{trial_status['start_date']} 至 {trial_status['end_date']}")
                
                # 显示进度条
                self.progress_bar.pack(fill="x", pady=(5, 5))
                self.progress_label.pack(anchor="w", pady=(0, 0))

                progress = (days_used / total_days) * 100
                self.progress_bar['value'] = progress
                self.progress_label.config(text=f"已使用 {days_used}/{total_days} 天 ({progress:.1f}%)")
                
                self.start_btn.config(state="normal")
                self.activate_btn.config(text="激活软件")
            else:
                self.status_label.config(text="❌ 试用期已过期", fg="red")
                self.detail_label.config(text="请购买正式版本或输入有效的许可证密钥")
                
                # 显示满进度条
                self.progress_bar.pack(fill="x", pady=(5, 0))
                self.progress_label.pack(anchor="w", pady=(2, 0))
                self.progress_bar['value'] = 100
                self.progress_label.config(text="试用期已结束")
                
                self.start_btn.config(state="disabled")
                self.activate_btn.config(text="激活软件")
    
    def activate_software(self):
        """激活软件"""
        license_key = simpledialog.askstring(
            "软件激活",
            "请输入许可证密钥:",
            parent=self.root
        )
        
        if not license_key:
            return
        
        success, message = self.license_validator.activate_license(license_key.strip().upper())
        
        if success:
            messagebox.showinfo("激活成功", message)
            self.update_status()
        else:
            messagebox.showerror("激活失败", message)
    
    def show_machine_id(self):
        """显示机器ID"""
        machine_id = self.trial_manager.machine_id
        
        dialog = tk.Toplevel(self.root)
        dialog.title("机器信息")
        dialog.geometry("500x350")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (250)
        y = (dialog.winfo_screenheight() // 2) - (175)
        dialog.geometry(f"500x350+{x}+{y}")
        
        main_frame = tk.Frame(dialog, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        
        tk.Label(main_frame, text="机器信息", 
                font=("Microsoft YaHei", 14, "bold")).pack(pady=(0, 20))
        
        info_text = f"""您的机器标识：

{machine_id}

购买许可证说明：
• 购买许可证时请提供上述机器标识
• 许可证与此机器硬件绑定，仅限本机使用
• 更换主要硬件后机器标识可能会改变

联系方式：
• 技术支持：400-123-4567
• 邮箱：<EMAIL>

请联系销售人员获取许可证。"""
        
        from tkinter import scrolledtext
        text_widget = scrolledtext.ScrolledText(main_frame, wrap="word",
                                               font=("Microsoft YaHei", 9), height=12)
        text_widget.pack(fill="both", expand=True, pady=(0, 15))
        text_widget.insert("end", info_text)
        text_widget.config(state="disabled")
        
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill="x")
        
        def copy_machine_id():
            dialog.clipboard_clear()
            dialog.clipboard_append(machine_id)
            messagebox.showinfo("成功", "机器标识已复制到剪贴板", parent=dialog)
        
        tk.Button(button_frame, text="复制机器标识",
                 command=copy_machine_id, font=("Microsoft YaHei", 9)).pack(side="left")
        tk.Button(button_frame, text="关闭",
                 command=dialog.destroy, font=("Microsoft YaHei", 9)).pack(side="right")
    
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False
    
    def start_system(self):
        """启动系统"""
        # 检查授权
        if not self.license_validator.can_run():
            messagebox.showerror("无法启动", "试用期已过期或许可证无效，请激活软件")
            return
        
        # 检查端口
        if self.check_port(8001):
            messagebox.showwarning("端口占用", "端口8001已被占用，请先停止占用该端口的程序")
            return
        
        self.status_var.set("正在启动系统...")
        self.start_btn.config(state="disabled")
        
        # 在后台线程中启动
        threading.Thread(target=self._start_django_server, daemon=True).start()
    
    def _start_django_server(self):
        """在后台线程中启动Django服务器"""
        try:
            # 设置环境变量
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')
            
            # 启动Django服务器
            if getattr(sys, 'frozen', False):
                # 打包后的exe环境
                import django
                from django.core.management import execute_from_command_line
                
                django.setup()
                
                # 在新进程中启动Django
                import multiprocessing
                
                def run_server():
                    try:
                        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8001', '--noreload'])
                    except Exception as e:
                        print(f"Django启动错误: {e}")
                
                server_process = multiprocessing.Process(target=run_server)
                server_process.start()
                self.server_process = server_process
            else:
                # 开发环境
                cmd = [sys.executable, "-c", """
import os
import django
from django.core.management import execute_from_command_line
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')
django.setup()
execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8001', '--noreload'])
"""]
                
                self.server_process = subprocess.Popen(
                    cmd,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
            
            # 等待服务器启动
            time.sleep(5)
            
            # 检查服务器是否真的启动了
            if self.check_port(8001):
                # 更新UI
                self.root.after(0, self._server_started)
            else:
                self.root.after(0, lambda: self._server_failed("服务器启动失败，端口8001未响应"))
            
        except Exception as e:
            self.root.after(0, lambda: self._server_failed(str(e)))
    
    def _server_started(self):
        """服务器启动成功后的UI更新"""
        self.server_running = True
        self.status_var.set("服务器运行中")
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.browser_btn.config(state="normal")
        
        # 自动打开浏览器
        threading.Timer(1.0, self.open_browser).start()
        
        messagebox.showinfo("启动成功", 
                           "系统已启动成功！\n\n"
                           "访问地址: http://127.0.0.1:8001\n"
                           "默认账户: admin\n"
                           "默认密码: admin123")
    
    def _server_failed(self, error_msg):
        """服务器启动失败后的UI更新"""
        self.status_var.set("启动失败")
        self.start_btn.config(state="normal")
        messagebox.showerror("启动失败", f"启动服务器时出错:\n{error_msg}")
    
    def stop_system(self):
        """停止系统"""
        try:
            if self.server_process:
                self.server_process.terminate()
                self.server_process = None
            
            self.server_running = False
            self.status_var.set("系统已停止")
            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            self.browser_btn.config(state="disabled")
            
            messagebox.showinfo("已停止", "系统已停止运行")
            
        except Exception as e:
            messagebox.showerror("错误", f"停止服务器时出错: {e}")
    
    def open_browser(self):
        """打开浏览器访问系统"""
        try:
            webbrowser.open('http://127.0.0.1:8001')
        except Exception as e:
            messagebox.showerror("错误", f"无法打开浏览器: {e}")
    
    def run(self):
        """运行应用程序"""
        # 窗口关闭时的处理
        def on_closing():
            if self.server_running:
                if messagebox.askokcancel("退出", "系统正在运行，确定要退出吗？"):
                    self.stop_system()
                    self.root.destroy()
            else:
                self.root.destroy()
        
        self.root.protocol("WM_DELETE_WINDOW", on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    app = AcademicManagerLauncher()
    app.run()
