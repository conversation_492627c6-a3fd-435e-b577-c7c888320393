#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合Django测试 - 全面检查Django环境和依赖
"""

import os
import sys
import time
import threading
import subprocess

# 导入Django工具函数
try:
    from django_utils import (
        get_resource_path, safe_django_setup, 
        check_django_dependencies, print_dependency_report,
        test_django_import_export
    )
    DJANGO_UTILS_AVAILABLE = True
except ImportError:
    DJANGO_UTILS_AVAILABLE = False
    print("⚠️ django_utils不可用，使用基础测试")

def test_environment():
    """测试运行环境"""
    print("\n=== 环境信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    
    try:
        print(f"PyInstaller临时目录: {sys._MEIPASS}")
        is_packaged = True
    except:
        print("PyInstaller临时目录: 未找到（开发环境）")
        is_packaged = False
    
    return is_packaged

def test_basic_imports():
    """测试基础导入"""
    print("\n=== 基础导入测试 ===")
    results = {}
    
    # 测试Django
    try:
        import django
        print(f"[OK] Django {django.get_version()} 导入成功")
        results['django'] = True
    except ImportError as e:
        print(f"[ERROR] Django导入失败: {e}")
        results['django'] = False
    
    # 测试import_export
    try:
        import import_export
        print(f"[OK] django-import-export {import_export.__version__} 导入成功")
        results['import_export'] = True
    except ImportError as e:
        print(f"[ERROR] django-import-export导入失败: {e}")
        results['import_export'] = False
    
    # 测试import_export.admin
    try:
        from import_export.admin import ImportExportModelAdmin
        print("[OK] import_export.admin 导入成功")
        results['import_export_admin'] = True
    except ImportError as e:
        print(f"[ERROR] import_export.admin导入失败: {e}")
        results['import_export_admin'] = False
    
    # 测试colorfield
    try:
        import colorfield
        print("[OK] django-colorfield 导入成功")
        results['colorfield'] = True
    except ImportError as e:
        print(f"[ERROR] django-colorfield导入失败: {e}")
        results['colorfield'] = False
    
    # 测试Excel库
    excel_libs = ['openpyxl', 'xlwt', 'xlrd']
    for lib in excel_libs:
        try:
            __import__(lib)
            print(f"[OK] {lib} 导入成功")
            results[lib] = True
        except ImportError as e:
            print(f"[ERROR] {lib}导入失败: {e}")
            results[lib] = False
    
    return results

def test_django_setup():
    """测试Django初始化"""
    print("\n=== Django初始化测试 ===")
    
    if DJANGO_UTILS_AVAILABLE:
        print("使用django_utils进行安全初始化...")
        success = safe_django_setup()
        if success:
            print("[OK] Django初始化成功")
            return True
        else:
            print("[ERROR] Django初始化失败")
            return False
    else:
        print("使用传统方法初始化Django...")
        try:
            # 获取项目目录
            if hasattr(sys, '_MEIPASS'):
                academic_dir = os.path.join(sys._MEIPASS, 'SchoolAcademicManager')
            else:
                academic_dir = os.path.join(os.getcwd(), 'SchoolAcademicManager')
            
            if academic_dir not in sys.path:
                sys.path.insert(0, academic_dir)
            
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')
            
            import django
            from django.conf import settings
            
            if not settings.configured:
                django.setup()
            
            print("[OK] Django初始化成功")
            return True
            
        except Exception as e:
            print(f"[ERROR] Django初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_django_apps():
    """测试Django应用"""
    print("\n=== Django应用测试 ===")
    
    try:
        from django.conf import settings
        print(f"[OK] Django配置加载成功")
        print(f"数据库引擎: {settings.DATABASES['default']['ENGINE']}")
        
        # 测试应用导入
        from academic import models, views, admin, apps
        print("[OK] academic应用导入成功")
        
        # 测试模型
        from academic.models import Department, Teacher, Student
        print("[OK] academic模型导入成功")
        
        # 测试管理器
        from academic.admin import DepartmentAdmin, TeacherAdmin
        print("[OK] academic管理器导入成功")
        
        # 测试URL配置
        from SchoolAcademicManager import urls
        print("[OK] URL配置导入成功")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Django应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """测试数据库"""
    print("\n=== 数据库测试 ===")
    
    try:
        # 获取数据库路径
        if hasattr(sys, '_MEIPASS'):
            db_path = os.path.join(sys._MEIPASS, 'SchoolAcademicManager', 'db.sqlite3')
        else:
            db_path = os.path.join(os.getcwd(), 'SchoolAcademicManager', 'db.sqlite3')
        
        if os.path.exists(db_path):
            db_size = os.path.getsize(db_path)
            print(f"[OK] 数据库文件存在: {db_path}")
            print(f"数据库大小: {db_size} 字节")
            
            if db_size > 0:
                print("[OK] 数据库文件不为空")
                return True
            else:
                print("[WARN] 数据库文件为空")
                return False
        else:
            print("[WARN] 数据库文件不存在")
            return False
            
    except Exception as e:
        print(f"[ERROR] 数据库测试失败: {e}")
        return False

def test_server_components():
    """测试服务器组件"""
    print("\n=== 服务器组件测试 ===")
    
    try:
        # 测试Django管理命令
        from django.core.management import execute_from_command_line
        print("[OK] Django管理命令导入成功")
        
        # 测试runserver命令
        from django.core.management.commands.runserver import Command as RunserverCommand
        print("[OK] runserver命令导入成功")
        
        # 测试WSGI应用
        from django.core.wsgi import get_wsgi_application
        print("[OK] WSGI应用导入成功")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 服务器组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🔍 综合Django环境测试")
    print("=" * 70)
    
    # 环境测试
    is_packaged = test_environment()
    
    # 依赖检查
    if DJANGO_UTILS_AVAILABLE:
        print("\n=== 详细依赖检查 ===")
        deps_ok = print_dependency_report()
    else:
        deps_ok = True
    
    # 运行测试
    tests = [
        ("基础导入", test_basic_imports),
        ("Django初始化", test_django_setup),
        ("Django应用", test_django_apps),
        ("数据库", test_database),
        ("服务器组件", test_server_components)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if test_name == "基础导入":
                result = test_func()
                # 对于基础导入，检查关键组件
                success = result.get('django', False) and result.get('import_export_admin', False)
            else:
                success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"[ERROR] {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示结果
    print("\n" + "=" * 70)
    print("📊 测试结果汇总:")
    print("=" * 70)
    
    for test_name, result in results:
        status = "[✅ 通过]" if result else "[❌ 失败]"
        print(f"{status} {test_name}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📈 总体结果: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！Django环境完全正常")
        print("✅ 系统可以正常启动和运行")
    else:
        print(f"\n⚠️ {total_count - success_count} 个测试失败")
        print("❌ 需要解决问题后再次测试")
    
    print("=" * 70)
    
    if is_packaged:
        input("按回车键退出...")

if __name__ == '__main__':
    main()
