#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终版打包脚本
"""

import subprocess

def create_final_spec():
    """创建最终版PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['final_academic_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('SchoolAcademicManager', 'SchoolAcademicManager'),
        ('trial_manager.py', '.'),
    ],
    hiddenimports=[
        'django',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.core.management',
        'django.core.management.commands',
        'django.core.management.commands.runserver',
        'django.core.wsgi',
        'academic',
        'academic.models',
        'academic.views',
        'academic.admin',
        'academic.apps',
        'colorfield',
        'import_export',
        'cryptography',
        'wmi',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='中学教务管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
    version='version_info.txt'
)
'''
    
    with open('final_academic.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建最终版PyInstaller规格文件")

def create_version_info():
    """创建版本信息文件"""
    version_info = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'学校管理系统开发团队'),
        StringStruct(u'FileDescription', u'中学教务管理系统'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'SchoolAcademicManager'),
        StringStruct(u'LegalCopyright', u'© 2024 学校管理系统开发团队'),
        StringStruct(u'OriginalFilename', u'中学教务管理系统.exe'),
        StringStruct(u'ProductName', u'中学教务管理系统'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ 已创建版本信息文件")

def build_final_exe():
    """构建最终版可执行文件"""
    print("\n🚀 开始构建最终版可执行文件...")
    
    try:
        result = subprocess.run([
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'final_academic.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print(f"📁 可执行文件位置: dist/中学教务管理系统.exe")
            return True
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 最终版中学教务管理系统打包工具")
    print("=" * 60)
    
    create_version_info()
    create_final_spec()
    
    if build_final_exe():
        print("\n🎉 最终版打包完成！")
        print("📋 特点:")
        print("- 修复了Django启动问题")
        print("- 包含完整的试用期功能")
        print("- 无控制台窗口")
        print("- 专业的用户界面")
        print("\n📝 测试建议:")
        print("1. 运行 dist/中学教务管理系统.exe")
        print("2. 检查试用期信息显示")
        print("3. 点击启动系统测试Django")
        print("4. 验证浏览器自动打开")
    else:
        print("\n❌ 打包失败")

if __name__ == '__main__':
    main()
