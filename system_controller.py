#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学校管理系统控制器
提供系统启动、停止、状态检查的Web API
"""

import os
import sys
import subprocess
import time
import threading
import webbrowser
import psutil
import socket
from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 系统配置
SYSTEMS = {
    'book': {
        'name': '图书管理系统',
        'port': 8000,
        'path': 'BookManager',
        'command': ['python', 'manage.py', 'runserver', '127.0.0.1:8000'],
        'process': None,
        'url': 'http://127.0.0.1:8000'
    },
    'academic': {
        'name': '中学教务管理系统',
        'port': 8001,
        'path': 'SchoolAcademicManager',
        'command': ['python', 'manage.py', 'runserver', '127.0.0.1:8001'],
        'process': None,
        'url': 'http://127.0.0.1:8001'
    },
    'finance': {
        'name': '学校财务管理系统',
        'port': 8002,
        'path': 'SchoolFinanceManager',
        'command': ['python', 'manage.py', 'runserver', '127.0.0.1:8002'],
        'process': None,
        'url': 'http://127.0.0.1:8002'
    }
}

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def get_system_status():
    """获取所有系统的运行状态"""
    status = {}
    for key, system in SYSTEMS.items():
        port_active = check_port(system['port'])
        status[key] = {
            'name': system['name'],
            'port': system['port'],
            'running': port_active,
            'url': system['url']
        }
    return status

@app.route('/')
def index():
    """主页面"""
    return render_template_string(open('system_launcher.html', 'r', encoding='utf-8').read())

@app.route('/api/status')
def api_status():
    """获取系统状态API"""
    return jsonify(get_system_status())

@app.route('/api/start/<system_id>')
def api_start(system_id):
    """启动系统API"""
    if system_id not in SYSTEMS:
        return jsonify({'success': False, 'message': '系统不存在'})
    
    system = SYSTEMS[system_id]
    
    # 检查是否已经运行
    if check_port(system['port']):
        return jsonify({'success': False, 'message': f"{system['name']}已经在运行"})
    
    try:
        # 切换到系统目录
        original_dir = os.getcwd()
        system_dir = os.path.join(original_dir, system['path'])
        
        if not os.path.exists(system_dir):
            return jsonify({'success': False, 'message': f"系统目录不存在: {system_dir}"})
        
        # 启动系统进程（隐藏控制台窗口）
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            process = subprocess.Popen(
                system['command'],
                cwd=system_dir,
                startupinfo=startupinfo,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:  # Linux/Mac系统
            process = subprocess.Popen(
                system['command'],
                cwd=system_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
        
        SYSTEMS[system_id]['process'] = process
        
        # 等待系统启动
        time.sleep(3)
        
        # 检查是否启动成功
        if check_port(system['port']):
            return jsonify({
                'success': True, 
                'message': f"{system['name']}启动成功",
                'url': system['url']
            })
        else:
            return jsonify({'success': False, 'message': f"{system['name']}启动失败"})
            
    except Exception as e:
        return jsonify({'success': False, 'message': f"启动失败: {str(e)}"})

@app.route('/api/stop/<system_id>')
def api_stop(system_id):
    """停止系统API"""
    if system_id not in SYSTEMS:
        return jsonify({'success': False, 'message': '系统不存在'})

    system = SYSTEMS[system_id]

    try:
        stopped = False

        # 方法1: 如果有进程引用，直接终止
        if SYSTEMS[system_id]['process'] and SYSTEMS[system_id]['process'].poll() is None:
            try:
                SYSTEMS[system_id]['process'].terminate()
                SYSTEMS[system_id]['process'].wait(timeout=5)
                stopped = True
                print(f"通过进程引用停止系统: {system['name']}")
            except:
                pass

        # 方法2: 通过端口查找并终止进程
        if not stopped:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 检查命令行参数是否包含对应端口
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and any(f"127.0.0.1:{system['port']}" in arg for arg in cmdline):
                        proc.terminate()
                        proc.wait(timeout=5)
                        stopped = True
                        print(f"通过命令行匹配停止系统: {system['name']}")
                        break

                    # 尝试检查网络连接（如果支持的话）
                    try:
                        connections = proc.connections()
                        if connections:
                            for conn in connections:
                                if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port == system['port']:
                                    proc.terminate()
                                    proc.wait(timeout=5)
                                    stopped = True
                                    print(f"通过端口连接停止系统: {system['name']}")
                                    break
                    except (psutil.AccessDenied, AttributeError, OSError):
                        # 如果无法获取连接信息，跳过这个方法
                        pass

                    if stopped:
                        break

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired, AttributeError):
                    continue

        # 方法3: 使用系统命令强制终止（隐藏窗口）
        if not stopped:
            try:
                if os.name == 'nt':  # Windows系统
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE

                    result = subprocess.run([
                        'powershell', '-Command',
                        f'Get-NetTCPConnection -LocalPort {system["port"]} | ForEach-Object {{ Stop-Process -Id $_.OwningProcess -Force }}'
                    ], capture_output=True, text=True, timeout=10,
                    startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    result = subprocess.run([
                        'powershell', '-Command',
                        f'Get-NetTCPConnection -LocalPort {system["port"]} | ForEach-Object {{ Stop-Process -Id $_.OwningProcess -Force }}'
                    ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    stopped = True
                    print(f"通过PowerShell停止系统: {system['name']}")
            except:
                pass

        # 清理进程引用
        SYSTEMS[system_id]['process'] = None

        # 等待端口释放
        time.sleep(3)

        # 检查是否成功停止
        if not check_port(system['port']):
            return jsonify({'success': True, 'message': f"{system['name']}已停止"})
        else:
            return jsonify({'success': False, 'message': f"{system['name']}停止失败，请尝试重新停止或手动终止进程"})

    except Exception as e:
        return jsonify({'success': False, 'message': f"停止失败: {str(e)}"})

@app.route('/api/open/<system_id>')
def api_open(system_id):
    """打开系统页面"""
    if system_id not in SYSTEMS:
        return jsonify({'success': False, 'message': '系统不存在'})
    
    system = SYSTEMS[system_id]
    
    if not check_port(system['port']):
        return jsonify({'success': False, 'message': f"{system['name']}未运行"})
    
    try:
        webbrowser.open(system['url'])
        return jsonify({'success': True, 'message': f"已打开{system['name']}"})
    except Exception as e:
        return jsonify({'success': False, 'message': f"打开失败: {str(e)}"})

def cleanup_on_exit():
    """程序退出时清理进程"""
    for system_id, system in SYSTEMS.items():
        if system['process'] and system['process'].poll() is None:
            try:
                system['process'].terminate()
                system['process'].wait(timeout=5)
            except:
                pass

if __name__ == '__main__':
    import atexit
    atexit.register(cleanup_on_exit)
    
    print("=" * 50)
    print("🏫 学校管理系统控制器")
    print("=" * 50)
    print("控制器地址: http://127.0.0.1:5000")
    print("按 Ctrl+C 停止控制器")
    print("=" * 50)
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n控制器已停止")
        cleanup_on_exit()
