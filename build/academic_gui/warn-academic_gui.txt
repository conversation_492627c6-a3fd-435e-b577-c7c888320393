
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pywatchman - imported by django.utils.autoreload (optional)
missing module named termios - imported by getpass (optional), django.utils.autoreload (optional), tty (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._distutils.util (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), http.server (delayed, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named _typeshed - imported by setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), asgiref.sync (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by code (delayed, conditional, optional), django.core.management.commands.shell (delayed, optional), rlcompleter (optional), cmd (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named typing_extensions.Buffer - imported by setuptools._vendor.typing_extensions (top-level), setuptools._vendor.wheel.wheelfile (conditional)
missing module named typing_extensions.Literal - imported by setuptools._vendor.typing_extensions (top-level), setuptools.config._validate_pyproject.formats (conditional)
missing module named typing_extensions.Self - imported by setuptools._vendor.typing_extensions (top-level), setuptools.config.expand (conditional), setuptools.config.pyprojecttoml (conditional), setuptools.config._validate_pyproject.error_reporting (conditional)
missing module named typing_extensions.deprecated - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.sysconfig (conditional), setuptools._distutils.command.bdist (conditional)
missing module named typing_extensions.TypeAlias - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.compilers.C.base (conditional), setuptools._reqs (conditional), setuptools.warnings (conditional), setuptools._path (conditional), setuptools._distutils.dist (conditional), setuptools.config.setupcfg (conditional), setuptools.config._apply_pyprojecttoml (conditional), setuptools.dist (conditional), setuptools.command.bdist_egg (conditional), setuptools.compat.py311 (conditional)
missing module named typing_extensions.Unpack - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.util (conditional), setuptools._distutils.compilers.C.base (conditional), setuptools._distutils.cmd (conditional)
missing module named typing_extensions.TypeVarTuple - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.util (conditional), setuptools._distutils.compilers.C.base (conditional), setuptools._distutils.cmd (conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named redis - imported by django.core.cache.backends.redis (delayed)
missing module named pymemcache - imported by django.core.cache.backends.memcached (delayed)
missing module named pylibmc - imported by django.core.cache.backends.memcached (delayed)
missing module named fcntl - imported by subprocess (optional), django.core.files.locks (conditional, optional)
excluded module named PIL - imported by django.core.validators (delayed, optional), django.forms.fields (delayed), django.core.files.images (delayed), django.db.models.fields.files (delayed, optional)
missing module named django.db.models.Max - imported by django.db.models (top-level), django.db.models.base (top-level)
missing module named django.db.models.IntegerField - imported by django.db.models (top-level), django.db.models.base (top-level), django.contrib.gis.db.models.functions (top-level), django.contrib.postgres.fields.array (top-level)
missing module named django.db.models.NOT_PROVIDED - imported by django.db.models (top-level), django.db.models.fields.composite (top-level), django.db.models.base (top-level), django.db.migrations.operations.fields (top-level), django.db.migrations.state (top-level), django.db.migrations.questioner (top-level), django.db.backends.mysql.schema (top-level)
missing module named django.db.models.Field - imported by django.db.models (top-level), django.db.models.query (top-level), django.db.models.fields.tuple_lookups (top-level), django.db.models.fields.composite (top-level), django.forms.models (delayed), django.contrib.admin.views.main (top-level), django.contrib.gis.db.models.fields (top-level), django.contrib.postgres.search (top-level), django.contrib.postgres.fields.array (top-level), django.contrib.postgres.fields.hstore (top-level)
missing module named django.db.models.DateTimeField - imported by django.db.models (top-level), django.db.models.query (top-level), django.contrib.postgres.functions (top-level)
missing module named django.db.models.DateField - imported by django.db.models (top-level), django.db.models.query (top-level)
missing module named django.db.models.DurationField - imported by django.db.models (top-level), django.db.backends.oracle.functions (top-level)
missing module named django.db.models.DecimalField - imported by django.db.models (top-level), django.db.backends.oracle.functions (top-level)
missing module named django.db.models.BooleanField - imported by django.db.models (delayed), django.db.models.query_utils (delayed), django.db.models.sql.where (delayed), django.contrib.gis.db.models.functions (top-level)
missing module named django.db.models.UniqueConstraint - imported by django.db.models (top-level), django.db.models.options (top-level), django.db.backends.mysql.schema (top-level), django.db.backends.sqlite3.schema (top-level)
missing module named django.db.models.AutoField - imported by django.db.models (top-level), django.db.models.options (top-level), django.db.models.query (top-level), django.forms.models (delayed), django.db.backends.oracle.operations (top-level)
missing module named psycopg_pool - imported by django.db.backends.postgresql.base (delayed, conditional, optional)
missing module named 'psycopg.types' - imported by django.db.backends.postgresql.psycopg_any (optional), django.db.backends.postgresql.operations (conditional), django.contrib.gis.db.backends.postgis.base (conditional), django.contrib.postgres.signals (conditional)
missing module named 'psycopg2.extras' - imported by django.db.backends.postgresql.psycopg_any (optional), django.db.backends.postgresql.base (conditional), django.contrib.postgres.signals (conditional)
missing module named 'psycopg2.extensions' - imported by django.db.backends.postgresql.base (conditional), django.contrib.gis.db.backends.postgis.adapter (delayed)
missing module named 'psycopg.pq' - imported by django.db.backends.postgresql.base (conditional), django.contrib.gis.db.backends.postgis.base (conditional)
missing module named 'psycopg.postgres' - imported by django.db.backends.postgresql.psycopg_any (optional)
missing module named psycopg2 - imported by django.db.backends.postgresql.base (optional), django.db.backends.postgresql.psycopg_any (optional), django.contrib.postgres.signals (conditional)
missing module named psycopg - imported by django.db.backends.postgresql.base (conditional, optional), django.db.backends.postgresql.psycopg_any (optional)
missing module named cx_Oracle - imported by django.db.backends.oracle.oracledb_any (optional)
missing module named oracledb - imported by django.db.backends.oracle.oracledb_any (optional)
missing module named 'MySQLdb.converters' - imported by django.db.backends.mysql.base (top-level)
missing module named 'MySQLdb.constants' - imported by django.db.backends.mysql.base (top-level), django.db.backends.mysql.introspection (top-level), django.contrib.gis.db.backends.mysql.introspection (top-level)
missing module named MySQLdb - imported by django.db.backends.mysql.base (optional)
missing module named tblib - imported by django.test.runner (optional)
missing module named ipdb - imported by django.test.runner (optional)
missing module named isort - imported by django.core.management.commands.shell (delayed, optional)
missing module named bpython - imported by django.core.management.commands.shell (delayed)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named selenium - imported by django.test.selenium (delayed, conditional)
missing module named yaml - imported by django.core.serializers.pyyaml (top-level)
excluded module named numpy - imported by django.contrib.gis.shortcuts (optional)
missing module named geoip2 - imported by django.contrib.gis.geoip2 (optional)
missing module named 'psycopg.adapt' - imported by django.contrib.gis.db.backends.postgis.base (conditional)
missing module named 'docutils.parsers' - imported by django.contrib.admindocs.utils (optional)
missing module named 'docutils.nodes' - imported by django.contrib.admindocs.utils (optional)
missing module named docutils - imported by django.contrib.admindocs.utils (optional)
missing module named 'selenium.webdriver' - imported by django.contrib.admin.tests (delayed)
