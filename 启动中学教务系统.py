#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中学教务管理系统启动器
解决中文路径和编码问题的最终解决方案
"""

import os
import sys
import subprocess
import time
import webbrowser
from threading import Timer

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待3秒让服务器启动
    try:
        webbrowser.open('http://127.0.0.1:8001')
        print("已自动打开浏览器访问系统")
    except:
        print("无法自动打开浏览器，请手动访问: http://127.0.0.1:8001")

def main():
    print("=" * 60)
    print("中学教务管理系统 v1.0")
    print("=" * 60)
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    django_dir = os.path.join(current_dir, "中学教务管理系统_v1.0", "SchoolAcademicManager")
    manage_py = os.path.join(django_dir, "manage.py")
    
    print(f"当前目录: {current_dir}")
    print(f"Django目录: {django_dir}")
    
    # 检查文件是否存在
    if not os.path.exists(manage_py):
        print(f"错误: 找不到manage.py文件")
        print(f"预期路径: {manage_py}")
        input("按回车键退出...")
        return
    
    # 切换到Django目录
    os.chdir(django_dir)
    print(f"工作目录已切换到: {os.getcwd()}")
    
    print("\n正在启动Django服务器...")
    print("服务器地址: http://127.0.0.1:8001")
    print("默认账户: admin")
    print("默认密码: admin123")
    print("按 Ctrl+C 停止服务器")
    print("-" * 60)
    
    # 启动浏览器定时器
    browser_timer = Timer(3.0, open_browser)
    browser_timer.start()
    
    try:
        # 启动Django服务器
        subprocess.run([
            sys.executable, "manage.py", 
            "runserver", "127.0.0.1:8001", 
            "--noreload"
        ])
    except KeyboardInterrupt:
        print("\n\n服务器已停止")
    except Exception as e:
        print(f"\n启动服务器时出错: {e}")
        print("请检查Python环境和依赖包是否正确安装")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
