#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建修复编码问题的分发包
"""

import os
import shutil
import sys

def create_distribution():
    print("创建修复版本的分发包...")
    
    # 源目录和目标目录
    source_dir = "SchoolAcademicManager"
    target_dir = "中学教务管理系统_v1.0_Fixed"
    
    # 如果目标目录存在，先删除
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    
    # 创建目标目录
    os.makedirs(target_dir)
    
    # 复制SchoolAcademicManager目录
    if os.path.exists(source_dir):
        shutil.copytree(source_dir, os.path.join(target_dir, "SchoolAcademicManager"))
        print(f"已复制 {source_dir} 到 {target_dir}")
    else:
        print(f"错误: 找不到源目录 {source_dir}")
        return False
    
    # 复制可执行文件（如果存在）
    exe_files = [
        "dist/中学教务管理系统.exe",
        "SchoolAcademicManager_Distribution/中学教务管理系统.exe"
    ]
    
    exe_copied = False
    for exe_path in exe_files:
        if os.path.exists(exe_path):
            shutil.copy2(exe_path, os.path.join(target_dir, "中学教务管理系统.exe"))
            print(f"已复制可执行文件: {exe_path}")
            exe_copied = True
            break
    
    if not exe_copied:
        print("警告: 未找到可执行文件")
    
    # 创建无中文字符的启动脚本
    create_startup_scripts(target_dir)
    
    # 创建使用说明
    create_readme(target_dir)
    
    print(f"分发包创建完成: {target_dir}")
    return True

def create_startup_scripts(target_dir):
    """创建启动脚本"""
    
    # 1. 主启动脚本
    startup_bat = os.path.join(target_dir, "start_system.bat")
    with open(startup_bat, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 >nul
echo Starting School Academic Management System...
echo.
echo Choose startup method:
echo 1. Start GUI Program (Recommended)
echo 2. Start Django Server (Debug Mode)
echo 3. Exit
echo.
set /p choice=Please enter choice (1-3): 

if "%choice%"=="1" (
    echo Starting GUI program...
    start "" "中学教务管理系统.exe"
) else if "%choice%"=="2" (
    echo Starting Django server...
    call start_django.bat
) else if "%choice%"=="3" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, starting GUI program...
    start "" "中学教务管理系统.exe"
)
""")
    
    # 2. Django启动脚本
    django_bat = os.path.join(target_dir, "start_django.bat")
    with open(django_bat, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 >nul
echo Starting Django server...
echo Current directory: %~dp0
cd /d "%~dp0SchoolAcademicManager"
if not exist manage.py (
    echo Error: manage.py not found
    echo Current directory: %CD%
    pause
    exit /b 1
)
echo Working directory: %CD%
echo Server address: http://127.0.0.1:8001
echo Default username: admin
echo Default password: admin123
echo Press Ctrl+C to stop server
echo.
python manage.py runserver 127.0.0.1:8001 --noreload
pause
""")
    
    # 3. Python启动器
    python_launcher = os.path.join(target_dir, "django_launcher.py")
    with open(python_launcher, 'w', encoding='utf-8') as f:
        f.write("""#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import time
import webbrowser
from threading import Timer

def open_browser():
    time.sleep(3)
    try:
        webbrowser.open('http://127.0.0.1:8001')
        print("Browser opened automatically")
    except:
        print("Cannot open browser automatically, please visit: http://127.0.0.1:8001")

def main():
    print("=" * 60)
    print("School Academic Management System v1.0")
    print("=" * 60)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    django_dir = os.path.join(current_dir, "SchoolAcademicManager")
    manage_py = os.path.join(django_dir, "manage.py")
    
    print(f"Current directory: {current_dir}")
    print(f"Django directory: {django_dir}")
    
    if not os.path.exists(manage_py):
        print(f"Error: manage.py not found")
        print(f"Expected path: {manage_py}")
        input("Press Enter to exit...")
        return
    
    os.chdir(django_dir)
    print(f"Working directory changed to: {os.getcwd()}")
    
    print("\\nStarting Django server...")
    print("Server address: http://127.0.0.1:8001")
    print("Default username: admin")
    print("Default password: admin123")
    print("Press Ctrl+C to stop server")
    print("-" * 60)
    
    browser_timer = Timer(3.0, open_browser)
    browser_timer.start()
    
    try:
        subprocess.run([
            sys.executable, "manage.py", 
            "runserver", "127.0.0.1:8001", 
            "--noreload"
        ])
    except KeyboardInterrupt:
        print("\\n\\nServer stopped")
    except Exception as e:
        print(f"\\nError starting server: {e}")
        print("Please check Python environment and dependencies")
    
    input("\\nPress Enter to exit...")

if __name__ == "__main__":
    main()
""")
    
    # 4. 简单的Python启动脚本
    simple_launcher = os.path.join(target_dir, "start_django.py")
    with open(simple_launcher, 'w', encoding='utf-8') as f:
        f.write("""#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import subprocess
import sys

os.chdir("SchoolAcademicManager")
subprocess.run([sys.executable, "manage.py", "runserver", "127.0.0.1:8001", "--noreload"])
""")
    
    print("启动脚本创建完成")

def create_readme(target_dir):
    """创建使用说明"""
    readme_path = os.path.join(target_dir, "README.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write("""# School Academic Management System v1.0

## Quick Start

### Method 1: GUI Program (Recommended)
Double-click: 中学教务管理系统.exe

### Method 2: Batch Script
Double-click: start_system.bat

### Method 3: Python Launcher (Most Stable)
Double-click: django_launcher.py
or run: python django_launcher.py

### Method 4: Simple Django Start
Double-click: start_django.py
or run: python start_django.py

## Access Information
- Server Address: http://127.0.0.1:8001
- Default Username: admin
- Default Password: admin123

## System Requirements
- Windows 7/8/10/11 (64-bit)
- Python 3.8+ (for script methods)
- At least 2GB RAM
- 500MB available disk space

## Troubleshooting

### If batch files show encoding errors:
- Use Python launchers instead
- Recommended: django_launcher.py

### If Django server fails to start:
- Check if Python is installed
- Ensure SchoolAcademicManager directory is complete
- Check if port 8001 is available

### If browser doesn't open automatically:
- Manually visit: http://127.0.0.1:8001

## File Structure
- 中学教务管理系统.exe - GUI executable
- start_system.bat - Main startup script
- start_django.bat - Django server startup
- django_launcher.py - Python launcher (recommended)
- start_django.py - Simple Python starter
- SchoolAcademicManager/ - Django project directory

## Support
For technical support, please contact the development team.
""")
    
    print("使用说明创建完成")

if __name__ == "__main__":
    create_distribution()
