#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接测试Django是否能正常启动
"""

import os
import sys
import subprocess
import time
import socket

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def test_django():
    """测试Django启动"""
    print("=" * 60)
    print("🧪 Django直接启动测试")
    print("=" * 60)
    
    # 检查目录
    academic_dir = "SchoolAcademicManager"
    if not os.path.exists(academic_dir):
        print(f"❌ 找不到目录: {academic_dir}")
        return False
    
    print(f"✅ 找到Django项目目录: {academic_dir}")
    
    # 检查manage.py
    manage_py = os.path.join(academic_dir, "manage.py")
    if not os.path.exists(manage_py):
        print(f"❌ 找不到manage.py: {manage_py}")
        return False
    
    print(f"✅ 找到manage.py: {manage_py}")
    
    # 检查端口
    if check_port(8001):
        print("⚠️ 端口8001已被占用，尝试停止...")
        # 尝试停止占用端口的进程
        try:
            subprocess.run(['taskkill', '/f', '/fi', 'PID eq 8001'], 
                         capture_output=True, timeout=5)
            time.sleep(2)
        except:
            pass
    
    if check_port(8001):
        print("❌ 端口8001仍被占用，无法启动")
        return False
    
    print("✅ 端口8001可用")
    
    # 尝试启动Django
    print("\n🚀 启动Django开发服务器...")
    try:
        process = subprocess.Popen([
            sys.executable, "manage.py", "runserver", "127.0.0.1:8001", "--noreload"
        ], cwd=academic_dir,
           stdout=subprocess.PIPE,
           stderr=subprocess.STDOUT,
           universal_newlines=True)
        
        print("⏳ 等待服务器启动...")
        
        # 监控输出
        start_time = time.time()
        while time.time() - start_time < 30:  # 最多等待30秒
            # 检查进程是否还在运行
            if process.poll() is not None:
                print("❌ Django进程意外退出")
                # 读取错误输出
                output, _ = process.communicate()
                print("错误输出:")
                print(output)
                return False
            
            # 检查端口是否开始监听
            if check_port(8001):
                print("✅ Django服务器启动成功！")
                print("🌐 访问地址: http://127.0.0.1:8001")
                
                # 读取一些输出
                try:
                    for _ in range(5):
                        line = process.stdout.readline()
                        if line:
                            print(f"Django: {line.strip()}")
                except:
                    pass
                
                # 停止服务器
                print("\n🛑 停止Django服务器...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except:
                    process.kill()
                
                print("✅ 测试完成，Django可以正常启动")
                return True
            
            time.sleep(1)
        
        print("❌ Django启动超时")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ 启动Django时出错: {e}")
        return False

def main():
    """主函数"""
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    
    # 检查Django
    try:
        import django
        print(f"Django版本: {django.get_version()}")
    except ImportError:
        print("❌ Django未安装")
        print("请运行: pip install django")
        return
    
    # 测试Django启动
    success = test_django()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Django测试成功！可以正常启动")
        print("💡 如果打包版本仍有问题，可能是路径或依赖问题")
    else:
        print("❌ Django测试失败")
        print("💡 请检查错误信息并解决问题后重试")
    
    print("=" * 60)
    input("按回车键退出...")

if __name__ == '__main__':
    main()
