<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务管理系统 - 账户测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .account-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            transition: transform 0.3s ease;
        }
        .account-card:hover {
            transform: translateY(-5px);
        }
        .role-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center text-white mb-4">
            <h1><i class="fas fa-chart-line me-2"></i>财务管理系统账户测试</h1>
            <p>测试所有财务系统用户账户的登录功能</p>
        </div>

        <div class="row">
            <!-- 系统管理员 -->
            <div class="col-md-6 col-lg-4">
                <div class="account-card position-relative">
                    <div class="role-badge bg-danger text-white">超级管理员</div>
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-crown me-2"></i>系统管理员</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>用户名:</strong> <code>admin</code><br>
                            <strong>密码:</strong> <code>admin123</code>
                        </div>
                        <div class="mb-3">
                            <strong>权限:</strong> 超级管理员<br>
                            <strong>职责:</strong> 系统配置、用户管理
                        </div>
                        <div class="d-grid gap-2">
                            <a href="http://127.0.0.1:8002/admin/" target="_blank" class="btn btn-dark">
                                <i class="fas fa-sign-in-alt me-1"></i>登录后台
                            </a>
                            <a href="http://127.0.0.1:8002/login/" target="_blank" class="btn btn-outline-dark">
                                <i class="fas fa-home me-1"></i>登录首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 财务主管 -->
            <div class="col-md-6 col-lg-4">
                <div class="account-card position-relative">
                    <div class="role-badge bg-primary text-white">财务主管</div>
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-user-tie me-2"></i>财务主管</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>用户名:</strong> <code>finance_manager</code><br>
                            <strong>密码:</strong> <code>finance123</code>
                        </div>
                        <div class="mb-3">
                            <strong>权限:</strong> 后台管理<br>
                            <strong>职责:</strong> 预算制定、财务审批
                        </div>
                        <div class="d-grid gap-2">
                            <a href="http://127.0.0.1:8002/admin/" target="_blank" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>登录后台
                            </a>
                            <a href="http://127.0.0.1:8002/login/" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-home me-1"></i>登录首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 会计员 -->
            <div class="col-md-6 col-lg-4">
                <div class="account-card position-relative">
                    <div class="role-badge bg-info text-white">会计员</div>
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-calculator me-2"></i>会计员</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>用户名:</strong> <code>accountant</code><br>
                            <strong>密码:</strong> <code>account123</code>
                        </div>
                        <div class="mb-3">
                            <strong>权限:</strong> 后台管理<br>
                            <strong>职责:</strong> 记账、报表编制
                        </div>
                        <div class="d-grid gap-2">
                            <a href="http://127.0.0.1:8002/admin/" target="_blank" class="btn btn-info">
                                <i class="fas fa-sign-in-alt me-1"></i>登录后台
                            </a>
                            <a href="http://127.0.0.1:8002/login/" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-home me-1"></i>登录首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 出纳员 -->
            <div class="col-md-6 col-lg-4">
                <div class="account-card position-relative">
                    <div class="role-badge bg-success text-white">出纳员</div>
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-money-bill-wave me-2"></i>出纳员</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>用户名:</strong> <code>cashier</code><br>
                            <strong>密码:</strong> <code>cashier123</code>
                        </div>
                        <div class="mb-3">
                            <strong>权限:</strong> 后台管理<br>
                            <strong>职责:</strong> 现金管理、收付业务
                        </div>
                        <div class="d-grid gap-2">
                            <a href="http://127.0.0.1:8002/admin/" target="_blank" class="btn btn-success">
                                <i class="fas fa-sign-in-alt me-1"></i>登录后台
                            </a>
                            <a href="http://127.0.0.1:8002/login/" target="_blank" class="btn btn-outline-success">
                                <i class="fas fa-home me-1"></i>登录首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审计员 -->
            <div class="col-md-6 col-lg-4">
                <div class="account-card position-relative">
                    <div class="role-badge bg-warning text-dark">审计员</div>
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-search me-2"></i>审计员</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>用户名:</strong> <code>auditor</code><br>
                            <strong>密码:</strong> <code>audit123</code>
                        </div>
                        <div class="mb-3">
                            <strong>权限:</strong> 后台管理<br>
                            <strong>职责:</strong> 审计检查、风险评估
                        </div>
                        <div class="d-grid gap-2">
                            <a href="http://127.0.0.1:8002/admin/" target="_blank" class="btn btn-warning">
                                <i class="fas fa-sign-in-alt me-1"></i>登录后台
                            </a>
                            <a href="http://127.0.0.1:8002/login/" target="_blank" class="btn btn-outline-warning">
                                <i class="fas fa-home me-1"></i>登录首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试记录 -->
            <div class="col-md-6 col-lg-4">
                <div class="account-card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>测试记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="admin-test">
                            <label class="form-check-label" for="admin-test">管理员登录测试</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="manager-test">
                            <label class="form-check-label" for="manager-test">财务主管测试</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="accountant-test">
                            <label class="form-check-label" for="accountant-test">会计员测试</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="cashier-test">
                            <label class="form-check-label" for="cashier-test">出纳员测试</label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="auditor-test">
                            <label class="form-check-label" for="auditor-test">审计员测试</label>
                        </div>
                        <button class="btn btn-secondary w-100" onclick="clearTests()">
                            <i class="fas fa-undo me-1"></i>清除记录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="account-card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-info-circle me-2"></i>使用说明</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>测试步骤</h6>
                        <ol>
                            <li>点击对应角色的"登录后台"或"登录首页"按钮</li>
                            <li>在新打开的页面中输入用户名和密码</li>
                            <li>验证是否能成功登录</li>
                            <li>测试登出功能是否正常</li>
                            <li>在测试记录中勾选已完成的测试</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>权限说明</h6>
                        <ul>
                            <li><strong>超级管理员</strong>: 拥有所有权限</li>
                            <li><strong>后台管理</strong>: 可以访问管理后台进行数据操作</li>
                            <li><strong>普通用户</strong>: 只能访问前台功能</li>
                        </ul>
                        
                        <h6 class="mt-3">注意事项</h6>
                        <ul>
                            <li>所有账户密码仅供测试使用</li>
                            <li>生产环境请及时修改默认密码</li>
                            <li>建议定期更换密码确保安全</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回导航 -->
        <div class="text-center">
            <a href="index.html" class="btn btn-light btn-lg me-2">
                <i class="fas fa-home me-2"></i>返回系统导航
            </a>
            <a href="test_logout.html" class="btn btn-outline-light btn-lg">
                <i class="fas fa-bug me-2"></i>登出功能测试
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function clearTests() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }
    </script>
</body>
</html>
