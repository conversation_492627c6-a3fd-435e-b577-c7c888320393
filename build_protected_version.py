#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
构建代码保护版本的中学教务管理系统
将所有Python源代码打包进exe文件中，只保留数据库文件在外部
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def main():
    print("=" * 60)
    print("构建代码保护版本 - 中学教务管理系统")
    print("=" * 60)
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("✗ 错误: 未安装PyInstaller")
        print("请运行: pip install pyinstaller")
        return False
    
    # 项目路径
    project_root = Path(__file__).parent
    source_dir = project_root / "SchoolAcademicManager"
    spec_file = project_root / "SchoolAcademicManager_Protected.spec"
    
    # 检查源目录
    if not source_dir.exists():
        print(f"✗ 错误: 找不到源目录 {source_dir}")
        return False
    
    print(f"✓ 源目录: {source_dir}")
    print(f"✓ Spec文件: {spec_file}")
    
    # 复制启动器文件到SchoolAcademicManager目录
    launcher_source = project_root / "fixed_protected_launcher.py"
    launcher_dest = source_dir / "fixed_protected_launcher.py"
    
    if launcher_source.exists():
        shutil.copy2(launcher_source, launcher_dest)
        print(f"✓ 已复制启动器: {launcher_dest}")
    else:
        print(f"✗ 错误: 找不到启动器文件 {launcher_source}")
        return False
    
    # 修改spec文件中的入口点
    update_spec_file(spec_file, launcher_dest)
    
    # 清理之前的构建
    build_dir = project_root / "build"
    dist_dir = project_root / "dist"
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print("✓ 已清理build目录")
    
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
        print("✓ 已清理dist目录")
    
    # 运行PyInstaller
    print("\n开始构建...")
    try:
        cmd = [sys.executable, "-m", "PyInstaller", str(spec_file), "--clean"]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功!")
        else:
            print("✗ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建过程中出错: {e}")
        return False
    
    # 创建分发包
    create_distribution_package(project_root)
    
    print("\n" + "=" * 60)
    print("构建完成!")
    print("=" * 60)
    return True

def update_spec_file(spec_file, launcher_path):
    """更新spec文件中的入口点"""
    if not spec_file.exists():
        print(f"✗ 错误: spec文件不存在 {spec_file}")
        return
    
    # 读取spec文件内容
    with open(spec_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换入口点
    old_entry = "[str(project_root / 'launcher.py')]"
    new_entry = f"['{launcher_path}']"
    
    content = content.replace(old_entry, new_entry)
    
    # 写回文件
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ 已更新spec文件入口点")

def create_distribution_package(project_root):
    """创建分发包"""
    print("\n创建分发包...")
    
    dist_dir = project_root / "dist"
    exe_file = dist_dir / "中学教务管理系统.exe"
    
    if not exe_file.exists():
        print("✗ 错误: 找不到生成的exe文件")
        return
    
    # 创建分发目录
    package_dir = project_root / "中学教务管理系统_代码保护版"
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 复制exe文件
    shutil.copy2(exe_file, package_dir / "中学教务管理系统.exe")
    print(f"✓ 已复制exe文件")
    
    # 创建data目录和示例数据库
    data_dir = package_dir / "data"
    data_dir.mkdir()
    
    # 复制数据库文件（如果存在）
    source_db = project_root / "SchoolAcademicManager" / "db.sqlite3"
    if source_db.exists():
        shutil.copy2(source_db, data_dir / "db.sqlite3")
        print(f"✓ 已复制数据库文件")
    else:
        # 创建空的数据库文件
        (data_dir / "db.sqlite3").touch()
        print(f"✓ 已创建空数据库文件")
    
    # 创建使用说明
    create_readme(package_dir)
    
    # 创建启动脚本
    create_startup_scripts(package_dir)
    
    print(f"✓ 分发包已创建: {package_dir}")

def create_readme(package_dir):
    """创建使用说明"""
    readme_content = """# 中学教务管理系统 - 代码保护版 v1.0

## 🔒 代码保护特性

本版本将所有Python源代码打包在exe文件中，用户无法查看源代码，有效保护知识产权。

## 🚀 快速开始

### 方法一：直接运行（推荐）
双击 `中学教务管理系统.exe` 启动程序

### 方法二：批处理启动
双击 `启动系统.bat`

## 📁 文件结构

```
中学教务管理系统_代码保护版/
├── 中学教务管理系统.exe     # 主程序（包含所有源代码）
├── 启动系统.bat            # 启动脚本
├── data/                   # 数据目录
│   └── db.sqlite3         # 数据库文件（唯一的外部文件）
└── README.txt             # 本说明文件
```

## 🔧 系统特点

### 代码保护
- ✅ 所有Python源代码打包在exe文件中
- ✅ 用户无法查看或修改源代码
- ✅ 有效保护知识产权
- ✅ 只有数据库文件在外部，便于数据管理

### 功能特性
- ✅ 完整的中学教务管理功能
- ✅ 60天试用期
- ✅ 图形化用户界面
- ✅ 自动打开浏览器
- ✅ 一键启动/停止

## 🌐 访问信息

- **服务器地址**: http://127.0.0.1:8001
- **默认用户名**: admin
- **默认密码**: admin123

## 💾 数据管理

- 数据库文件位于 `data/db.sqlite3`
- 可以备份此文件来保存数据
- 删除此文件将重置所有数据

## ⚠️ 注意事项

1. **首次运行**: 系统会自动创建数据库和管理员账户
2. **防火墙**: 如果防火墙询问，请允许程序访问网络
3. **端口占用**: 确保8001端口未被其他程序占用
4. **数据备份**: 定期备份 `data/db.sqlite3` 文件

## 🔧 故障排除

### 如果程序无法启动
1. 检查是否有杀毒软件阻止
2. 确保有足够的磁盘空间
3. 尝试以管理员身份运行

### 如果无法访问网页
1. 检查防火墙设置
2. 确认端口8001未被占用
3. 尝试访问 http://localhost:8001

### 如果忘记密码
1. 删除 `data/db.sqlite3` 文件
2. 重新启动程序
3. 系统将重新创建默认账户

## 📞 技术支持

如有问题，请联系技术支持团队。

---
© 2025 中学教务管理系统 - 代码保护版 v1.0
"""
    
    with open(package_dir / "README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 已创建使用说明")

def create_startup_scripts(package_dir):
    """创建启动脚本"""
    
    # 启动脚本
    startup_bat = package_dir / "启动系统.bat"
    with open(startup_bat, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 >nul
echo Starting School Academic Management System...
echo Code Protected Version
echo.
start "" "中学教务管理系统.exe"
""")
    
    print("✓ 已创建启动脚本")

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 代码保护版本构建成功!")
        print("📁 分发包位置: 中学教务管理系统_代码保护版/")
        print("🔒 所有源代码已安全打包在exe文件中")
    else:
        print("\n❌ 构建失败，请检查错误信息")
    
    input("\n按回车键退出...")
