#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SchoolAcademicManager 许可证验证器
处理注册码验证和许可证管理
"""

import os
import json
import hashlib
import base64
import re
from datetime import datetime, timedelta
from cryptography.fernet import Fernet

class AcademicLicenseValidator:
    def __init__(self, trial_manager):
        self.trial_manager = trial_manager
        self.app_name = "SchoolAcademicManager"
        self.license_file = trial_manager.license_file
        self.machine_id = trial_manager.machine_id
        self.cipher = trial_manager.cipher
        
        # 许可证类型
        self.license_types = {
            'TRIAL': {'name': '试用版', 'days': 60},
            'BASIC': {'name': '基础版', 'days': 365},
            'STANDARD': {'name': '标准版', 'days': 365 * 2},
            'PROFESSIONAL': {'name': '专业版', 'days': 365 * 3},
            'ENTERPRISE': {'name': '企业版', 'days': 365 * 5},
            'LIFETIME': {'name': '终身版', 'days': 365 * 99}
        }
    
    def validate_license_format(self, license_key):
        """验证许可证格式"""
        if not license_key:
            return False, "许可证密钥不能为空"
        
        # 移除空格和转换为大写
        license_key = license_key.strip().upper()
        
        # 检查基本格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
        pattern = r'^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$'
        if not re.match(pattern, license_key):
            return False, "许可证格式错误，应为：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX"
        
        return True, license_key
    
    def generate_license_key(self, license_type='STANDARD', custom_data=None):
        """生成许可证密钥（用于测试）"""
        if license_type not in self.license_types:
            license_type = 'STANDARD'
        
        # 生成许可证数据
        now = datetime.now()
        license_data = {
            'type': license_type,
            'machine_id': self.machine_id,
            'issued_date': now.isoformat(),
            'app_name': self.app_name,
            'version': '1.0'
        }
        
        if custom_data:
            license_data.update(custom_data)
        
        # 生成校验码
        data_string = json.dumps(license_data, sort_keys=True)
        checksum = hashlib.sha256(data_string.encode()).hexdigest()[:20]
        
        # 格式化为许可证密钥
        license_key = f"{checksum[:5]}-{checksum[5:10]}-{checksum[10:15]}-{checksum[15:20]}-{license_type[:5]}"
        
        return license_key.upper()
    
    def validate_license_key(self, license_key):
        """验证许可证密钥"""
        # 格式验证
        valid, result = self.validate_license_format(license_key)
        if not valid:
            return False, result
        
        license_key = result
        
        # 解析许可证类型
        parts = license_key.split('-')
        license_type_code = parts[4]
        
        # 查找匹配的许可证类型
        license_type = None
        for lt_code, lt_info in self.license_types.items():
            if lt_code.startswith(license_type_code) or license_type_code.startswith(lt_code[:5]):
                license_type = lt_code
                break
        
        if not license_type:
            return False, "无效的许可证类型"
        
        # 这里可以添加更复杂的验证逻辑
        # 例如：连接服务器验证、检查黑名单等
        
        # 简单验证：检查校验码
        expected_checksum = ''.join(parts[:4])
        
        # 生成预期的许可证数据
        license_data = {
            'type': license_type,
            'machine_id': self.machine_id,
            'app_name': self.app_name,
            'version': '1.0'
        }
        
        # 注意：实际应用中应该有更安全的验证方法
        return True, license_type
    
    def activate_license(self, license_key):
        """激活许可证"""
        # 验证许可证
        valid, result = self.validate_license_key(license_key)
        if not valid:
            return False, result
        
        license_type = result
        
        # 创建许可证数据
        now = datetime.now()
        license_info = self.license_types[license_type]
        
        if license_type == 'LIFETIME':
            # 终身许可证
            expire_date = now + timedelta(days=365 * 99)
        else:
            expire_date = now + timedelta(days=license_info['days'])
        
        license_data = {
            'license_key': license_key,
            'license_type': license_type,
            'license_name': license_info['name'],
            'machine_id': self.machine_id,
            'app_name': self.app_name,
            'app_version': '1.0',
            'activated_date': now.isoformat(),
            'expire_date': expire_date.isoformat(),
            'status': 'active',
            'activation_count': 1
        }
        
        # 加密并保存许可证
        encrypted_data = self.trial_manager._encrypt_data(license_data)
        try:
            with open(self.license_file, 'w') as f:
                f.write(encrypted_data)
            
            # 同时保存到注册表
            self.trial_manager._write_registry('license_data', encrypted_data)
            self.trial_manager._write_registry('license_status', 'active')
            
            return True, f"许可证激活成功！类型：{license_info['name']}"
        except Exception as e:
            return False, f"许可证保存失败：{str(e)}"
    
    def get_license_info(self):
        """获取许可证信息"""
        # 从文件读取
        license_data = None
        if os.path.exists(self.license_file):
            try:
                with open(self.license_file, 'r') as f:
                    encrypted_data = f.read()
                license_data = self.trial_manager._decrypt_data(encrypted_data)
            except Exception:
                pass
        
        # 从注册表读取（备份）
        if not license_data:
            encrypted_data = self.trial_manager._read_registry('license_data')
            if encrypted_data:
                license_data = self.trial_manager._decrypt_data(encrypted_data)
        
        # 验证许可证数据
        if license_data:
            if license_data.get('machine_id') != self.machine_id:
                return None
            if license_data.get('app_name') != self.app_name:
                return None
        
        return license_data
    
    def check_license_status(self):
        """检查许可证状态"""
        license_data = self.get_license_info()
        
        if not license_data:
            return {
                'status': 'no_license',
                'message': '未找到许可证',
                'licensed': False
            }
        
        now = datetime.now()
        try:
            expire_date = datetime.fromisoformat(license_data['expire_date'])
            activated_date = datetime.fromisoformat(license_data['activated_date'])
        except Exception:
            return {
                'status': 'invalid',
                'message': '许可证数据损坏',
                'licensed': False
            }
        
        if now > expire_date:
            days_expired = (now - expire_date).days
            return {
                'status': 'expired',
                'message': f'许可证已过期 {days_expired} 天',
                'licensed': False,
                'license_type': license_data.get('license_name', '未知'),
                'expire_date': expire_date.strftime('%Y-%m-%d'),
                'days_expired': days_expired
            }
        
        days_remaining = (expire_date - now).days + 1
        return {
            'status': 'active',
            'message': f'许可证有效 (剩余 {days_remaining} 天)',
            'licensed': True,
            'license_type': license_data.get('license_name', '未知'),
            'license_key': license_data.get('license_key', ''),
            'activated_date': activated_date.strftime('%Y-%m-%d'),
            'expire_date': expire_date.strftime('%Y-%m-%d'),
            'days_remaining': days_remaining
        }
    
    def is_licensed(self):
        """检查是否已激活许可证"""
        status = self.check_license_status()
        return status['licensed']
    
    def can_run(self):
        """检查软件是否可以运行"""
        # 首先检查许可证
        if self.is_licensed():
            return True
        
        # 然后检查试用期
        return self.trial_manager.is_trial_valid()
    
    def get_status_message(self):
        """获取状态消息"""
        license_status = self.check_license_status()
        
        if license_status['licensed']:
            return license_status['message']
        
        # 检查试用期
        trial_status = self.trial_manager.check_trial_status()
        if trial_status['status'] == 'active':
            return f"试用期剩余 {trial_status['days_remaining']} 天"
        else:
            return "试用期已过期，请激活许可证"
    
    def remove_license(self):
        """移除许可证（用于测试）"""
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
            
            # 清除注册表
            try:
                import winreg
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.trial_manager.registry_key, 0, winreg.KEY_SET_VALUE) as key:
                    try:
                        winreg.DeleteValue(key, 'license_data')
                    except:
                        pass
                    try:
                        winreg.DeleteValue(key, 'license_status')
                    except:
                        pass
            except:
                pass
            
            return True
        except Exception:
            return False

# 测试函数
def test_license_validator():
    """测试许可证验证器"""
    from academic_trial_manager import AcademicTrialManager
    
    print("=" * 60)
    print("🔑 SchoolAcademicManager 许可证验证器测试")
    print("=" * 60)
    
    # 创建试用期管理器和许可证验证器
    tm = AcademicTrialManager()
    lv = AcademicLicenseValidator(tm)
    
    print(f"机器ID: {lv.machine_id}")
    print()
    
    # 生成测试许可证
    test_license = lv.generate_license_key('STANDARD')
    print(f"生成测试许可证: {test_license}")
    
    # 验证许可证格式
    valid, result = lv.validate_license_format(test_license)
    print(f"格式验证: {valid} - {result}")
    
    # 激活许可证
    success, message = lv.activate_license(test_license)
    print(f"激活结果: {success} - {message}")
    
    if success:
        # 检查许可证状态
        status = lv.check_license_status()
        print("\n许可证状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print(f"\n可以运行: {lv.can_run()}")
        print(f"状态消息: {lv.get_status_message()}")
    
    print("=" * 60)

if __name__ == '__main__':
    test_license_validator()
