#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
独立的Django服务器启动脚本
用于在打包环境中启动Django服务器
"""

import os
import sys
import subprocess
import time

def start_django_server():
    """启动Django服务器"""
    try:
        # 查找SchoolAcademicManager目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        academic_dir = os.path.join(script_dir, "SchoolAcademicManager")
        
        if not os.path.exists(academic_dir):
            print(f"错误: 找不到SchoolAcademicManager目录: {academic_dir}")
            return False
        
        manage_py = os.path.join(academic_dir, "manage.py")
        if not os.path.exists(manage_py):
            print(f"错误: 找不到manage.py文件: {manage_py}")
            return False
        
        print(f"Django项目目录: {academic_dir}")
        print(f"manage.py路径: {manage_py}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['DJANGO_SETTINGS_MODULE'] = 'SchoolAcademicManager.settings'
        env['PYTHONPATH'] = academic_dir
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 启动Django服务器
        cmd = [sys.executable, "manage.py", "runserver", "127.0.0.1:8001", "--noreload"]
        
        print(f"启动命令: {' '.join(cmd)}")
        print(f"工作目录: {academic_dir}")
        print("正在启动Django服务器...")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=academic_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        # 监控输出
        startup_lines = []
        for i in range(30):  # 最多等待30秒
            if process.poll() is not None:
                # 进程已退出
                remaining_output, _ = process.communicate()
                if remaining_output:
                    startup_lines.extend(remaining_output.split('\n'))
                
                print("Django进程已退出")
                print("完整输出:")
                for line in startup_lines:
                    if line.strip():
                        print(f"  {line}")
                return False
            
            # 读取一行输出
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    startup_lines.append(line)
                    print(f"Django: {line}")
                    
                    # 检查启动成功的标志
                    if "Starting development server" in line or "Quit the server" in line:
                        print("Django服务器启动成功!")
                        return True
            except:
                pass
            
            time.sleep(1)
        
        print("Django服务器启动超时")
        return False
        
    except Exception as e:
        print(f"启动Django服务器时出错: {str(e)}")
        return False

if __name__ == '__main__':
    success = start_django_server()
    if success:
        print("Django服务器已启动，按Ctrl+C停止")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("正在停止Django服务器...")
    else:
        print("Django服务器启动失败")
        input("按回车键退出...")
