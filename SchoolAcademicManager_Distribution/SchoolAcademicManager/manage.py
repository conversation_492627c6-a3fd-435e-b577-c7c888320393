#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
from SchoolAcademicManager import trial  # noqa: E402  (import after shebang)
trial.check()

import os
import sys


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SchoolAcademicManager.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    # If no additional CLI args were supplied (i.e. user just double-clicked the EXE),
    # default to starting the built-in Django dev server on localhost:8000.
    if len(sys.argv) == 1:
        sys.argv += ['runserver', '0.0.0.0:8000', '--noreload']

    main()
