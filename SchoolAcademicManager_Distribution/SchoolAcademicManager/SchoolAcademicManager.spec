# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['launcher.py'],
    pathex=[],
    binaries=[('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tcl86t.dll', '.'), ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tk86t.dll', '.')],
    datas=[('templates', 'templates'), ('static', 'static'), ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\import_export\\templates', 'import_export/templates'), ('run_server.py', '.'), ('manage.py', '.'), ('SchoolAcademicManager', 'SchoolAcademicManager'), ('trial_checker.py', '.'), ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6', 'tcl/tcl8.6'), ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6', 'tcl/tk8.6')],
    hiddenimports=['django'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='SchoolAcademicManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='SchoolAcademicManager',
)
