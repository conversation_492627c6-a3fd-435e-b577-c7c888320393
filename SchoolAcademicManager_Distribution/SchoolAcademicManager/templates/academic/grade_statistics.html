{% extends 'base.html' %}

{% block title %}成绩统计 - 学校教务管理系统{% endblock %}
{% block page_title %}成绩统计{% endblock %}

{% block content %}
<!-- 当前学期信息 -->
{% if current_semester %}
<div class="alert alert-info mb-4">
    <h5><i class="fas fa-chart-bar me-2"></i>成绩统计分析</h5>
    <p class="mb-0">{{ current_semester.name }} ({{ current_semester.start_date }} 至 {{ current_semester.end_date }})</p>
</div>
{% endif %}

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ course_stats|length }}</h3>
                <p class="text-muted mb-0">开设课程</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">
                    {% if course_stats %}
                        {{ course_stats|length|floatformat:0 }}
                    {% else %}
                        0
                    {% endif %}
                </h3>
                <p class="text-muted mb-0">有成绩课程</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">
                    {% if course_stats %}
                        {% for stat in course_stats %}{{ stat.total_students|add:0 }}{% if not forloop.last %}+{% endif %}{% endfor %}
                    {% else %}
                        0
                    {% endif %}
                </h3>
                <p class="text-muted mb-0">参考学生</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">
                    {% if course_stats %}
                        {% widthratio course_stats|length 1 course_stats|length %}
                    {% else %}
                        0
                    {% endif %}%
                </h3>
                <p class="text-muted mb-0">平均通过率</p>
            </div>
        </div>
    </div>
</div>

<!-- 课程成绩统计表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>课程成绩统计
        </h5>
    </div>
    <div class="card-body">
        {% if course_stats %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>课程代码</th>
                            <th>课程名称</th>
                            <th>任课教师</th>
                            <th>参考人数</th>
                            <th>平均分</th>
                            <th>通过率</th>
                            <th>成绩分布</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stat in course_stats %}
                        <tr>
                            <td><strong>{{ stat.offering.course.code }}</strong></td>
                            <td>{{ stat.offering.course.name }}</td>
                            <td>{{ stat.offering.teacher.user.get_full_name|default:stat.offering.teacher.user.username }}</td>
                            <td>
                                <span class="badge bg-primary">{{ stat.total_students }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{% if stat.avg_score >= 85 %}success{% elif stat.avg_score >= 75 %}info{% elif stat.avg_score >= 60 %}warning{% else %}danger{% endif %}">
                                    {{ stat.avg_score }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 60px; height: 20px;">
                                        <div class="progress-bar bg-{% if stat.pass_rate >= 90 %}success{% elif stat.pass_rate >= 80 %}info{% elif stat.pass_rate >= 60 %}warning{% else %}danger{% endif %}" 
                                             style="width: {{ stat.pass_rate }}%"></div>
                                    </div>
                                    <span class="small">{{ stat.pass_rate }}%</span>
                                </div>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#gradeModal{{ forloop.counter }}">
                                    <i class="fas fa-chart-pie"></i>
                                </button>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="/admin/academic/graderecord/?course_offering={{ stat.offering.id }}" 
                                       class="btn btn-outline-primary" title="查看成绩">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/admin/academic/graderecord/add/?course_offering={{ stat.offering.id }}" 
                                       class="btn btn-outline-success" title="录入成绩">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- 成绩分布模态框 -->
                        <div class="modal fade" id="gradeModal{{ forloop.counter }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{{ stat.offering.course.name }} - 成绩分布</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row text-center">
                                            <div class="col-3">
                                                <div class="card bg-success text-white">
                                                    <div class="card-body">
                                                        <h4>A</h4>
                                                        <p class="mb-0">90-100</p>
                                                        <small>优秀</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="card bg-info text-white">
                                                    <div class="card-body">
                                                        <h4>B</h4>
                                                        <p class="mb-0">80-89</p>
                                                        <small>良好</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="card bg-warning text-white">
                                                    <div class="card-body">
                                                        <h4>C</h4>
                                                        <p class="mb-0">70-79</p>
                                                        <small>中等</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="card bg-danger text-white">
                                                    <div class="card-body">
                                                        <h4>D/F</h4>
                                                        <p class="mb-0">60-69/<60</p>
                                                        <small>及格/不及格</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <hr>
                                        
                                        <div class="row">
                                            <div class="col-6">
                                                <h6>课程信息</h6>
                                                <ul class="list-unstyled small">
                                                    <li><strong>课程:</strong> {{ stat.offering.course.name }}</li>
                                                    <li><strong>教师:</strong> {{ stat.offering.teacher.user.get_full_name|default:stat.offering.teacher.user.username }}</li>
                                                    <li><strong>学分:</strong> {{ stat.offering.course.credits }}</li>
                                                    <li><strong>学时:</strong> {{ stat.offering.course.hours }}</li>
                                                </ul>
                                            </div>
                                            <div class="col-6">
                                                <h6>统计数据</h6>
                                                <ul class="list-unstyled small">
                                                    <li><strong>参考人数:</strong> {{ stat.total_students }}</li>
                                                    <li><strong>平均分:</strong> {{ stat.avg_score }}</li>
                                                    <li><strong>通过率:</strong> {{ stat.pass_rate }}%</li>
                                                    <li><strong>最高分:</strong> -</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                        <a href="/admin/academic/graderecord/?course_offering={{ stat.offering.id }}" class="btn btn-primary">查看详细成绩</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <p class="text-muted">当前学期暂无成绩数据</p>
                <a href="/admin/academic/graderecord/add/" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>开始录入成绩
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- 快速操作 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="/admin/academic/graderecord/add/" class="btn btn-success w-100">
                            <i class="fas fa-plus me-1"></i>录入成绩
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/academic/graderecord/" class="btn btn-info w-100">
                            <i class="fas fa-list me-1"></i>管理成绩
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/academic/courseoffering/" class="btn btn-warning w-100">
                            <i class="fas fa-book me-1"></i>开课管理
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button onclick="window.print()" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-print me-1"></i>打印报表
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .modal, .card-header {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    table {
        font-size: 12px;
    }
}
</style>
{% endblock %}
