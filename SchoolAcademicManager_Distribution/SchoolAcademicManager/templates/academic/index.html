{% extends 'base.html' %}

{% block title %}首页 - 中学教务管理系统{% endblock %}
{% block page_title %}中学教务管理系统首页{% endblock %}

{% block content %}
<div class="row">
    <!-- 统计卡片 -->
    <div class="col-md-2 mb-4">
        <div class="card card-stats h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <span class="h2 font-weight-bold mb-0">{{ stats.total_students }}</span>
                        <p class="text-muted mb-0">在校学生</p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-graduate stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-4">
        <div class="card card-stats h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <span class="h2 font-weight-bold mb-0">{{ stats.total_teachers }}</span>
                        <p class="text-muted mb-0">在职教师</p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chalkboard-teacher stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-4">
        <div class="card card-stats h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <span class="h2 font-weight-bold mb-0">{{ stats.total_courses }}</span>
                        <p class="text-muted mb-0">开设课程</p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-book stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-4">
        <div class="card card-stats h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <span class="h2 font-weight-bold mb-0">{{ stats.total_classes }}</span>
                        <p class="text-muted mb-0">教学班级</p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-2 mb-4">
        <div class="card card-stats h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <span class="h2 font-weight-bold mb-0">{{ stats.total_departments }}</span>
                        <p class="text-muted mb-0">教研组</p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 当前学期信息 -->
{% if current_semester %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h5><i class="fas fa-calendar-alt me-2"></i>当前学期</h5>
            <p class="mb-0">{{ current_semester.name }} ({{ current_semester.start_date }} 至 {{ current_semester.end_date }})</p>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <!-- 最近成绩记录 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>最近成绩记录
                </h5>
            </div>
            <div class="card-body">
                {% if recent_grades %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>学生</th>
                                    <th>课程</th>
                                    <th>考试类型</th>
                                    <th>分数</th>
                                    <th>录入时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for grade in recent_grades %}
                                <tr>
                                    <td>{{ grade.student.name }}</td>
                                    <td>{{ grade.course_offering.course.name }}</td>
                                    <td>{{ grade.exam_type.name }}</td>
                                    <td>
                                        <span class="badge bg-{% if grade.is_passed %}success{% else %}danger{% endif %}">
                                            {{ grade.score|default:"-" }}
                                        </span>
                                    </td>
                                    <td>{{ grade.recorded_at|date:"m-d H:i" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">暂无成绩记录</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 最近选课记录 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>最近选课记录
                </h5>
            </div>
            <div class="card-body">
                {% if recent_enrollments %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>学生</th>
                                    <th>课程</th>
                                    <th>状态</th>
                                    <th>选课时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in recent_enrollments %}
                                <tr>
                                    <td>{{ enrollment.student.name }}</td>
                                    <td>{{ enrollment.course_offering.course.name }}</td>
                                    <td>
                                        <span class="badge bg-{% if enrollment.status == 'enrolled' %}success{% elif enrollment.status == 'dropped' %}danger{% else %}warning{% endif %}">
                                            {{ enrollment.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ enrollment.enrollment_date|date:"m-d H:i" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">暂无选课记录</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'academic:student_list' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-graduate me-2"></i>学生管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'academic:teacher_list' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-chalkboard-teacher me-2"></i>教师管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'academic:course_list' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-book me-2"></i>课程管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'academic:schedule' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-calendar-alt me-2"></i>查看课表
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/admin/academic/graderecord/add/" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-plus me-2"></i>录入成绩
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/academic/enrollment/add/" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-user-plus me-2"></i>学生选课
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/academic/attendance/add/" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-check me-2"></i>考勤记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'academic:grade_statistics' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-chart-bar me-2"></i>成绩统计
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
