{% extends 'base.html' %}

{% block title %}学生管理 - 学校教务管理系统{% endblock %}
{% block page_title %}学生管理{% endblock %}

{% block content %}
<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">搜索</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="学号、姓名或身份证号">
            </div>
            <div class="col-md-2">
                <label for="grade" class="form-label">年级</label>
                <select class="form-select" id="grade" name="grade">
                    <option value="">全部年级</option>
                    {% for grade in grades %}
                        <option value="{{ grade.id }}" {% if grade.id|stringformat:"s" == selected_grade %}selected{% endif %}>
                            {{ grade.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="class_type" class="form-label">班级类型</label>
                <select class="form-select" id="class_type" name="class_type">
                    <option value="">全部类型</option>
                    {% for class_type in class_types %}
                        <option value="{{ class_type.id }}" {% if class_type.id|stringformat:"s" == selected_class_type %}selected{% endif %}>
                            {{ class_type.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="class" class="form-label">班级</label>
                <select class="form-select" id="class" name="class">
                    <option value="">全部班级</option>
                    {% for class in classes %}
                        <option value="{{ class.id }}" {% if class.id|stringformat:"s" == selected_class %}selected{% endif %}>
                            {{ class.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{% url 'academic:student_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                    <a href="/admin/academic/student/add/" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加学生
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 学生列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-graduate me-2"></i>学生列表
            <span class="badge bg-primary ms-2">共 {{ page_obj.paginator.count }} 人</span>
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>学号</th>
                            <th>姓名</th>
                            <th>性别</th>
                            <th>年级</th>
                            <th>班级类型</th>
                            <th>班级</th>
                            <th>住校情况</th>
                            <th>学籍状态</th>
                            <th>入学日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in page_obj %}
                        <tr>
                            <td>
                                <a href="{% url 'academic:student_detail' student.student_id %}" 
                                   class="text-decoration-none">
                                    {{ student.student_id }}
                                </a>
                            </td>
                            <td>{{ student.name }}</td>
                            <td>{{ student.get_gender_display }}</td>
                            <td>{{ student.class_info.grade.name }}</td>
                            <td>{{ student.class_info.class_type.name }}</td>
                            <td>{{ student.class_info.name }}</td>
                            <td>
                                <span class="badge bg-{% if student.is_boarding %}info{% else %}secondary{% endif %}">
                                    {% if student.is_boarding %}住校{% else %}走读{% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{% if student.status == 'enrolled' %}success{% elif student.status == 'graduated' %}info{% elif student.status == 'suspended' %}warning{% else %}danger{% endif %}">
                                    {{ student.get_status_display }}
                                </span>
                            </td>
                            <td>{{ student.enrollment_date }}</td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'academic:student_detail' student.student_id %}" 
                                       class="btn btn-outline-primary" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/admin/academic/student/{{ student.id }}/change/" 
                                       class="btn btn-outline-secondary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="学生列表分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_grade %}&grade={{ selected_grade }}{% endif %}{% if selected_major %}&major={{ selected_major }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_grade %}&grade={{ selected_grade }}{% endif %}{% if selected_major %}&major={{ selected_major }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_grade %}&grade={{ selected_grade }}{% endif %}{% if selected_major %}&major={{ selected_major }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_grade %}&grade={{ selected_grade }}{% endif %}{% if selected_major %}&major={{ selected_major }}{% endif %}{% if selected_class %}&class={{ selected_class }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                <p class="text-muted">没有找到符合条件的学生</p>
                <a href="/admin/academic/student/add/" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>添加第一个学生
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
