<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}中学教务管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card-stats {
            border-left: 4px solid #007bff;
        }
        .stats-icon {
            font-size: 2rem;
            color: #007bff;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar position-fixed d-none d-md-block">
        <div class="p-3">
            <h5 class="text-white">中学教务管理系统</h5>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'index' %}active{% endif %}" 
                   href="{% url 'academic:index' %}">
                    <i class="fas fa-home me-2"></i>首页
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'student' in request.resolver_match.url_name %}active{% endif %}" 
                   href="{% url 'academic:student_list' %}">
                    <i class="fas fa-user-graduate me-2"></i>学生管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'teacher' in request.resolver_match.url_name %}active{% endif %}" 
                   href="{% url 'academic:teacher_list' %}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>教师管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'course' in request.resolver_match.url_name %}active{% endif %}" 
                   href="{% url 'academic:course_list' %}">
                    <i class="fas fa-book me-2"></i>课程管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'schedule' %}active{% endif %}" 
                   href="{% url 'academic:schedule' %}">
                    <i class="fas fa-calendar-alt me-2"></i>课程表
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'statistics' in request.resolver_match.url_name %}active{% endif %}" 
                   href="{% url 'academic:grade_statistics' %}">
                    <i class="fas fa-chart-bar me-2"></i>成绩统计
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/">
                    <i class="fas fa-cog me-2"></i>系统管理
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
            <div class="container-fluid">
                <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <span class="navbar-brand mb-0 h1">{% block page_title %}中学教务管理系统{% endblock %}</span>
                
                <div class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/admin/password_change/">修改密码</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="post" action="{% url 'academic:logout' %}" style="display: inline;">
                                        {% csrf_token %}
                                        <button type="submit" class="dropdown-item" style="border: none; background: none; width: 100%; text-align: left;">
                                            退出登录
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    {% else %}
                        <a class="nav-link" href="{% url 'login' %}">登录</a>
                    {% endif %}
                </div>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-fluid p-4">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
