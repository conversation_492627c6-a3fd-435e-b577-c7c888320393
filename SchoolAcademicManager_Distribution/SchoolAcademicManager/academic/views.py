from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count, Avg
from django.http import JsonResponse, HttpResponse
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from datetime import date, datetime, timedelta
import json

from .models import (
    Department, Teacher, Grade, ClassType, Class, Student, Course,
    Semester, CourseOffering, Enrollment, ExamType, GradeRecord,
    Attendance, Classroom, Schedule
)


def index(request):
    """首页视图"""
    # 统计数据
    stats = {
        'total_students': Student.objects.filter(is_active=True).count(),
        'total_teachers': Teacher.objects.filter(is_active=True).count(),
        'total_courses': Course.objects.filter(is_active=True).count(),
        'total_classes': Class.objects.filter(is_active=True).count(),
        'total_departments': Department.objects.filter(is_active=True).count(),
    }
    
    # 当前学期
    current_semester = Semester.objects.filter(is_current=True).first()
    
    # 最近的成绩记录
    recent_grades = GradeRecord.objects.select_related(
        'student', 'course_offering__course', 'exam_type'
    ).order_by('-recorded_at')[:10]
    
    # 最近的选课记录
    recent_enrollments = Enrollment.objects.select_related(
        'student', 'course_offering__course'
    ).order_by('-enrollment_date')[:10]
    
    context = {
        'stats': stats,
        'current_semester': current_semester,
        'recent_grades': recent_grades,
        'recent_enrollments': recent_enrollments,
    }
    
    return render(request, 'academic/index.html', context)


def student_list(request):
    """学生列表视图"""
    students = Student.objects.select_related('class_info__grade', 'class_info__class_type').filter(is_active=True)

    # 搜索功能
    search = request.GET.get('search')
    if search:
        students = students.filter(
            Q(student_id__icontains=search) |
            Q(name__icontains=search) |
            Q(id_card__icontains=search) |
            Q(student_number__icontains=search)
        )

    # 筛选功能
    grade_id = request.GET.get('grade')
    if grade_id:
        students = students.filter(class_info__grade_id=grade_id)

    class_type_id = request.GET.get('class_type')
    if class_type_id:
        students = students.filter(class_info__class_type_id=class_type_id)

    class_id = request.GET.get('class')
    if class_id:
        students = students.filter(class_info_id=class_id)

    # 分页
    paginator = Paginator(students, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取筛选选项
    grades = Grade.objects.filter(is_active=True)
    class_types = ClassType.objects.filter(is_active=True)
    classes = Class.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'grades': grades,
        'class_types': class_types,
        'classes': classes,
        'search': search,
        'selected_grade': grade_id,
        'selected_class_type': class_type_id,
        'selected_class': class_id,
    }

    return render(request, 'academic/student_list.html', context)


def student_detail(request, student_id):
    """学生详情视图"""
    student = get_object_or_404(Student, student_id=student_id)
    
    # 获取学生的选课记录
    enrollments = Enrollment.objects.filter(
        student=student, is_active=True
    ).select_related('course_offering__course', 'course_offering__semester')
    
    # 获取学生的成绩记录
    grades = GradeRecord.objects.filter(
        student=student
    ).select_related('course_offering__course', 'exam_type').order_by('-exam_date')
    
    # 获取学生的考勤记录
    attendances = Attendance.objects.filter(
        student=student
    ).select_related('course_offering__course').order_by('-date')[:20]
    
    context = {
        'student': student,
        'enrollments': enrollments,
        'grades': grades,
        'attendances': attendances,
    }
    
    return render(request, 'academic/student_detail.html', context)


def teacher_list(request):
    """教师列表视图"""
    teachers = Teacher.objects.select_related('user', 'department').filter(is_active=True)
    
    # 搜索功能
    search = request.GET.get('search')
    if search:
        teachers = teachers.filter(
            Q(employee_id__icontains=search) |
            Q(user__first_name__icontains=search) |
            Q(user__last_name__icontains=search) |
            Q(user__username__icontains=search)
        )
    
    # 筛选功能
    department_id = request.GET.get('department')
    if department_id:
        teachers = teachers.filter(department_id=department_id)
    
    title = request.GET.get('title')
    if title:
        teachers = teachers.filter(title=title)
    
    # 分页
    paginator = Paginator(teachers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 获取筛选选项
    departments = Department.objects.filter(is_active=True)
    title_choices = Teacher.TITLE_CHOICES
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'title_choices': title_choices,
        'search': search,
        'selected_department': department_id,
        'selected_title': title,
    }
    
    return render(request, 'academic/teacher_list.html', context)


def course_list(request):
    """课程列表视图"""
    courses = Course.objects.select_related('department').filter(is_active=True)
    
    # 搜索功能
    search = request.GET.get('search')
    if search:
        courses = courses.filter(
            Q(code__icontains=search) |
            Q(name__icontains=search)
        )
    
    # 筛选功能
    department_id = request.GET.get('department')
    if department_id:
        courses = courses.filter(department_id=department_id)
    
    course_type = request.GET.get('course_type')
    if course_type:
        courses = courses.filter(course_type=course_type)
    
    # 分页
    paginator = Paginator(courses, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 获取筛选选项
    departments = Department.objects.filter(is_active=True)
    course_type_choices = Course.COURSE_TYPE_CHOICES
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'course_type_choices': course_type_choices,
        'search': search,
        'selected_department': department_id,
        'selected_course_type': course_type,
    }
    
    return render(request, 'academic/course_list.html', context)


def schedule_view(request):
    """课程表视图"""
    current_semester = Semester.objects.filter(is_current=True).first()

    if not current_semester:
        messages.warning(request, _('请先设置当前学期'))
        return redirect('academic:index')

    # 获取当前学期的课程表
    schedules = Schedule.objects.filter(
        course_offering__semester=current_semester
    ).select_related(
        'course_offering__course',
        'course_offering__teacher__user',
        'classroom'
    ).order_by('weekday', 'time_slot')

    # 构建课程表数据结构 - 使用嵌套列表而不是字典
    schedule_matrix = []
    weekdays = Schedule.WEEKDAY_CHOICES
    time_slots = Schedule.TIME_SLOT_CHOICES

    # 创建一个二维数组来存储课程表数据
    for time_slot_value, time_slot_display in time_slots:
        row = []
        for weekday_value, weekday_display in weekdays:
            # 查找对应时间段的课程
            schedule_item = schedules.filter(
                weekday=weekday_value,
                time_slot=time_slot_value
            ).first()

            if schedule_item:
                row.append({
                    'course': schedule_item.course_offering.course,
                    'teacher': schedule_item.course_offering.teacher,
                    'classroom': schedule_item.classroom,
                    'weeks': schedule_item.weeks,
                })
            else:
                row.append(None)

        schedule_matrix.append({
            'time_slot': time_slot_display,
            'courses': row
        })

    context = {
        'current_semester': current_semester,
        'schedule_matrix': schedule_matrix,
        'weekdays': weekdays,
        'time_slots': time_slots,
    }

    return render(request, 'academic/schedule.html', context)


def grade_statistics(request):
    """成绩统计视图"""
    current_semester = Semester.objects.filter(is_current=True).first()
    
    if not current_semester:
        messages.warning(request, _('请先设置当前学期'))
        return redirect('academic:index')
    
    # 按课程统计成绩
    course_stats = []
    course_offerings = CourseOffering.objects.filter(
        semester=current_semester, is_active=True
    ).select_related('course', 'teacher__user')
    
    for offering in course_offerings:
        grades = GradeRecord.objects.filter(course_offering=offering)
        if grades.exists():
            avg_score = grades.aggregate(avg=Avg('score'))['avg']
            pass_count = grades.filter(is_passed=True).count()
            total_count = grades.count()
            pass_rate = (pass_count / total_count * 100) if total_count > 0 else 0
            
            course_stats.append({
                'offering': offering,
                'avg_score': round(avg_score, 2) if avg_score else 0,
                'pass_rate': round(pass_rate, 2),
                'total_students': total_count,
            })
    
    context = {
        'current_semester': current_semester,
        'course_stats': course_stats,
    }
    
    return render(request, 'academic/grade_statistics.html', context)
