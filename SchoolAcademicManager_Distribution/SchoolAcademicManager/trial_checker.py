import os
import json
import datetime
from tkinter import Tk, messagebox

TRIAL_FILE = os.path.expanduser("~/.school_academic_trial.json")
TRIAL_DAYS = 60


def check_trial():
    today = datetime.date.today()

    if os.path.exists(TRIAL_FILE):
        with open(TRIAL_FILE, 'r') as f:
            data = json.load(f)
            start_date = datetime.date.fromisoformat(data.get("start_date"))
            days_used = (today - start_date).days
            if days_used > TRIAL_DAYS:
                show_expired()
                exit()
    else:
        # 首次运行，记录试用开始时间
        with open(TRIAL_FILE, 'w') as f:
            json.dump({"start_date": today.isoformat()}, f)


def show_expired():
    root = Tk()
    root.withdraw()
    messagebox.showerror("试用已到期", f"软件试用期为{TRIAL_DAYS}天，已到期。请购买正式版本。")
    root.destroy()
