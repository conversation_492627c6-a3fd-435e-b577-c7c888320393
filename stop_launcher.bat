@echo off
chcp 65001 >nul 2>&1
title 停止学校管理系统启动器

echo ========================================
echo    🛑 停止学校管理系统启动器
echo ========================================
echo.

echo 正在停止启动器和所有系统...
echo.

REM 方法1: 停止Python进程（包括启动器和Django服务器）
echo 🔄 停止Python进程...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1

REM 方法2: 通过端口停止进程
echo 🔄 通过端口停止进程...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000 ^| findstr LISTENING') do (
    echo 停止端口5000的进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000 ^| findstr LISTENING') do (
    echo 停止端口8000的进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001 ^| findstr LISTENING') do (
    echo 停止端口8001的进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8002 ^| findstr LISTENING') do (
    echo 停止端口8002的进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

REM 方法3: 使用PowerShell强制停止
echo 🔄 使用PowerShell强制停止...
powershell -Command "Get-NetTCPConnection -LocalPort 5000 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }" >nul 2>&1
powershell -Command "Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }" >nul 2>&1
powershell -Command "Get-NetTCPConnection -LocalPort 8001 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }" >nul 2>&1
powershell -Command "Get-NetTCPConnection -LocalPort 8002 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }" >nul 2>&1

REM 等待进程完全停止
timeout /t 3 >nul

echo.
echo ✅ 启动器和所有系统已停止
echo.

REM 检查端口状态
echo 🔍 检查端口状态：
netstat -an | findstr "5000 8000 8001 8002" | findstr "LISTENING" >nul
if errorlevel 1 (
    echo ✅ 所有端口已释放
) else (
    echo ⚠️ 部分端口可能仍被占用
    echo 当前监听的端口：
    netstat -an | findstr "5000 8000 8001 8002" | findstr "LISTENING"
)

echo.
echo ========================================
echo 按任意键退出...
pause >nul
