<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学校管理系统启动器</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .main-container {
            padding: 2rem 0;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .system-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            height: 100%;
            border: none;
        }
        
        .system-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
        
        .card-header {
            padding: 2rem;
            text-align: center;
            color: white;
            border: none;
            position: relative;
        }
        
        .book-manager {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .finance-manager {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .academic-manager {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .card-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .card-subtitle {
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #28a745;
            margin-right: 0.5rem;
        }
        
        .btn-system {
            width: 100%;
            padding: 0.8rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 10px;
            border: none;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }
        
        .btn-start {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-stop {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }
        
        .btn-open {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
        }
        
        .btn-system:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .status-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            backdrop-filter: blur(10px);
            font-weight: 600;
        }
        
        .status-running {
            background: rgba(40, 167, 69, 0.8);
            color: white;
        }
        
        .status-stopped {
            background: rgba(220, 53, 69, 0.8);
            color: white;
        }
        
        .status-loading {
            background: rgba(255, 193, 7, 0.8);
            color: white;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 3rem;
            opacity: 0.8;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .main-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Toast 通知容器 -->
    <div class="toast-container"></div>
    
    <div class="container main-container">
        <!-- 页面标题 -->
        <div class="header">
            <h1><i class="fas fa-rocket me-3"></i>学校管理系统启动器</h1>
            <p>智能启动和管理您的学校系统</p>
            <div class="mt-3">
                <button class="btn btn-light btn-sm me-2" onclick="refreshStatus()">
                    <i class="fas fa-sync-alt me-1"></i>刷新状态
                </button>
                <button class="btn btn-success btn-sm me-2" onclick="startAllSystems()">
                    <i class="fas fa-play me-1"></i>启动所有系统
                </button>
                <button class="btn btn-danger btn-sm me-2" onclick="stopAllSystems()">
                    <i class="fas fa-stop me-1"></i>停止所有系统
                </button>
                <button class="btn btn-warning btn-sm me-2" onclick="forceStopAll()">
                    <i class="fas fa-skull me-1"></i>强制停止
                </button>
                <button class="btn btn-info btn-sm" onclick="showDiagnosis()">
                    <i class="fas fa-stethoscope me-1"></i>系统诊断
                </button>
            </div>
        </div>
        
        <!-- 系统卡片 -->
        <div class="row g-4" id="systemCards">
            <!-- 图书管理系统 -->
            <div class="col-lg-4 col-md-6">
                <div class="card system-card" id="card-book">
                    <div class="card-header book-manager position-relative">
                        <div class="status-badge status-loading" id="status-book">
                            <i class="fas fa-spinner fa-spin me-1"></i>检查中
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3 class="card-title">图书管理系统</h3>
                        <p class="card-subtitle">BookManager - 端口 8000</p>
                    </div>
                    <div class="card-body">
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i>图书信息管理</li>
                            <li><i class="fas fa-check"></i>借阅归还管理</li>
                            <li><i class="fas fa-check"></i>读者信息管理</li>
                            <li><i class="fas fa-check"></i>图书统计分析</li>
                        </ul>
                        <div class="d-grid gap-2" id="controls-book">
                            <button class="btn btn-start btn-system" onclick="startSystem('book')">
                                <i class="fas fa-play me-2"></i>启动系统
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 财务管理系统 -->
            <div class="col-lg-4 col-md-6">
                <div class="card system-card" id="card-finance">
                    <div class="card-header finance-manager position-relative">
                        <div class="status-badge status-loading" id="status-finance">
                            <i class="fas fa-spinner fa-spin me-1"></i>检查中
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="card-title">学校财务管理系统</h3>
                        <p class="card-subtitle">SchoolFinanceManager - 端口 8002</p>
                    </div>
                    <div class="card-body">
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i>收入支出管理</li>
                            <li><i class="fas fa-check"></i>预算制定执行</li>
                            <li><i class="fas fa-check"></i>财务报表生成</li>
                            <li><i class="fas fa-check"></i>资金流向分析</li>
                        </ul>
                        <div class="d-grid gap-2" id="controls-finance">
                            <button class="btn btn-start btn-system" onclick="startSystem('finance')">
                                <i class="fas fa-play me-2"></i>启动系统
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 教务管理系统 -->
            <div class="col-lg-4 col-md-6">
                <div class="card system-card" id="card-academic">
                    <div class="card-header academic-manager position-relative">
                        <div class="status-badge status-loading" id="status-academic">
                            <i class="fas fa-spinner fa-spin me-1"></i>检查中
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="card-title">中学教务管理系统</h3>
                        <p class="card-subtitle">SchoolAcademicManager - 端口 8001</p>
                    </div>
                    <div class="card-body">
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i>学生信息管理</li>
                            <li><i class="fas fa-check"></i>教师班级管理</li>
                            <li><i class="fas fa-check"></i>课程安排管理</li>
                            <li><i class="fas fa-check"></i>成绩考勤管理</li>
                        </ul>
                        <div class="d-grid gap-2" id="controls-academic">
                            <button class="btn btn-start btn-system" onclick="startSystem('academic')">
                                <i class="fas fa-play me-2"></i>启动系统
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">
            <p><i class="fas fa-code me-2"></i>学校管理系统启动器 - 智能管理您的系统</p>
            <p class="small">默认管理员账户: admin / admin123</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 系统状态管理
        let systemStatus = {};
        
        // 显示通知
        function showToast(message, type = 'info') {
            const toastContainer = document.querySelector('.toast-container');
            const toastId = 'toast-' + Date.now();
            
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type} border-0" role="alert" id="${toastId}">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = new bootstrap.Toast(document.getElementById(toastId));
            toastElement.show();
            
            // 自动移除
            setTimeout(() => {
                const element = document.getElementById(toastId);
                if (element) element.remove();
            }, 5000);
        }
        
        // 更新系统状态显示
        function updateSystemDisplay(systemId, status) {
            const statusBadge = document.getElementById(`status-${systemId}`);
            const controlsDiv = document.getElementById(`controls-${systemId}`);
            
            if (status.running) {
                statusBadge.className = 'status-badge status-running';
                statusBadge.innerHTML = '<i class="fas fa-circle me-1"></i>运行中';
                
                controlsDiv.innerHTML = `
                    <button class="btn btn-open btn-system" onclick="openSystem('${systemId}')">
                        <i class="fas fa-external-link-alt me-2"></i>打开系统
                    </button>
                    <button class="btn btn-stop btn-system" onclick="stopSystem('${systemId}')">
                        <i class="fas fa-stop me-2"></i>停止系统
                    </button>
                `;
            } else {
                statusBadge.className = 'status-badge status-stopped';
                statusBadge.innerHTML = '<i class="fas fa-circle me-1"></i>已停止';
                
                controlsDiv.innerHTML = `
                    <button class="btn btn-start btn-system" onclick="startSystem('${systemId}')">
                        <i class="fas fa-play me-2"></i>启动系统
                    </button>
                `;
            }
        }
        
        // 刷新系统状态
        async function refreshStatus() {
            try {
                const response = await fetch('/api/status');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                systemStatus = data;

                for (const [systemId, status] of Object.entries(data)) {
                    updateSystemDisplay(systemId, status);
                }

                // 隐藏连接错误提示
                hideConnectionError();

            } catch (error) {
                console.error('获取状态失败:', error);
                showConnectionError();

                // 如果是网络错误，显示特殊提示
                if (error.message.includes('fetch') || error.message.includes('Failed to fetch')) {
                    showToast('启动器控制器未运行，请先启动控制器', 'warning');
                } else {
                    showToast('获取系统状态失败: ' + error.message, 'danger');
                }
            }
        }

        // 显示连接错误
        function showConnectionError() {
            let errorDiv = document.getElementById('connectionError');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'connectionError';
                errorDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
                errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                errorDiv.innerHTML = `
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>连接错误</h6>
                    <p class="mb-2">无法连接到启动器控制器，可能的原因：</p>
                    <ul class="mb-2">
                        <li>控制器未启动</li>
                        <li>端口5000被占用</li>
                        <li>防火墙阻止连接</li>
                    </ul>
                    <div class="d-grid gap-2">
                        <button class="btn btn-warning btn-sm" onclick="restartController()">
                            <i class="fas fa-redo me-1"></i>重启控制器
                        </button>
                        <button class="btn btn-info btn-sm" onclick="openManualStart()">
                            <i class="fas fa-external-link-alt me-1"></i>手动启动
                        </button>
                    </div>
                    <button type="button" class="btn-close" onclick="hideConnectionError()"></button>
                `;
                document.body.appendChild(errorDiv);
            }
            errorDiv.style.display = 'block';
        }

        // 隐藏连接错误
        function hideConnectionError() {
            const errorDiv = document.getElementById('connectionError');
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }

        // 重启控制器
        function restartController() {
            showToast('请手动运行 start_launcher_silent.bat 重启控制器', 'info');
        }

        // 打开手动启动说明
        function openManualStart() {
            const helpModal = `
                <div class="modal fade" id="helpModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-question-circle me-2"></i>手动启动说明
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <h6>启动控制器：</h6>
                                <ol>
                                    <li>双击运行 <code>start_launcher_silent.bat</code></li>
                                    <li>或运行 <code>启动器选择.bat</code> 选择启动方式</li>
                                    <li>等待浏览器自动打开</li>
                                </ol>

                                <h6 class="mt-3">直接启动系统：</h6>
                                <ul>
                                    <li><strong>图书管理：</strong> 进入 BookManager 文件夹，运行 <code>python manage.py runserver 127.0.0.1:8000</code></li>
                                    <li><strong>教务管理：</strong> 进入 SchoolAcademicManager 文件夹，运行 <code>python manage.py runserver 127.0.0.1:8001</code></li>
                                    <li><strong>财务管理：</strong> 进入 SchoolFinanceManager 文件夹，运行 <code>python manage.py runserver 127.0.0.1:8002</code></li>
                                </ul>

                                <h6 class="mt-3">访问地址：</h6>
                                <ul>
                                    <li><a href="http://127.0.0.1:8000" target="_blank">图书管理系统</a></li>
                                    <li><a href="http://127.0.0.1:8001" target="_blank">教务管理系统</a></li>
                                    <li><a href="http://127.0.0.1:8002" target="_blank">财务管理系统</a></li>
                                </ul>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="refreshStatus(); bootstrap.Modal.getInstance(document.getElementById('helpModal')).hide();">
                                    <i class="fas fa-sync-alt me-1"></i>重新检测
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除旧的模态框
            const oldModal = document.getElementById('helpModal');
            if (oldModal) oldModal.remove();

            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', helpModal);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('helpModal'));
            modal.show();
        }
        
        // 启动系统
        async function startSystem(systemId) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>启动中...';
            button.disabled = true;

            try {
                const response = await fetch(`/api/start/${systemId}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(refreshStatus, 2000);
                } else {
                    showToast(data.message, 'danger');
                }
            } catch (error) {
                if (error.message.includes('fetch') || error.message.includes('Failed to fetch')) {
                    showToast('启动失败: 控制器未运行，请先启动控制器', 'danger');
                    showConnectionError();
                } else {
                    showToast('启动失败: ' + error.message, 'danger');
                }
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }
        
        // 停止系统
        async function stopSystem(systemId) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>停止中...';
            button.disabled = true;
            
            try {
                const response = await fetch(`/api/stop/${systemId}`);
                const data = await response.json();
                
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(refreshStatus, 2000);
                } else {
                    showToast(data.message, 'danger');
                }
            } catch (error) {
                showToast('停止失败: ' + error.message, 'danger');
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }
        
        // 打开系统
        async function openSystem(systemId) {
            try {
                const response = await fetch(`/api/open/${systemId}`);
                const data = await response.json();
                
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    showToast(data.message, 'warning');
                }
            } catch (error) {
                showToast('打开失败: ' + error.message, 'danger');
            }
        }
        
        // 启动所有系统
        async function startAllSystems() {
            const allSystems = ['book', 'finance', 'academic'];
            const systemNames = {
                'book': '图书管理系统',
                'finance': '学校财务管理系统',
                'academic': '中学教务管理系统'
            };

            showToast(`正在启动所有系统 (${allSystems.length}个)...`, 'info');

            let startedCount = 0;
            let skippedCount = 0;
            let failedCount = 0;

            for (const systemId of allSystems) {
                const systemName = systemNames[systemId];

                if (!systemStatus[systemId]?.running) {
                    showToast(`正在启动 ${systemName}...`, 'info');

                    try {
                        // 模拟点击启动按钮
                        const response = await fetch(`/api/start/${systemId}`);
                        const data = await response.json();

                        if (data.success) {
                            startedCount++;
                            showToast(`✅ ${systemName} 启动成功`, 'success');
                        } else {
                            failedCount++;
                            showToast(`❌ ${systemName} 启动失败: ${data.message}`, 'danger');
                        }
                    } catch (error) {
                        failedCount++;
                        showToast(`❌ ${systemName} 启动异常: ${error.message}`, 'danger');
                    }

                    await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
                } else {
                    skippedCount++;
                    showToast(`⏭️ ${systemName} 已在运行，跳过`, 'warning');
                }
            }

            // 显示总结
            const summary = `批量启动完成: 启动${startedCount}个，跳过${skippedCount}个，失败${failedCount}个`;
            showToast(summary, failedCount > 0 ? 'warning' : 'success');

            // 刷新状态
            setTimeout(refreshStatus, 2000);
        }
        
        // 停止所有系统
        async function stopAllSystems() {
            const allSystems = ['book', 'finance', 'academic'];
            const systemNames = {
                'book': '图书管理系统',
                'finance': '学校财务管理系统',
                'academic': '中学教务管理系统'
            };

            showToast(`正在停止所有系统 (${allSystems.length}个)...`, 'info');

            let stoppedCount = 0;
            let skippedCount = 0;
            let failedCount = 0;

            for (const systemId of allSystems) {
                const systemName = systemNames[systemId];

                if (systemStatus[systemId]?.running) {
                    showToast(`正在停止 ${systemName}...`, 'info');

                    try {
                        // 模拟点击停止按钮
                        const response = await fetch(`/api/stop/${systemId}`);
                        const data = await response.json();

                        if (data.success) {
                            stoppedCount++;
                            showToast(`✅ ${systemName} 停止成功`, 'success');
                        } else {
                            failedCount++;
                            showToast(`❌ ${systemName} 停止失败: ${data.message}`, 'danger');
                        }
                    } catch (error) {
                        failedCount++;
                        showToast(`❌ ${systemName} 停止异常: ${error.message}`, 'danger');
                    }

                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
                } else {
                    skippedCount++;
                    showToast(`⏭️ ${systemName} 未运行，跳过`, 'warning');
                }
            }

            // 显示总结
            const summary = `批量停止完成: 停止${stoppedCount}个，跳过${skippedCount}个，失败${failedCount}个`;
            showToast(summary, failedCount > 0 ? 'warning' : 'success');

            // 如果有失败的，建议使用强制停止
            if (failedCount > 0) {
                showToast('如果停止失败，请使用"强制停止"功能', 'info');
            }

            // 刷新状态
            setTimeout(refreshStatus, 2000);
        }

        // 强制停止所有系统
        async function forceStopAll() {
            if (!confirm('确定要强制停止所有系统吗？这将终止所有Python进程。')) {
                return;
            }

            showToast('正在强制停止所有系统...', 'warning');

            try {
                // 调用Python脚本强制停止
                const response = await fetch('/api/force_stop', {
                    method: 'POST'
                });

                if (response.ok) {
                    showToast('强制停止完成！', 'success');
                    setTimeout(refreshStatus, 3000);
                } else {
                    // 如果API不可用，提示用户手动执行
                    showToast('请运行 "强制停止所有系统.bat" 或 "python kill_systems.py"', 'warning');
                }
            } catch (error) {
                // 如果启动器也被停止了，这是正常的
                showToast('强制停止执行中...请稍后刷新页面检查状态', 'info');
                setTimeout(() => {
                    window.location.reload();
                }, 5000);
            }
        }

        // 显示系统诊断
        async function showDiagnosis() {
            try {
                const response = await fetch('/api/diagnose');
                const data = await response.json();

                let diagnosisHtml = `
                    <div class="modal fade" id="diagnosisModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="fas fa-stethoscope me-2"></i>系统诊断报告
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <h6><i class="fas fa-server me-2"></i>系统状态</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>系统</th>
                                                    <th>端口</th>
                                                    <th>状态</th>
                                                    <th>进程ID</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                `;

                for (const [id, system] of Object.entries(data.systems)) {
                    const statusBadge = system.running ?
                        '<span class="badge bg-success">运行中</span>' :
                        '<span class="badge bg-danger">已停止</span>';
                    const pid = system.process ? system.process.pid : '-';

                    diagnosisHtml += `
                        <tr>
                            <td>${system.name}</td>
                            <td>${system.port}</td>
                            <td>${statusBadge}</td>
                            <td>${pid}</td>
                        </tr>
                    `;
                }

                diagnosisHtml += `
                                            </tbody>
                                        </table>
                                    </div>

                                    <h6 class="mt-4"><i class="fas fa-cogs me-2"></i>Python进程</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>进程ID</th>
                                                    <th>进程名</th>
                                                    <th>命令行</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                `;

                for (const proc of data.processes) {
                    diagnosisHtml += `
                        <tr>
                            <td>${proc.pid}</td>
                            <td>${proc.name}</td>
                            <td><small>${proc.cmdline}</small></td>
                        </tr>
                    `;
                }

                diagnosisHtml += `
                                            </tbody>
                                        </table>
                                    </div>

                                    <h6 class="mt-4"><i class="fas fa-lightbulb me-2"></i>建议</h6>
                                    <ul class="list-group list-group-flush">
                `;

                for (const recommendation of data.recommendations) {
                    diagnosisHtml += `<li class="list-group-item">${recommendation}</li>`;
                }

                diagnosisHtml += `
                                    </ul>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                    <button type="button" class="btn btn-primary" onclick="refreshStatus(); bootstrap.Modal.getInstance(document.getElementById('diagnosisModal')).hide();">
                                        <i class="fas fa-sync-alt me-1"></i>刷新状态
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 移除旧的模态框
                const oldModal = document.getElementById('diagnosisModal');
                if (oldModal) oldModal.remove();

                // 添加新的模态框
                document.body.insertAdjacentHTML('beforeend', diagnosisHtml);

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('diagnosisModal'));
                modal.show();

            } catch (error) {
                showToast('获取诊断信息失败: ' + error.message, 'danger');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            
            // 定期刷新状态
            setInterval(refreshStatus, 10000); // 每10秒刷新一次
        });
    </script>
</body>
</html>
